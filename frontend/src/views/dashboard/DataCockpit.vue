<template>
  <div class="data-cockpit">
    <div class="page-header">
      <h2>数据驾驶舱</h2>
      <div class="header-actions">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="refreshAllData"
        />
        <el-button @click="refreshAllData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="primary" @click="showCustomAnalysisDialog">
          <el-icon><DataAnalysis /></el-icon>
          自定义分析
        </el-button>
      </div>
    </div>

    <!-- 核心指标概览 -->
    <div class="metrics-overview">
      <el-row :gutter="20">
        <el-col :span="6" v-for="metric in coreMetrics" :key="metric.key">
          <el-card class="metric-card" :class="metric.trend">
            <div class="metric-content">
              <div class="metric-icon" :style="{ background: metric.color }">
                <el-icon><component :is="metric.icon" /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ formatMetricValue(metric.value, metric.type) }}</div>
                <div class="metric-label">{{ metric.label }}</div>
                <div class="metric-change" :class="metric.trend">
                  <el-icon><component :is="metric.trend === 'up' ? 'TrendCharts' : 'Bottom'" /></el-icon>
                  {{ metric.change }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表分析区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>营收趋势分析</span>
              <el-select v-model="revenueChartType" size="small" @change="updateRevenueChart">
                <el-option label="日趋势" value="daily" />
                <el-option label="周趋势" value="weekly" />
                <el-option label="月趋势" value="monthly" />
              </el-select>
            </div>
          </template>
          <div ref="revenueChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>客户分析</span>
          </template>
          <div ref="customerChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-section">
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <span>服务项目分析</span>
          </template>
          <div ref="serviceChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <span>门店业绩对比</span>
          </template>
          <div ref="storeChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <span>员工业绩排行</span>
          </template>
          <div ref="employeeChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时数据监控 -->
    <el-card class="realtime-card">
      <template #header>
        <div class="card-header">
          <span>实时数据监控</span>
          <el-switch
            v-model="realtimeEnabled"
            active-text="实时更新"
            @change="toggleRealtime"
          />
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6" v-for="item in realtimeData" :key="item.key">
          <div class="realtime-item">
            <div class="realtime-label">{{ item.label }}</div>
            <div class="realtime-value">{{ item.value }}</div>
            <div class="realtime-time">{{ formatTime(item.updateTime) }}</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>详细数据</span>
          <el-button-group>
            <el-button 
              v-for="tab in dataTabs" 
              :key="tab.key"
              :type="activeDataTab === tab.key ? 'primary' : ''"
              size="small"
              @click="switchDataTab(tab.key)"
            >
              {{ tab.label }}
            </el-button>
          </el-button-group>
        </div>
      </template>
      
      <component 
        :is="currentDataComponent" 
        :data="currentTableData" 
        :loading="tableLoading"
        @refresh="loadTableData"
      />
    </el-card>

    <!-- 自定义分析对话框 -->
    <el-dialog v-model="customAnalysisVisible" title="自定义数据分析" width="800px">
      <el-form :model="analysisForm" label-width="120px">
        <el-form-item label="分析维度">
          <el-checkbox-group v-model="analysisForm.dimensions">
            <el-checkbox label="time">时间</el-checkbox>
            <el-checkbox label="store">门店</el-checkbox>
            <el-checkbox label="service">服务项目</el-checkbox>
            <el-checkbox label="employee">员工</el-checkbox>
            <el-checkbox label="customer">客户</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="分析指标">
          <el-checkbox-group v-model="analysisForm.metrics">
            <el-checkbox label="revenue">营收</el-checkbox>
            <el-checkbox label="order_count">订单数</el-checkbox>
            <el-checkbox label="customer_count">客户数</el-checkbox>
            <el-checkbox label="avg_order_value">客单价</el-checkbox>
            <el-checkbox label="profit">利润</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="analysisForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        
        <el-form-item label="图表类型">
          <el-select v-model="analysisForm.chartType">
            <el-option label="柱状图" value="bar" />
            <el-option label="折线图" value="line" />
            <el-option label="饼图" value="pie" />
            <el-option label="散点图" value="scatter" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="customAnalysisVisible = false">取消</el-button>
        <el-button type="primary" @click="generateCustomAnalysis" :loading="analyzing">
          生成分析
        </el-button>
      </template>
    </el-dialog>

    <!-- 自定义分析结果对话框 -->
    <el-dialog v-model="analysisResultVisible" title="分析结果" width="1000px">
      <div v-if="analysisResult">
        <div ref="customChartRef" class="custom-chart-container"></div>
        
        <el-divider />
        
        <div class="analysis-summary">
          <h4>分析摘要</h4>
          <p>{{ analysisResult.summary }}</p>
          
          <h4>关键发现</h4>
          <ul>
            <li v-for="insight in analysisResult.insights" :key="insight">{{ insight }}</li>
          </ul>
          
          <h4>建议</h4>
          <ul>
            <li v-for="recommendation in analysisResult.recommendations" :key="recommendation">
              {{ recommendation }}
            </li>
          </ul>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Refresh, DataAnalysis, TrendCharts, Bottom,
  Money, User, ShoppingCart, Timer
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import biApi from '@/api/businessIntelligence'

// 数据状态
const loading = ref(false)
const tableLoading = ref(false)
const analyzing = ref(false)
const realtimeEnabled = ref(false)
const customAnalysisVisible = ref(false)
const analysisResultVisible = ref(false)

// 日期范围
const dateRange = ref([])

// 图表类型
const revenueChartType = ref('daily')

// 核心指标
const coreMetrics = ref([
  {
    key: 'revenue',
    label: '总营收',
    value: 0,
    type: 'currency',
    change: 0,
    trend: 'up',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    icon: 'Money'
  },
  {
    key: 'orders',
    label: '订单数',
    value: 0,
    type: 'number',
    change: 0,
    trend: 'up',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    icon: 'ShoppingCart'
  },
  {
    key: 'customers',
    label: '客户数',
    value: 0,
    type: 'number',
    change: 0,
    trend: 'up',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    icon: 'User'
  },
  {
    key: 'avg_order_value',
    label: '客单价',
    value: 0,
    type: 'currency',
    change: 0,
    trend: 'down',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    icon: 'Timer'
  }
])

// 实时数据
const realtimeData = ref([
  { key: 'today_revenue', label: '今日营收', value: '¥0', updateTime: new Date() },
  { key: 'today_orders', label: '今日订单', value: '0', updateTime: new Date() },
  { key: 'online_customers', label: '在线客户', value: '0', updateTime: new Date() },
  { key: 'pending_orders', label: '待处理订单', value: '0', updateTime: new Date() }
])

// 数据表格
const activeDataTab = ref('orders')
const dataTabs = [
  { key: 'orders', label: '订单数据' },
  { key: 'customers', label: '客户数据' },
  { key: 'services', label: '服务数据' },
  { key: 'employees', label: '员工数据' }
]

const currentTableData = ref([])
const currentDataComponent = ref('OrderDataTable')

// 自定义分析
const analysisForm = reactive({
  dimensions: ['time'],
  metrics: ['revenue'],
  dateRange: [],
  chartType: 'line'
})

const analysisResult = ref(null)

// 图表引用
const revenueChartRef = ref(null)
const customerChartRef = ref(null)
const serviceChartRef = ref(null)
const storeChartRef = ref(null)
const employeeChartRef = ref(null)
const customChartRef = ref(null)

// 图表实例
let revenueChart = null
let customerChart = null
let serviceChart = null
let storeChart = null
let employeeChart = null
let customChart = null

// 实时更新定时器
let realtimeTimer = null

// 初始化日期范围
const initDateRange = () => {
  const end = new Date()
  const start = new Date()
  start.setDate(start.getDate() - 30)
  
  dateRange.value = [
    start.toISOString().split('T')[0],
    end.toISOString().split('T')[0]
  ]
}

// 格式化指标值
const formatMetricValue = (value, type) => {
  if (type === 'currency') {
    return `¥${value.toLocaleString()}`
  } else if (type === 'number') {
    return value.toLocaleString()
  } else if (type === 'percentage') {
    return `${value}%`
  }
  return value
}

// 格式化时间
const formatTime = (time) => {
  return new Date(time).toLocaleTimeString()
}

// 加载核心指标
const loadCoreMetrics = async () => {
  try {
    const [startDate, endDate] = dateRange.value
    console.log('加载核心指标，日期范围:', startDate, endDate)

    if (!startDate || !endDate) {
      console.warn('日期范围为空，跳过加载核心指标')
      return
    }

    const requestData = {
      start_date: startDate,
      end_date: endDate,
      indicators: ['revenue', 'order_count', 'customer_count', 'avg_order_value']
    }
    console.log('发送BI API请求:', requestData)

    const res = await biApi.getBusinessIndicators(requestData)
    console.log('BI API响应:', res)

    if (res && res.data) {
      const data = res.data
      coreMetrics.value[0].value = data.revenue || 0
      coreMetrics.value[1].value = data.order_count || 0
      coreMetrics.value[2].value = data.customer_count || 0
      coreMetrics.value[3].value = data.avg_order_value || 0

      // 计算变化趋势（简化处理）
      coreMetrics.value.forEach(metric => {
        metric.change = Math.random() * 20 - 10 // 模拟变化
        metric.trend = metric.change > 0 ? 'up' : 'down'
      })
      console.log('核心指标更新完成:', coreMetrics.value)
    } else {
      console.warn('BI API响应数据为空')
    }
  } catch (error) {
    console.error('加载核心指标失败', error)
    ElMessage.error('获取业务指标数据失败')
  }
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  
  // 营收趋势图
  if (revenueChartRef.value) {
    revenueChart = echarts.init(revenueChartRef.value)
    updateRevenueChart()
  }
  
  // 客户分析图
  if (customerChartRef.value) {
    customerChart = echarts.init(customerChartRef.value)
    updateCustomerChart()
  }
  
  // 服务项目图
  if (serviceChartRef.value) {
    serviceChart = echarts.init(serviceChartRef.value)
    updateServiceChart()
  }
  
  // 门店对比图
  if (storeChartRef.value) {
    storeChart = echarts.init(storeChartRef.value)
    updateStoreChart()
  }
  
  // 员工排行图
  if (employeeChartRef.value) {
    employeeChart = echarts.init(employeeChartRef.value)
    updateEmployeeChart()
  }
}

// 更新营收趋势图
const updateRevenueChart = async () => {
  if (!revenueChart) return
  
  try {
    const [startDate, endDate] = dateRange.value
    const res = await biApi.getMultidimensionalAnalysis({
      dimensions: ['time'],
      metrics: ['revenue'],
      start_date: startDate,
      end_date: endDate,
      time_granularity: revenueChartType.value
    })
    
    const data = res.data?.data || []
    const xData = data.map(item => item.time)
    const yData = data.map(item => item.revenue)
    
    const option = {
      title: {
        text: '营收趋势',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: xData
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '¥{value}'
        }
      },
      series: [{
        data: yData,
        type: 'line',
        smooth: true,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(102, 126, 234, 0.8)' },
            { offset: 1, color: 'rgba(102, 126, 234, 0.1)' }
          ])
        }
      }]
    }
    
    revenueChart.setOption(option)
  } catch (error) {
    console.error('更新营收趋势图失败', error)
  }
}

// 更新客户分析图
const updateCustomerChart = async () => {
  if (!customerChart) return
  
  // 模拟客户分析数据
  const option = {
    title: {
      text: '客户类型分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    series: [{
      type: 'pie',
      radius: '60%',
      data: [
        { value: 335, name: 'VIP客户' },
        { value: 310, name: '普通客户' },
        { value: 234, name: '新客户' },
        { value: 135, name: '流失客户' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  
  customerChart.setOption(option)
}

// 更新服务项目图
const updateServiceChart = async () => {
  if (!serviceChart) return
  
  // 模拟服务项目数据
  const option = {
    title: {
      text: '热门服务项目',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: ['按摩', '推拿', '足疗', '刮痧', '拔罐']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: [120, 200, 150, 80, 70],
      type: 'bar',
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#83bff6' },
          { offset: 0.5, color: '#188df0' },
          { offset: 1, color: '#188df0' }
        ])
      }
    }]
  }
  
  serviceChart.setOption(option)
}

// 更新门店对比图
const updateStoreChart = async () => {
  if (!storeChart) return
  
  // 模拟门店数据
  const option = {
    title: {
      text: '门店业绩对比',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['总店', '分店A', '分店B', '分店C']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: [50000, 35000, 28000, 22000],
      type: 'bar',
      itemStyle: {
        color: function(params) {
          const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666']
          return colors[params.dataIndex]
        }
      }
    }]
  }
  
  storeChart.setOption(option)
}

// 更新员工排行图
const updateEmployeeChart = async () => {
  if (!employeeChart) return
  
  // 模拟员工数据
  const option = {
    title: {
      text: '员工业绩排行',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: ['张师傅', '李师傅', '王师傅', '赵师傅', '刘师傅']
    },
    series: [{
      data: [18000, 15000, 12000, 10000, 8000],
      type: 'bar',
      itemStyle: {
        color: '#73c0de'
      }
    }]
  }
  
  employeeChart.setOption(option)
}

// 切换数据表格
const switchDataTab = (tab) => {
  activeDataTab.value = tab
  loadTableData()
}

// 加载表格数据
const loadTableData = async () => {
  tableLoading.value = true
  try {
    // 根据不同的tab加载不同的数据
    // 这里简化处理
    currentTableData.value = []
  } catch (error) {
    console.error('加载表格数据失败', error)
  } finally {
    tableLoading.value = false
  }
}

// 显示自定义分析对话框
const showCustomAnalysisDialog = () => {
  analysisForm.dateRange = [...dateRange.value]
  customAnalysisVisible.value = true
}

// 生成自定义分析
const generateCustomAnalysis = async () => {
  analyzing.value = true
  try {
    const res = await biApi.getMultidimensionalAnalysis({
      dimensions: analysisForm.dimensions,
      metrics: analysisForm.metrics,
      start_date: analysisForm.dateRange[0],
      end_date: analysisForm.dateRange[1]
    })
    
    analysisResult.value = {
      data: res.data,
      summary: '基于选定维度和指标的分析结果显示...',
      insights: [
        '营收在选定时间段内呈现上升趋势',
        '客户数量稳定增长',
        '服务质量得到提升'
      ],
      recommendations: [
        '继续保持当前的服务质量',
        '加强客户关系维护',
        '优化服务流程'
      ]
    }
    
    customAnalysisVisible.value = false
    analysisResultVisible.value = true
    
    // 渲染自定义图表
    await nextTick()
    if (customChartRef.value) {
      customChart = echarts.init(customChartRef.value)
      // 根据分析结果渲染图表
      renderCustomChart()
    }
  } catch (error) {
    console.error('生成自定义分析失败', error)
    ElMessage.error('生成自定义分析失败')
  } finally {
    analyzing.value = false
  }
}

// 渲染自定义图表
const renderCustomChart = () => {
  if (!customChart || !analysisResult.value) return
  
  // 简化的图表渲染
  const option = {
    title: {
      text: '自定义分析结果',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['数据1', '数据2', '数据3', '数据4', '数据5']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: [120, 200, 150, 80, 70],
      type: analysisForm.chartType === 'bar' ? 'bar' : 'line'
    }]
  }
  
  customChart.setOption(option)
}

// 切换实时更新
const toggleRealtime = (enabled) => {
  if (enabled) {
    startRealtimeUpdate()
  } else {
    stopRealtimeUpdate()
  }
}

// 开始实时更新
const startRealtimeUpdate = () => {
  realtimeTimer = setInterval(() => {
    updateRealtimeData()
  }, 30000) // 30秒更新一次
}

// 停止实时更新
const stopRealtimeUpdate = () => {
  if (realtimeTimer) {
    clearInterval(realtimeTimer)
    realtimeTimer = null
  }
}

// 更新实时数据
const updateRealtimeData = () => {
  realtimeData.value.forEach(item => {
    // 模拟数据更新
    item.updateTime = new Date()
    switch (item.key) {
      case 'today_revenue':
        item.value = `¥${(Math.random() * 10000).toFixed(0)}`
        break
      case 'today_orders':
        item.value = Math.floor(Math.random() * 100).toString()
        break
      case 'online_customers':
        item.value = Math.floor(Math.random() * 50).toString()
        break
      case 'pending_orders':
        item.value = Math.floor(Math.random() * 20).toString()
        break
    }
  })
}

// 刷新所有数据
const refreshAllData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadCoreMetrics(),
      updateRevenueChart(),
      updateCustomerChart(),
      updateServiceChart(),
      updateStoreChart(),
      updateEmployeeChart()
    ])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    console.error('刷新数据失败', error)
    ElMessage.error('刷新数据失败')
  } finally {
    loading.value = false
  }
}

// 窗口大小变化时重新调整图表
const handleResize = () => {
  if (revenueChart) revenueChart.resize()
  if (customerChart) customerChart.resize()
  if (serviceChart) serviceChart.resize()
  if (storeChart) storeChart.resize()
  if (employeeChart) employeeChart.resize()
  if (customChart) customChart.resize()
}

// 生命周期
onMounted(() => {
  initDateRange()
  initCharts()
  loadCoreMetrics()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  stopRealtimeUpdate()
  window.removeEventListener('resize', handleResize)
  
  // 销毁图表实例
  if (revenueChart) revenueChart.dispose()
  if (customerChart) customerChart.dispose()
  if (serviceChart) serviceChart.dispose()
  if (storeChart) storeChart.dispose()
  if (employeeChart) employeeChart.dispose()
  if (customChart) customChart.dispose()
})
</script>

<style scoped>
.data-cockpit {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.metrics-overview {
  margin-bottom: 20px;
}

.metric-card {
  height: 120px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.metric-change {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.metric-change.up {
  color: #67c23a;
}

.metric-change.down {
  color: #f56c6c;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
}

.custom-chart-container {
  height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.realtime-card {
  margin-bottom: 20px;
}

.realtime-item {
  text-align: center;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.realtime-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.realtime-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.realtime-time {
  font-size: 12px;
  color: #c0c4cc;
}

.table-card {
  margin-bottom: 20px;
}

.analysis-summary {
  padding: 20px;
}

.analysis-summary h4 {
  margin-bottom: 10px;
  color: #303133;
}

.analysis-summary p,
.analysis-summary li {
  color: #606266;
  line-height: 1.6;
}

.analysis-summary ul {
  padding-left: 20px;
}
</style>
