<template>
  <div class="data-dashboard-container">
    <el-card class="dashboard-header">
      <div class="dashboard-title">
        <h1>数据驾驶舱</h1>
        <div class="dashboard-filters">
          <el-select v-model="storeId" placeholder="选择门店" clearable style="width: 150px; margin-right: 10px;">
            <el-option v-for="store in stores" :key="store.id" :label="store.name" :value="store.id" />
          </el-select>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            style="width: 300px; margin-right: 10px;"
          />
          <el-button type="primary" @click="refreshData">查询</el-button>
          <el-button @click="exportData">导出数据</el-button>
          <el-button type="success" @click="showShareModal">
            <el-icon><Share /></el-icon> 分享
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 核心指标卡片 -->
    <el-row :gutter="20" class="dashboard-row">
      <el-col :span="6" v-for="(indicator, index) in businessIndicators" :key="index">
        <el-card shadow="hover" class="indicator-card">
          <div class="indicator-header">
            <div class="indicator-title">{{ indicator.title }}</div>
            <el-icon class="indicator-icon" :style="{ color: indicator.iconColor }">
              <component :is="indicator.icon"></component>
            </el-icon>
          </div>
          <div class="indicator-value">{{ indicator.value }}</div>
          <div class="indicator-footer">
            <span :class="['indicator-change', indicator.trend]">
              <el-icon v-if="indicator.trend === 'up'"><CaretTop /></el-icon>
              <el-icon v-else><CaretBottom /></el-icon>
              {{ indicator.changePercentage }}% {{ indicator.trend === 'up' ? '增长' : '下降' }}
            </span>
            <span class="indicator-period">环比</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 多维度分析 -->
    <el-card class="dashboard-card">
      <template #header>
        <div class="card-header">
          <h2>多维度分析</h2>
          <div class="dimension-selector">
            <span>维度:</span>
            <el-select 
              v-model="selectedDimensions" 
              multiple 
              collapse-tags 
              placeholder="选择维度"
              style="width: 220px; margin-right: 15px;"
            >
              <el-option
                v-for="item in dimensionOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <span>指标:</span>
            <el-select 
              v-model="selectedMetrics" 
              multiple 
              collapse-tags 
              placeholder="选择指标"
              style="width: 220px; margin-right: 15px;"
            >
              <el-option
                v-for="item in metricOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-button type="primary" @click="analyzeData">分析</el-button>
            <el-button v-if="drillHistory.length > 0" @click="drillUp">
              <el-icon><Back /></el-icon> 返回上层
            </el-button>
          </div>
        </div>
      </template>
      <div v-loading="analysisLoading" class="analysis-container">
        <div v-if="!analysisData.length" class="empty-data">
          <el-empty description="请选择维度和指标进行分析"></el-empty>
        </div>
        <div v-else>
          <div class="chart-container" ref="analysisChartRef"></div>
          <div class="drill-path" v-if="drillHistory.length > 0">
            当前钻取路径: 
            <el-tag 
              v-for="(drill, index) in drillHistory" 
              :key="index"
              class="drill-tag"
            >
              {{ drill.dimension }}: {{ drill.value }}
              <el-icon v-if="index < drillHistory.length - 1"><ArrowRight /></el-icon>
            </el-tag>
          </div>
          <el-table
            :data="analysisData"
            style="width: 100%; margin-top: 20px;"
            border
            stripe
            :max-height="400"
          >
            <el-table-column
              v-for="col in analysisColumns"
              :key="col.prop"
              :prop="col.prop"
              :label="col.label"
              :formatter="col.formatter"
              :width="col.width"
            />
            <el-table-column label="操作" width="120" v-if="canDrillDown">
              <template #default="scope">
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="drillDown(scope.row)"
                  :disabled="!hasDrillDownDimension(scope.row)"
                >
                  钻取
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>

    <!-- 趋势预测 -->
    <el-card class="dashboard-card">
      <template #header>
        <div class="card-header">
          <h2>趋势预测</h2>
          <div class="prediction-selector">
            <el-radio-group v-model="predictionType" @change="loadPrediction">
              <el-radio-button label="revenue">营收预测</el-radio-button>
              <el-radio-button label="customer_flow">客流量预测</el-radio-button>
              <el-radio-button label="order_count">订单数预测</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>
      <div v-loading="predictionLoading" class="prediction-container">
        <div class="chart-container" ref="predictionChartRef"></div>
        <div class="prediction-factors">
          <h3>影响因素</h3>
          <el-table
            :data="predictionFactors"
            style="width: 100%"
            border
          >
            <el-table-column prop="name" label="因素名称" width="180" />
            <el-table-column prop="impact" label="影响程度" width="180">
              <template #default="scope">
                <el-progress
                  :percentage="Math.abs(scope.row.impact * 100)"
                  :status="scope.row.impact > 0 ? 'success' : 'exception'"
                  :stroke-width="15"
                  :format="() => Math.abs(scope.row.impact * 100).toFixed(0) + '%'"
                />
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" />
          </el-table>
        </div>
      </div>
    </el-card>
    
    <!-- 分享模态框 -->
    <share-report-modal
      v-model:visible="shareModalVisible"
      :reportConfig="dashboardConfig"
      reportType="dashboard"
      :title="'数据驾驶舱'"
      @close="shareModalVisible = false"
    />
    
    <!-- 导出选项模态框 - 核心指标 -->
    <export-options-modal
      v-model:visible="indicatorsExportModalVisible"
      title="核心指标"
      :data="indicatorsExportData"
      :headers="indicatorsExportHeaders"
      :element-id="'indicators-section'"
      @export-success="handleExportSuccess"
      @export-error="handleExportError"
    />
    
    <!-- 导出选项模态框 - 多维度分析 -->
    <export-options-modal
      v-model:visible="analysisExportModalVisible"
      title="多维度分析"
      :data="analysisData"
      :headers="analysisExportHeaders"
      :element-id="'analysis-chart'"
      @export-success="handleExportSuccess"
      @export-error="handleExportError"
    />
    
    <!-- 导出选项模态框 - 趋势预测 -->
    <export-options-modal
      v-model:visible="predictionExportModalVisible"
      :title="`趋势预测_${predictionType}`"
      :data="predictionExportData"
      :headers="predictionExportHeaders"
      :element-id="'prediction-chart'"
      @export-success="handleExportSuccess"
      @export-error="handleExportError"
    />
  </div>
</template>

<script>
import { ref, reactive, onMounted, nextTick, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import { Money, User, ShoppingCart, Histogram, Back, ArrowRight, Share } from '@element-plus/icons-vue'
import { getStores } from '@/api/store'
import biApi from '@/api/business_intelligence'
// 导入新的图表组件
import RadarChart from '@/components/charts/RadarChart.vue'
import GaugeChart from '@/components/charts/GaugeChart.vue'
import FunnelChart from '@/components/charts/FunnelChart.vue'
// 导入导出服务
import exportService from '@/utils/exportService'
// 导入分享模态框组件
import ShareReportModal from '@/components/business_intelligence/ShareReportModal.vue'
import ExportOptionsModal from '@/components/business_intelligence/ExportOptionsModal.vue'

export default {
  name: 'DataDashboard',
  components: {
    Money,
    User,
    ShoppingCart,
    Histogram,
    Back,
    ArrowRight,
    Share,
    RadarChart,
    GaugeChart,
    FunnelChart,
    ShareReportModal,
    ExportOptionsModal
  },
  setup() {
    // 路由
    const router = useRouter()
    
    // 筛选条件
    const storeId = ref('')
    const dateRange = ref([])
    const stores = ref([])
    
    // 业务指标数据
    const businessIndicators = ref([
      {
        title: '营业额',
        value: '¥0',
        changePercentage: 0,
        trend: 'up',
        icon: 'Money',
        iconColor: '#67C23A'
      },
      {
        title: '客流量',
        value: '0',
        changePercentage: 0,
        trend: 'up',
        icon: 'User',
        iconColor: '#409EFF'
      },
      {
        title: '订单数',
        value: '0',
        changePercentage: 0,
        trend: 'up',
        icon: 'ShoppingCart',
        iconColor: '#E6A23C'
      },
      {
        title: '客单价',
        value: '¥0',
        changePercentage: 0,
        trend: 'up',
        icon: 'Histogram',
        iconColor: '#F56C6C'
      }
    ])
    
    // 多维度分析
    const dimensionOptions = [
      { label: '按天', value: 'time_daily' },
      { label: '按周', value: 'time_weekly' },
      { label: '按月', value: 'time_monthly' },
      { label: '按门店', value: 'store' },
      { label: '按会员等级', value: 'customer_level' },
      { label: '按项目类型', value: 'item_type' },
      { label: '按员工', value: 'employee' },
      { label: '按支付方式', value: 'payment_method' }
    ]
    const metricOptions = [
      { label: '订单数', value: 'order_count' },
      { label: '总营收', value: 'total_revenue' },
      { label: '实际营收', value: 'actual_revenue' },
      { label: '优惠金额', value: 'discount_amount' },
      { label: '客户数', value: 'customer_count' },
      { label: '客单价', value: 'avg_order_amount' }
    ]
    const selectedDimensions = ref(['time_monthly', 'store'])
    const selectedMetrics = ref(['total_revenue', 'order_count'])
    const analysisData = ref([])
    const analysisLoading = ref(false)
    const analysisChartRef = ref(null)
    let analysisChart = null
    
    // 钻取分析相关
    const drillHistory = ref([])
    const drillFilters = ref([])
    
    // 是否可以继续钻取
    const canDrillDown = computed(() => {
      // 如果已选维度中包含可以进一步钻取的维度，则返回true
      if (selectedDimensions.value.includes('time_monthly')) {
        return true // 月可以钻取到天
      }
      if (selectedDimensions.value.includes('time_weekly')) {
        return true // 周可以钻取到天
      }
      if (selectedDimensions.value.includes('store') && !drillHistory.value.some(d => d.dimension === '门店')) {
        return true // 门店可以钻取到员工
      }
      if (selectedDimensions.value.includes('customer_level') && !drillHistory.value.some(d => d.dimension === '会员等级')) {
        return true // 会员等级可以钻取到具体会员
      }
      if (selectedDimensions.value.includes('item_type') && !drillHistory.value.some(d => d.dimension === '项目类型')) {
        return true // 项目类型可以钻取到具体项目
      }
      return false
    })
    
    // 检查特定行是否可以进行钻取
    const hasDrillDownDimension = (row) => {
      if (selectedDimensions.value.includes('time_monthly') && row.time) {
        return true
      }
      if (selectedDimensions.value.includes('time_weekly') && row.time) {
        return true
      }
      if (selectedDimensions.value.includes('store') && row.store_id && !drillHistory.value.some(d => d.dimension === '门店')) {
        return true
      }
      if (selectedDimensions.value.includes('customer_level') && row.level_id && !drillHistory.value.some(d => d.dimension === '会员等级')) {
        return true
      }
      if (selectedDimensions.value.includes('item_type') && row.item_type && !drillHistory.value.some(d => d.dimension === '项目类型')) {
        return true
      }
      return false
    }
    
    // 钻取到下一层
    const drillDown = (row) => {
      // 保存当前状态到历史记录
      const currentState = {
        dimensions: [...selectedDimensions.value],
        metrics: [...selectedMetrics.value],
        filters: [...drillFilters.value]
      }
      
      // 根据当前选择的维度和行数据确定钻取方向
      if (selectedDimensions.value.includes('time_monthly') && row.time) {
        // 从月钻取到日
        drillHistory.value.push({
          dimension: '月份',
          value: row.time,
          state: currentState
        })
        
        // 更新维度和过滤条件
        const monthDate = new Date(row.time + '-01')
        const year = monthDate.getFullYear()
        const month = monthDate.getMonth() + 1
        const lastDay = new Date(year, month, 0).getDate()
        
        const startDate = `${year}-${month.toString().padStart(2, '0')}-01`
        const endDate = `${year}-${month.toString().padStart(2, '0')}-${lastDay}`
        
        // 替换月维度为日维度
        selectedDimensions.value = selectedDimensions.value.map(d => 
          d === 'time_monthly' ? 'time_daily' : d
        )
        
        // 添加时间过滤条件
        drillFilters.value.push({
          field: 'date_range',
          value: {
            start: startDate,
            end: endDate
          }
        })
        
        // 重新加载数据
        analyzeData()
      } 
      else if (selectedDimensions.value.includes('time_weekly') && row.time) {
        // 从周钻取到日
        drillHistory.value.push({
          dimension: '周',
          value: row.time,
          state: currentState
        })
        
        // 解析周信息 (格式: 2023-W01)
        const [year, week] = row.time.split('-W')
        
        // 计算该周的开始日期和结束日期
        const startDate = getDateOfWeek(parseInt(year), parseInt(week))
        const endDate = new Date(startDate)
        endDate.setDate(startDate.getDate() + 6)
        
        // 格式化日期
        const formatStartDate = startDate.toISOString().split('T')[0]
        const formatEndDate = endDate.toISOString().split('T')[0]
        
        // 替换周维度为日维度
        selectedDimensions.value = selectedDimensions.value.map(d => 
          d === 'time_weekly' ? 'time_daily' : d
        )
        
        // 添加时间过滤条件
        drillFilters.value.push({
          field: 'date_range',
          value: {
            start: formatStartDate,
            end: formatEndDate
          }
        })
        
        // 重新加载数据
        analyzeData()
      }
      else if (selectedDimensions.value.includes('store') && row.store_id && !drillHistory.value.some(d => d.dimension === '门店')) {
        // 从门店钻取到员工
        drillHistory.value.push({
          dimension: '门店',
          value: row.store_name,
          state: currentState
        })
        
        // 替换门店维度为员工维度
        selectedDimensions.value = selectedDimensions.value.map(d => 
          d === 'store' ? 'employee' : d
        )
        
        // 添加门店过滤条件
        drillFilters.value.push({
          field: 'store_id',
          value: row.store_id
        })
        
        // 重新加载数据
        analyzeData()
      }
      else if (selectedDimensions.value.includes('customer_level') && row.level_id && !drillHistory.value.some(d => d.dimension === '会员等级')) {
        // 从会员等级钻取到具体会员
        drillHistory.value.push({
          dimension: '会员等级',
          value: row.level_name,
          state: currentState
        })
        
        // 添加会员等级过滤条件
        drillFilters.value.push({
          field: 'customer_level_id',
          value: row.level_id
        })
        
        // 重新加载数据，这里可以跳转到会员列表页面，也可以继续在当前页面展示该等级的会员数据
        ElMessage.info('正在开发会员等级钻取功能...')
      }
      else if (selectedDimensions.value.includes('item_type') && row.item_type && !drillHistory.value.some(d => d.dimension === '项目类型')) {
        // 从项目类型钻取到具体项目
        drillHistory.value.push({
          dimension: '项目类型',
          value: row.item_type,
          state: currentState
        })
        
        // 添加项目类型过滤条件
        drillFilters.value.push({
          field: 'service_type',
          value: row.item_type
        })
        
        // 重新加载数据
        analyzeData()
      }
    }
    
    // 返回上一层
    const drillUp = () => {
      if (drillHistory.value.length === 0) return
      
      // 恢复上一层的状态
      const lastState = drillHistory.value.pop().state
      selectedDimensions.value = lastState.dimensions
      selectedMetrics.value = lastState.metrics
      drillFilters.value = lastState.filters
      
      // 重新加载数据
      analyzeData()
    }
    
    // 获取指定年份和周数的日期
    const getDateOfWeek = (year, week) => {
      const date = new Date(year, 0, 1)
      const dayOfWeek = date.getDay()
      const daysToAdd = (week - 1) * 7 + (dayOfWeek <= 4 ? 1 - dayOfWeek : 8 - dayOfWeek)
      date.setDate(date.getDate() + daysToAdd)
      return date
    }
    
    // 趋势预测
    const predictionType = ref('revenue')
    const predictionLoading = ref(false)
    const predictionChartRef = ref(null)
    let predictionChart = null
    const predictionData = ref({})
    const predictionFactors = ref([])
    
    // 分析结果表格列
    const analysisColumns = computed(() => {
      const columns = []
      
      // 添加维度列
      if (selectedDimensions.value.includes('time_daily') || 
          selectedDimensions.value.includes('time_weekly') || 
          selectedDimensions.value.includes('time_monthly')) {
        columns.push({
          prop: 'time',
          label: '时间',
          width: 120
        })
      }
      
      if (selectedDimensions.value.includes('store')) {
        columns.push({
          prop: 'store_name',
          label: '门店',
          width: 120
        })
      }
      
      if (selectedDimensions.value.includes('customer_level')) {
        columns.push({
          prop: 'level_name',
          label: '会员等级',
          width: 120
        })
      }
      
      if (selectedDimensions.value.includes('item_type')) {
        columns.push({
          prop: 'item_type',
          label: '项目类型',
          width: 120
        })
      }
      
      // 添加指标列
      if (selectedMetrics.value.includes('order_count')) {
        columns.push({
          prop: 'order_count',
          label: '订单数',
          width: 100
        })
      }
      
      if (selectedMetrics.value.includes('total_revenue')) {
        columns.push({
          prop: 'total_revenue',
          label: '总营收',
          formatter: (row) => `¥${row.total_revenue.toFixed(2)}`,
          width: 120
        })
      }
      
      if (selectedMetrics.value.includes('actual_revenue')) {
        columns.push({
          prop: 'actual_revenue',
          label: '实际营收',
          formatter: (row) => `¥${row.actual_revenue.toFixed(2)}`,
          width: 120
        })
      }
      
      if (selectedMetrics.value.includes('discount_amount')) {
        columns.push({
          prop: 'discount_amount',
          label: '优惠金额',
          formatter: (row) => `¥${row.discount_amount.toFixed(2)}`,
          width: 120
        })
      }
      
      if (selectedMetrics.value.includes('customer_count')) {
        columns.push({
          prop: 'customer_count',
          label: '客户数',
          width: 100
        })
      }
      
      if (selectedMetrics.value.includes('avg_order_amount')) {
        columns.push({
          prop: 'avg_order_amount',
          label: '客单价',
          formatter: (row) => `¥${row.avg_order_amount.toFixed(2)}`,
          width: 120
        })
      }
      
      return columns
    })
    
    // 获取门店列表
    const fetchStores = async () => {
      try {
        const res = await getStores()
        stores.value = res.items
      } catch (error) {
        console.error('获取门店列表失败:', error)
        ElMessage.error('获取门店列表失败')
      }
    }
    
    // 获取业务指标数据
    const fetchBusinessIndicators = async () => {
      try {
        const params = {}
        if (storeId.value) params.store_ids = [storeId.value]
        if (dateRange.value && dateRange.value.length === 2) {
          params.start_date = dateRange.value[0]
          params.end_date = dateRange.value[1]
        }
        
        const res = await biApi.getBusinessIndicators(params)
        
        console.log('BI API响应:', res)

        // 更新指标卡片数据
        if (res.data && res.data.indicators) {
          const indicators = res.data.indicators

          // 营业额 (对应后端的sales_amount)
          if (indicators.sales_amount) {
            businessIndicators.value[0].value = `¥${indicators.sales_amount.current_value || 0}`
            businessIndicators.value[0].changePercentage = Math.abs(indicators.sales_amount.change_percent || 0).toFixed(2)
            businessIndicators.value[0].trend = indicators.sales_amount.trend || 'stable'
          }

          // 客流量 (对应后端的customer_count)
          if (indicators.customer_count) {
            businessIndicators.value[1].value = indicators.customer_count.current_value || 0
            businessIndicators.value[1].changePercentage = Math.abs(indicators.customer_count.change_percent || 0).toFixed(2)
            businessIndicators.value[1].trend = indicators.customer_count.trend || 'stable'
          }

          // 订单数 (对应后端的order_count)
          if (indicators.order_count) {
            businessIndicators.value[2].value = indicators.order_count.current_value || 0
            businessIndicators.value[2].changePercentage = Math.abs(indicators.order_count.change_percent || 0).toFixed(2)
            businessIndicators.value[2].trend = indicators.order_count.trend || 'stable'
          }

          // 客单价 (对应后端的avg_order_value)
          if (indicators.avg_order_value) {
            businessIndicators.value[3].value = `¥${indicators.avg_order_value.current_value || 0}`
            businessIndicators.value[3].changePercentage = Math.abs(indicators.avg_order_value.change_percent || 0).toFixed(2)
            businessIndicators.value[3].trend = indicators.avg_order_value.trend || 'stable'
          }

          console.log('业务指标更新完成:', businessIndicators.value)
          ElMessage.success('业务指标数据加载成功')
        } else {
          console.warn('BI API响应数据格式不正确:', res)
          ElMessage.warning('业务指标数据格式不正确')
        }
      } catch (error) {
        console.error('获取业务指标数据失败:', error)
        ElMessage.error('获取业务指标数据失败')
      }
    }
    
    // 分析数据
    const analyzeData = async () => {
      if (selectedDimensions.value.length === 0) {
        ElMessage.warning('请至少选择一个维度')
        return
      }
      
      if (selectedMetrics.value.length === 0) {
        ElMessage.warning('请至少选择一个指标')
        return
      }
      
      analysisLoading.value = true
      
      try {
        const params = {
          data_type: 'orders',
          dimensions: selectedDimensions.value,
          metrics: selectedMetrics.value
        }
        
        if (storeId.value) params.store_ids = [storeId.value]
        if (dateRange.value && dateRange.value.length === 2) {
          params.start_date = dateRange.value[0]
          params.end_date = dateRange.value[1]
        }
        
        // 添加钻取过滤条件
        if (drillFilters.value.length > 0) {
          params.filters = drillFilters.value
        }
        
        console.log('发送多维度分析请求参数:', params)
        const res = await biApi.getMultiDimensionalAnalysis(params)
        console.log('多维度分析API完整响应:', res)

        // 修复数据格式处理逻辑
        if (res && res.data) {
          // 检查是否是直接的数组格式 (新的API格式)
          if (Array.isArray(res.data)) {
            analysisData.value = res.data
            console.log('多维度分析数据更新完成 (直接数组格式):', analysisData.value)
            ElMessage.success(`多维度分析数据加载成功，共${analysisData.value.length}条记录`)
          }
          // 检查是否是嵌套的data格式 (旧的API格式)
          else if (res.data.data && Array.isArray(res.data.data)) {
            analysisData.value = res.data.data
            console.log('多维度分析数据更新完成 (嵌套data格式):', analysisData.value)
            ElMessage.success(`多维度分析数据加载成功，共${analysisData.value.length}条记录`)
          }
          // 如果有其他字段但data为空
          else if (res.data.dimensions || res.data.metrics) {
            analysisData.value = []
            console.log('多维度分析返回空数据，但API结构正确')
            ElMessage.success('多维度分析完成，暂无数据')
          }
          else {
            console.warn('多维度分析API响应数据格式不正确:', res)
            analysisData.value = []
            ElMessage.warning('多维度分析数据格式不正确')
          }
        } else {
          console.warn('多维度分析API响应为空:', res)
          analysisData.value = []
          ElMessage.warning('多维度分析API响应为空')
        }
        
        nextTick(() => {
          renderAnalysisChart()
        })
      } catch (error) {
        console.error('分析数据失败:', error)
        ElMessage.error('分析数据失败')
      } finally {
        analysisLoading.value = false
      }
    }
    
    // 渲染分析图表
    const renderAnalysisChart = () => {
      if (!analysisChartRef.value) return
      
      if (!analysisChart) {
        analysisChart = echarts.init(analysisChartRef.value)
      }
      
      // 根据选择的维度和指标，动态生成图表配置
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: selectedMetrics.value.map(metric => {
            const metricObj = metricOptions.find(m => m.value === metric)
            return metricObj ? metricObj.label : metric
          })
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        }
      }
      
      // 处理坐标轴
      if (selectedDimensions.value.includes('time_daily') || 
          selectedDimensions.value.includes('time_weekly') || 
          selectedDimensions.value.includes('time_monthly')) {
        // 时间维度作为X轴
        option.xAxis = {
          type: 'category',
          data: [...new Set(analysisData.value.map(item => item.time))].sort()
        }
        option.yAxis = {
          type: 'value'
        }
      } else if (selectedDimensions.value.includes('store')) {
        // 门店维度作为X轴
        option.xAxis = {
          type: 'category',
          data: [...new Set(analysisData.value.map(item => item.store_name))]
        }
        option.yAxis = {
          type: 'value'
        }
      } else {
        // 其他维度，使用条形图
        option.yAxis = {
          type: 'category',
          data: selectedDimensions.value.includes('customer_level') 
            ? [...new Set(analysisData.value.map(item => item.level_name))]
            : [...new Set(analysisData.value.map(item => item.item_type))]
        }
        option.xAxis = {
          type: 'value'
        }
      }
      
      // 处理系列
      option.series = selectedMetrics.value.map(metric => {
        const metricObj = metricOptions.find(m => m.value === metric)
        const seriesName = metricObj ? metricObj.label : metric
        
        let seriesData = []
        if (option.xAxis && option.xAxis.type === 'category') {
          // 对于类别X轴，按X轴数据顺序提取指标值
          seriesData = option.xAxis.data.map(xValue => {
            const item = analysisData.value.find(d => {
              if (selectedDimensions.value.includes('time_daily') || 
                  selectedDimensions.value.includes('time_weekly') || 
                  selectedDimensions.value.includes('time_monthly')) {
                return d.time === xValue
              } else {
                return d.store_name === xValue
              }
            })
            return item ? item[metric] : 0
          })
        } else {
          // 对于类别Y轴，按Y轴数据顺序提取指标值
          seriesData = option.yAxis.data.map(yValue => {
            const item = analysisData.value.find(d => {
              if (selectedDimensions.value.includes('customer_level')) {
                return d.level_name === yValue
              } else {
                return d.item_type === yValue
              }
            })
            return item ? item[metric] : 0
          })
        }
        
        return {
          name: seriesName,
          type: ['order_count', 'customer_count'].includes(metric) ? 'bar' : 'line',
          data: seriesData,
          label: {
            show: true,
            position: 'top',
            formatter: function(params) {
              if (['total_revenue', 'actual_revenue', 'discount_amount', 'avg_order_amount'].includes(metric)) {
                return `¥${params.value.toFixed(2)}`
              }
              return params.value
            }
          }
        }
      })
      
      analysisChart.setOption(option)
    }
    
    // 加载趋势预测
    const loadPrediction = async () => {
      predictionLoading.value = true
      
      try {
        const params = {
          prediction_type: predictionType.value,
          prediction_periods: 3,
          period_unit: 'month'
        }
        
        if (storeId.value) params.store_ids = [storeId.value]
        if (dateRange.value && dateRange.value.length === 2) {
          params.historical_start_date = dateRange.value[0]
          params.historical_end_date = dateRange.value[1]
        }
        
        const res = await biApi.getTrendPrediction(params)
        console.log('趋势预测API响应:', res)

        if (res.data) {
          predictionData.value = res.data
          predictionFactors.value = res.data.factors || []
          ElMessage.success('趋势预测数据加载成功')
        } else {
          console.warn('趋势预测API响应数据格式不正确:', res)
          ElMessage.warning('趋势预测数据格式不正确')
        }
        
        nextTick(() => {
          renderPredictionChart()
        })
      } catch (error) {
        console.error('加载趋势预测失败:', error)
        ElMessage.error('加载趋势预测失败')
      } finally {
        predictionLoading.value = false
      }
    }
    
    // 渲染预测图表
    const renderPredictionChart = () => {
      if (!predictionChartRef.value || !predictionData.value.historical_data) return
      
      if (!predictionChart) {
        predictionChart = echarts.init(predictionChartRef.value)
      }
      
      const historicalData = predictionData.value.historical_data
      const predictedData = predictionData.value.predicted_data
      
      const option = {
        title: {
          text: getPredictionTitle(),
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['历史数据', '预测数据', '置信区间'],
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: [...historicalData.time_labels, ...predictedData.time_labels]
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '历史数据',
            type: 'line',
            data: [...historicalData.values, ...new Array(predictedData.time_labels.length).fill(null)],
            smooth: true,
            lineStyle: {
              width: 3
            }
          },
          {
            name: '预测数据',
            type: 'line',
            data: [...new Array(historicalData.time_labels.length).fill(null), ...predictedData.values],
            smooth: true,
            lineStyle: {
              width: 3
            },
            itemStyle: {
              color: '#91cc75'
            }
          },
          {
            name: '置信区间',
            type: 'line',
            data: [...new Array(historicalData.time_labels.length).fill(null), ...predictedData.confidence_intervals.upper],
            lineStyle: {
              opacity: 0
            },
            stack: 'confidence',
            symbol: 'none'
          },
          {
            name: '置信区间',
            type: 'line',
            data: [...new Array(historicalData.time_labels.length).fill(null), ...predictedData.confidence_intervals.lower],
            lineStyle: {
              opacity: 0
            },
            areaStyle: {
              color: '#91cc75',
              opacity: 0.2
            },
            stack: 'confidence',
            symbol: 'none'
          }
        ]
      }
      
      predictionChart.setOption(option)
    }
    
    // 获取预测标题
    const getPredictionTitle = () => {
      switch (predictionType.value) {
        case 'revenue':
          return '营收预测'
        case 'customer_flow':
          return '客流量预测'
        case 'order_count':
          return '订单数预测'
        default:
          return '趋势预测'
      }
    }
    
    // 刷新所有数据
    const refreshData = () => {
      fetchBusinessIndicators()
      analyzeData()
      loadPrediction()
    }
    
    // 导出数据
    const exportData = () => {
      // 显示导出选项对话框
      ElMessageBox.prompt('请输入导出文件名', '导出数据', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        inputPattern: /^[\w\u4e00-\u9fa5\-_]+$/,
        inputErrorMessage: '文件名不能包含特殊字符',
        inputValue: '数据驾驶舱',
        roundButton: true,
        showCancelButton: true,
        inputPlaceholder: '请输入文件名，不含扩展名',
        distinguishCancelAndClose: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '导出中...'
            
            setTimeout(() => {
              instance.confirmButtonLoading = false
              done()
            }, 500)
          } else {
            done()
          }
        }
      }).then(({ value: filename }) => {
        // 显示导出内容选择
        ElMessageBox.confirm(
          '请选择要导出的内容',
          '导出数据',
          {
            confirmButtonText: '核心指标',
            cancelButtonText: '多维度分析',
            distinguishCancelAndClose: true,
            type: 'info',
            showClose: true,
            center: true,
            roundButton: true,
            showCancelButton: true,
            closeOnClickModal: false
          }
        ).then(() => {
          // 导出核心指标
          exportIndicators(filename)
        }).catch((action) => {
          if (action === 'cancel') {
            // 导出多维度分析
            exportAnalysis(filename)
          } else if (action === 'close') {
            // 导出趋势预测
            exportPrediction(filename)
          }
        })
      }).catch(() => {
        // 用户取消导出
      })
    }
    
    // 导出核心指标
    const exportIndicators = (filename) => {
      try {
        // 准备导出数据
        const data = businessIndicators.value.map(indicator => ({
          title: indicator.title,
          value: indicator.value,
          changePercentage: indicator.changePercentage,
          trend: indicator.trend === 'up' ? '上升' : '下降'
        }))
        
        // 准备表头
        const headers = [
          { key: 'title', label: '指标名称' },
          { key: 'value', label: '当前值' },
          { key: 'changePercentage', label: '变化百分比(%)' },
          { key: 'trend', label: '趋势' }
        ]
        
        // 显示导出格式选择
        ElMessageBox.confirm(
          '请选择导出格式',
          '导出核心指标',
          {
            confirmButtonText: 'Excel',
            cancelButtonText: 'CSV',
            distinguishCancelAndClose: true,
            type: 'info',
            showClose: true,
            center: true,
            roundButton: true,
            showCancelButton: true,
            closeOnClickModal: false
          }
        ).then(() => {
          // 导出为Excel
          const result = exportService.exportToExcel(data, `${filename}_核心指标`, headers)
          
          if (result) {
            ElMessage.success('导出Excel成功')
          } else {
            ElMessage.error('导出Excel失败')
          }
        }).catch((action) => {
          if (action === 'cancel') {
            // 导出为CSV
            const result = exportService.exportToCSV(data, `${filename}_核心指标`, headers)
            
            if (result) {
              ElMessage.success('导出CSV成功')
            } else {
              ElMessage.error('导出CSV失败')
            }
          }
        })
      } catch (error) {
        console.error('导出核心指标失败:', error)
        ElMessage.error('导出核心指标失败')
      }
    }
    
    // 导出多维度分析
    const exportAnalysis = (filename) => {
      if (analysisData.value.length === 0) {
        ElMessage.warning('没有多维度分析数据可导出')
        return
      }
      
      try {
        // 准备表头
        const headers = []
        
        // 添加维度列
        if (selectedDimensions.value.includes('time_daily') || 
            selectedDimensions.value.includes('time_weekly') || 
            selectedDimensions.value.includes('time_monthly')) {
          headers.push({ key: 'time', label: '时间' })
        }
        
        if (selectedDimensions.value.includes('store')) {
          headers.push({ key: 'store_name', label: '门店' })
        }
        
        if (selectedDimensions.value.includes('customer_level')) {
          headers.push({ key: 'level_name', label: '会员等级' })
        }
        
        if (selectedDimensions.value.includes('item_type')) {
          headers.push({ key: 'item_type', label: '项目类型' })
        }
        
        // 添加指标列
        if (selectedMetrics.value.includes('order_count')) {
          headers.push({ key: 'order_count', label: '订单数' })
        }
        
        if (selectedMetrics.value.includes('total_revenue')) {
          headers.push({ key: 'total_revenue', label: '总营收' })
        }
        
        if (selectedMetrics.value.includes('actual_revenue')) {
          headers.push({ key: 'actual_revenue', label: '实际营收' })
        }
        
        if (selectedMetrics.value.includes('discount_amount')) {
          headers.push({ key: 'discount_amount', label: '优惠金额' })
        }
        
        if (selectedMetrics.value.includes('customer_count')) {
          headers.push({ key: 'customer_count', label: '客户数' })
        }
        
        if (selectedMetrics.value.includes('avg_order_amount')) {
          headers.push({ key: 'avg_order_amount', label: '客单价' })
        }
        
        // 显示导出格式选择
        ElMessageBox.confirm(
          '请选择导出格式',
          '导出多维度分析',
          {
            confirmButtonText: 'Excel',
            cancelButtonText: 'CSV',
            distinguishCancelAndClose: true,
            type: 'info',
            showClose: true,
            center: true,
            roundButton: true,
            showCancelButton: true,
            closeOnClickModal: false
          }
        ).then(() => {
          // 导出为Excel
          const result = exportService.exportToExcel(analysisData.value, `${filename}_多维度分析`, headers)
          
          if (result) {
            ElMessage.success('导出Excel成功')
          } else {
            ElMessage.error('导出Excel失败')
          }
        }).catch((action) => {
          if (action === 'cancel') {
            // 导出为CSV
            const result = exportService.exportToCSV(analysisData.value, `${filename}_多维度分析`, headers)
            
            if (result) {
              ElMessage.success('导出CSV成功')
            } else {
              ElMessage.error('导出CSV失败')
            }
          } else if (action === 'close') {
            // 导出为图片
            nextTick(async () => {
              const result = await exportService.exportToImage('analysis-chart', `${filename}_多维度分析`)
              
              if (result) {
                ElMessage.success('导出图片成功')
              } else {
                ElMessage.error('导出图片失败')
              }
            })
          }
        })
      } catch (error) {
        console.error('导出多维度分析失败:', error)
        ElMessage.error('导出多维度分析失败')
      }
    }
    
    // 导出趋势预测
    const exportPrediction = (filename) => {
      if (!predictionData.value.historical_data) {
        ElMessage.warning('没有趋势预测数据可导出')
        return
      }
      
      try {
        // 准备导出数据
        const historicalData = predictionData.value.historical_data
        const predictedData = predictionData.value.predicted_data
        
        // 合并历史数据和预测数据
        const data = []
        
        for (let i = 0; i < historicalData.time_series.length; i++) {
          data.push({
            time: historicalData.time_series[i],
            value: historicalData.values[i],
            type: '历史数据'
          })
        }
        
        for (let i = 0; i < predictedData.time_series.length; i++) {
          data.push({
            time: predictedData.time_series[i],
            value: predictedData.values[i],
            type: '预测数据'
          })
        }
        
        // 准备表头
        const headers = [
          { key: 'time', label: '时间' },
          { key: 'value', label: '值' },
          { key: 'type', label: '数据类型' }
        ]
        
        // 显示导出格式选择
        ElMessageBox.confirm(
          '请选择导出格式',
          '导出趋势预测',
          {
            confirmButtonText: 'Excel',
            cancelButtonText: 'CSV',
            distinguishCancelAndClose: true,
            type: 'info',
            showClose: true,
            center: true,
            roundButton: true,
            showCancelButton: true,
            closeOnClickModal: false
          }
        ).then(() => {
          // 导出为Excel
          const result = exportService.exportToExcel(data, `${filename}_趋势预测_${predictionType.value}`, headers)
          
          if (result) {
            ElMessage.success('导出Excel成功')
          } else {
            ElMessage.error('导出Excel失败')
          }
        }).catch((action) => {
          if (action === 'cancel') {
            // 导出为CSV
            const result = exportService.exportToCSV(data, `${filename}_趋势预测_${predictionType.value}`, headers)
            
            if (result) {
              ElMessage.success('导出CSV成功')
            } else {
              ElMessage.error('导出CSV失败')
            }
          } else if (action === 'close') {
            // 导出为图片
            nextTick(async () => {
              const result = await exportService.exportToImage('prediction-chart', `${filename}_趋势预测_${predictionType.value}`)
              
              if (result) {
                ElMessage.success('导出图片成功')
              } else {
                ElMessage.error('导出图片失败')
              }
            })
          }
        })
      } catch (error) {
        console.error('导出趋势预测失败:', error)
        ElMessage.error('导出趋势预测失败')
      }
    }
    
    // 分享功能相关
    const shareModalVisible = ref(false)
    const dashboardConfig = computed(() => {
      return {
        title: '数据驾驶舱',
        description: '展示核心业务指标、多维度分析和趋势预测',
        indicators: businessIndicators.value.map(indicator => ({
          name: indicator.title,
          value: indicator.value,
          format: indicator.format || 'number',
          trend: indicator.trend === 'up' ? indicator.changePercentage : -indicator.changePercentage,
          trendPeriod: '环比'
        })),
        sections: [
          {
            title: '多维度分析',
            span: 24,
            height: 400,
            chartType: 'bar',
            data: analysisData.value
          },
          {
            title: '趋势预测',
            span: 24,
            height: 400,
            chartType: 'line',
            data: predictionData.value
          }
        ],
        filters: {
          storeId: storeId.value,
          dateRange: dateRange.value
        }
      }
    })
    
    // 显示分享模态框
    const showShareModal = () => {
      shareModalVisible.value = true
    }
    
    // 导出相关
    const indicatorsExportModalVisible = ref(false)
    const analysisExportModalVisible = ref(false)
    const predictionExportModalVisible = ref(false)
    
    const indicatorsExportData = ref([])
    const indicatorsExportHeaders = ref([])
    const analysisExportHeaders = ref([])
    const predictionExportData = ref([])
    const predictionExportHeaders = ref([])
    
    // 显示导出选项模态框
    const showExportModal = (type) => {
      if (type === 'indicators') {
        // 准备指标数据
        prepareIndicatorsExportData()
        indicatorsExportModalVisible.value = true
      } else if (type === 'analysis') {
        // 准备分析数据
        prepareAnalysisExportData()
        analysisExportModalVisible.value = true
      } else if (type === 'prediction') {
        // 准备预测数据
        preparePredictionExportData()
        predictionExportModalVisible.value = true
      }
    }
    
    // 准备指标导出数据
    const prepareIndicatorsExportData = () => {
      // 准备导出数据
      indicatorsExportData.value = []
      
      // 将卡片数据转换为表格形式
      if (businessIndicators.value) {
        const { currentPeriod, previousPeriod, trends } = businessIndicators.value
        
        // 添加总营收
        indicatorsExportData.value.push({
          indicator: '总营收',
          current_value: currentPeriod.totalRevenue,
          previous_value: previousPeriod.totalRevenue,
          change_rate: trends.totalRevenue + '%',
          trend: trends.totalRevenue > 0 ? '上升' : (trends.totalRevenue < 0 ? '下降' : '持平')
        })
        
        // 添加订单数
        indicatorsExportData.value.push({
          indicator: '订单数',
          current_value: currentPeriod.orderCount,
          previous_value: previousPeriod.orderCount,
          change_rate: trends.orderCount + '%',
          trend: trends.orderCount > 0 ? '上升' : (trends.orderCount < 0 ? '下降' : '持平')
        })
        
        // 添加客单价
        indicatorsExportData.value.push({
          indicator: '客单价',
          current_value: currentPeriod.averageOrderValue,
          previous_value: previousPeriod.averageOrderValue,
          change_rate: trends.averageOrderValue + '%',
          trend: trends.averageOrderValue > 0 ? '上升' : (trends.averageOrderValue < 0 ? '下降' : '持平')
        })
        
        // 添加客户数
        indicatorsExportData.value.push({
          indicator: '客户数',
          current_value: currentPeriod.customerCount,
          previous_value: previousPeriod.customerCount,
          change_rate: trends.customerCount + '%',
          trend: trends.customerCount > 0 ? '上升' : (trends.customerCount < 0 ? '下降' : '持平')
        })
        
        // 添加新客户数
        indicatorsExportData.value.push({
          indicator: '新客户数',
          current_value: currentPeriod.newCustomerCount,
          previous_value: previousPeriod.newCustomerCount,
          change_rate: trends.newCustomerCount + '%',
          trend: trends.newCustomerCount > 0 ? '上升' : (trends.newCustomerCount < 0 ? '下降' : '持平')
        })
        
        // 添加复购率
        indicatorsExportData.value.push({
          indicator: '复购率',
          current_value: currentPeriod.repeatPurchaseRate + '%',
          previous_value: previousPeriod.repeatPurchaseRate + '%',
          change_rate: trends.repeatPurchaseRate + '%',
          trend: trends.repeatPurchaseRate > 0 ? '上升' : (trends.repeatPurchaseRate < 0 ? '下降' : '持平')
        })
      }
      
      // 准备表头
      indicatorsExportHeaders.value = [
        { key: 'indicator', label: '指标' },
        { key: 'current_value', label: '当前值' },
        { key: 'previous_value', label: '上期值' },
        { key: 'change_rate', label: '变化率' },
        { key: 'trend', label: '趋势' }
      ]
      
      // 显示导出选项模态框
      indicatorsExportModalVisible.value = true
    }
    
    // 准备分析导出数据
    const prepareAnalysisExportData = () => {
      // 准备表头
      analysisExportHeaders.value = []
      
      // 添加维度列
      if (selectedDimensions.value.includes('store')) {
        analysisExportHeaders.value.push({ key: 'dimension', label: '门店' })
      } else if (selectedDimensions.value.includes('employee')) {
        analysisExportHeaders.value.push({ key: 'dimension', label: '员工' })
      } else if (selectedDimensions.value.includes('item_type')) {
        analysisExportHeaders.value.push({ key: 'dimension', label: '项目' })
      } else if (selectedDimensions.value.includes('customer_level')) {
        analysisExportHeaders.value.push({ key: 'dimension', label: '会员等级' })
      } else {
        analysisExportHeaders.value.push({ key: 'dimension', label: '维度' })
      }
      
      // 添加指标列
      if (selectedMetrics.value.includes('total_revenue')) {
        analysisExportHeaders.value.push({ key: 'value', label: '营收额' })
      } else if (selectedMetrics.value.includes('order_count')) {
        analysisExportHeaders.value.push({ key: 'value', label: '订单数' })
      } else if (selectedMetrics.value.includes('customer_count')) {
        analysisExportHeaders.value.push({ key: 'value', label: '客户数' })
      } else if (selectedMetrics.value.includes('avg_order_amount')) {
        analysisExportHeaders.value.push({ key: 'value', label: '客单价' })
      } else {
        analysisExportHeaders.value.push({ key: 'value', label: '数值' })
      }
      
      // 显示导出选项模态框
      analysisExportModalVisible.value = true
    }
    
    // 准备预测导出数据
    const preparePredictionExportData = () => {
      // 准备导出数据
      predictionExportData.value = []
      
      if (predictionData.value) {
        const { historical_data, predicted_data } = predictionData.value
        
        // 将历史数据和预测数据合并
        for (let i = 0; i < historical_data.time_series.length; i++) {
          predictionExportData.value.push({
            time: historical_data.time_series[i],
            value: historical_data.values[i],
            type: '历史数据'
          })
        }
        
        for (let i = 0; i < predicted_data.time_series.length; i++) {
          predictionExportData.value.push({
            time: predicted_data.time_series[i],
            value: predicted_data.values[i],
            type: '预测数据'
          })
        }
      }
      
      // 准备表头
      predictionExportHeaders.value = [
        { key: 'time', label: '时间' },
        { key: 'value', label: '数值' },
        { key: 'type', label: '数据类型' }
      ]
      
      // 显示导出选项模态框
      predictionExportModalVisible.value = true
    }
    
    // 处理导出成功
    const handleExportSuccess = (type) => {
      ElMessage.success(`导出${type}成功`)
    }
    
    // 处理导出失败
    const handleExportError = (type, error) => {
      console.error(`导出${type}失败:`, error)
      ElMessage.error(`导出${type}失败`)
    }
    
    // 监听窗口大小变化，调整图表大小
    const handleResize = () => {
      if (analysisChart) analysisChart.resize()
      if (predictionChart) predictionChart.resize()
    }
    
    // 生命周期钩子
    onMounted(() => {
      fetchStores()
      fetchBusinessIndicators()
      analyzeData()
      loadPrediction()
      
      window.addEventListener('resize', handleResize)
    })
    
    return {
      storeId,
      dateRange,
      stores,
      businessIndicators,
      dimensionOptions,
      metricOptions,
      selectedDimensions,
      selectedMetrics,
      analysisData,
      analysisLoading,
      analysisChartRef,
      analysisColumns,
      predictionType,
      predictionLoading,
      predictionChartRef,
      predictionFactors,
      drillHistory,
      canDrillDown,
      hasDrillDownDimension,
      drillDown,
      drillUp,
      refreshData,
      analyzeData,
      loadPrediction,
      exportData,
      exportIndicators,
      exportAnalysis,
      exportPrediction,
      
      // 分享相关
      shareModalVisible,
      dashboardConfig,
      showShareModal,
      
      // 导出相关
      indicatorsExportModalVisible,
      analysisExportModalVisible,
      predictionExportModalVisible,
      indicatorsExportData,
      indicatorsExportHeaders,
      analysisExportHeaders,
      predictionExportData,
      predictionExportHeaders,
      handleExportSuccess,
      handleExportError
    }
  }
}
</script>

<style scoped>
.data-dashboard-container {
  padding: 20px;
}

.dashboard-header {
  margin-bottom: 20px;
}

.dashboard-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dashboard-title h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.dashboard-row {
  margin-bottom: 20px;
}

.indicator-card {
  height: 120px;
  overflow: hidden;
}

.indicator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.indicator-title {
  font-size: 16px;
  color: #606266;
}

.indicator-icon {
  font-size: 24px;
}

.indicator-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.indicator-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.indicator-change {
  display: flex;
  align-items: center;
}

.indicator-change.up {
  color: #67C23A;
}

.indicator-change.down {
  color: #F56C6C;
}

.indicator-period {
  color: #909399;
}

.dashboard-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.analysis-container {
  min-height: 500px;
}

.chart-container {
  height: 400px;
  margin-bottom: 20px;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.prediction-container {
  min-height: 600px;
}

.prediction-factors {
  margin-top: 20px;
}

.prediction-factors h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  color: #303133;
}

.drill-path {
  margin: 15px 0;
  font-size: 14px;
  color: #606266;
}

.drill-tag {
  margin-right: 5px;
  display: inline-flex;
  align-items: center;
}

.drill-tag .el-icon {
  margin: 0 5px;
}

.el-button + .el-button {
  margin-left: 10px;
}
</style> 