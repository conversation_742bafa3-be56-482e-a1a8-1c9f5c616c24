<template>
  <div class="ai-analytics-container">
    <el-card class="header-card">
      <div class="page-header">
        <div class="page-title">
          <h1>AI智能分析</h1>
          <p class="page-description">利用人工智能技术深入分析业务数据，发现潜在规律和机会</p>
        </div>
        <div class="filter-section">
          <el-select v-model="storeId" placeholder="选择门店" clearable style="width: 150px; margin-right: 10px;">
            <el-option v-for="store in stores" :key="store.id" :label="store.name" :value="store.id" />
          </el-select>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            style="width: 300px; margin-right: 10px;"
          />
          <el-button type="primary" @click="runAnalysis" :loading="loading">
            <el-icon><DataAnalysis /></el-icon> 开始分析
          </el-button>
        </div>
      </div>
    </el-card>

    <el-tabs v-model="activeTab" class="analysis-tabs" @tab-click="handleTabClick">
      <el-tab-pane label="综合洞察" name="comprehensive">
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="3" animated />
          <el-skeleton :rows="3" animated />
          <el-skeleton :rows="3" animated />
        </div>
        <div v-else-if="!hasComprehensiveInsights" class="empty-container">
          <el-empty description="暂无分析数据，请选择门店和日期范围，点击开始分析按钮" />
        </div>
        <div v-else>
          <div class="summary-card" v-if="comprehensiveInsights.summary">
            <div class="summary-header">
              <el-icon><Connection /></el-icon>
              <h2>洞察摘要</h2>
            </div>
            <div class="summary-content">
              <p>{{ comprehensiveInsights.summary }}</p>
            </div>
          </div>
          
          <div class="insights-container">
            <ai-insight-card
              v-for="(insight, index) in comprehensiveInsights.insights"
              :key="index"
              :insight="insight"
              @export="exportInsight"
            />
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="销售模式" name="sales">
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="3" animated />
          <el-skeleton :rows="3" animated />
        </div>
        <div v-else-if="!hasSalesInsights" class="empty-container">
          <el-empty description="暂无销售模式分析数据，请选择门店和日期范围，点击开始分析按钮" />
        </div>
        <div v-else class="insights-container">
          <ai-insight-card
            v-for="(insight, index) in salesInsights.insights"
            :key="index"
            :insight="insight"
            @export="exportInsight"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="客户行为" name="customer">
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="3" animated />
          <el-skeleton :rows="3" animated />
        </div>
        <div v-else-if="!hasCustomerInsights" class="empty-container">
          <el-empty description="暂无客户行为分析数据，请选择门店和日期范围，点击开始分析按钮" />
        </div>
        <div v-else class="insights-container">
          <ai-insight-card
            v-for="(insight, index) in customerInsights.insights"
            :key="index"
            :insight="insight"
            @export="exportInsight"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="员工绩效" name="employee">
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="3" animated />
          <el-skeleton :rows="3" animated />
        </div>
        <div v-else-if="!hasEmployeeInsights" class="empty-container">
          <el-empty description="暂无员工绩效分析数据，请选择门店和日期范围，点击开始分析按钮" />
        </div>
        <div v-else class="insights-container">
          <ai-insight-card
            v-for="(insight, index) in employeeInsights.insights"
            :key="index"
            :insight="insight"
            @export="exportInsight"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="业务机会" name="business">
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="3" animated />
          <el-skeleton :rows="3" animated />
        </div>
        <div v-else-if="!hasBusinessInsights" class="empty-container">
          <el-empty description="暂无业务机会分析数据，请选择门店和日期范围，点击开始分析按钮" />
        </div>
        <div v-else class="insights-container">
          <ai-insight-card
            v-for="(insight, index) in businessInsights.insights"
            :key="index"
            :insight="insight"
            @export="exportInsight"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { DataAnalysis, Connection } from '@element-plus/icons-vue'
import AIInsightCard from '@/components/business_intelligence/AIInsightCard.vue'
import aiAnalysisApi from '@/api/ai_analysis'
import exportService from '@/utils/exportService'
import { getStores } from '@/api/store'

export default {
  name: 'AIAnalytics',
  components: {
    DataAnalysis,
    Connection,
    AIInsightCard
  },
  setup() {
    // 状态
    const loading = ref(false)
    const activeTab = ref('comprehensive')
    const storeId = ref('')
    const dateRange = ref([])
    const stores = ref([])
    
    // 分析结果
    const comprehensiveInsights = ref({ insights: [] })
    const salesInsights = ref({ insights: [] })
    const customerInsights = ref({ insights: [] })
    const employeeInsights = ref({ insights: [] })
    const businessInsights = ref({ insights: [] })
    
    // 计算属性
    const hasComprehensiveInsights = computed(() => {
      return comprehensiveInsights.value && 
             comprehensiveInsights.value.insights && 
             comprehensiveInsights.value.insights.length > 0
    })
    
    const hasSalesInsights = computed(() => {
      return salesInsights.value && 
             salesInsights.value.insights && 
             salesInsights.value.insights.length > 0
    })
    
    const hasCustomerInsights = computed(() => {
      return customerInsights.value && 
             customerInsights.value.insights && 
             customerInsights.value.insights.length > 0
    })
    
    const hasEmployeeInsights = computed(() => {
      return employeeInsights.value && 
             employeeInsights.value.insights && 
             employeeInsights.value.insights.length > 0
    })
    
    const hasBusinessInsights = computed(() => {
      return businessInsights.value && 
             businessInsights.value.insights && 
             businessInsights.value.insights.length > 0
    })
    
    // 加载门店列表
    const loadStores = async () => {
      try {
        const response = await getStores()
        stores.value = response.data || []
      } catch (error) {
        console.error('加载门店列表失败:', error)
        ElMessage.error('加载门店列表失败')
      }
    }
    
    // 运行分析
    const runAnalysis = async () => {
      if (!dateRange.value || dateRange.value.length !== 2) {
        ElMessage.warning('请选择日期范围')
        return
      }
      
      const loadingInstance = ElLoading.service({
        text: '正在进行智能分析，请稍候...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      
      loading.value = true
      
      try {
        // 准备查询参数
        const params = {
          store_id: storeId.value || undefined,
          start_date: dateRange.value[0],
          end_date: dateRange.value[1]
        }
        
        // 根据当前选项卡运行不同的分析
        if (activeTab.value === 'comprehensive' || activeTab.value === 'sales') {
          const salesResponse = await aiAnalysisApi.analyzeSalesPattern(params)
          salesInsights.value = salesResponse
        }
        
        if (activeTab.value === 'comprehensive' || activeTab.value === 'customer') {
          const customerResponse = await aiAnalysisApi.analyzeCustomerBehavior(params)
          customerInsights.value = customerResponse
        }
        
        if (activeTab.value === 'comprehensive' || activeTab.value === 'employee') {
          const employeeResponse = await aiAnalysisApi.analyzeEmployeePerformance(params)
          employeeInsights.value = employeeResponse
        }
        
        if (activeTab.value === 'comprehensive' || activeTab.value === 'business') {
          const businessResponse = await aiAnalysisApi.analyzeBusinessOpportunities(params)
          businessInsights.value = businessResponse
        }
        
        if (activeTab.value === 'comprehensive') {
          const comprehensiveResponse = await aiAnalysisApi.getComprehensiveInsights(params)
          comprehensiveInsights.value = comprehensiveResponse
        }
        
        ElMessage.success('分析完成')
      } catch (error) {
        console.error('AI分析失败:', error)
        ElMessage.error('AI分析失败，请稍后重试')
      } finally {
        loading.value = false
        loadingInstance.close()
      }
    }
    
    // 处理标签页点击
    const handleTabClick = (tab) => {
      // 如果已经有数据，不需要重新加载
      if (tab.props.name === 'comprehensive' && !hasComprehensiveInsights.value) {
        runAnalysis()
      } else if (tab.props.name === 'sales' && !hasSalesInsights.value) {
        runAnalysis()
      } else if (tab.props.name === 'customer' && !hasCustomerInsights.value) {
        runAnalysis()
      } else if (tab.props.name === 'employee' && !hasEmployeeInsights.value) {
        runAnalysis()
      } else if (tab.props.name === 'business' && !hasBusinessInsights.value) {
        runAnalysis()
      }
    }
    
    // 导出洞察
    const exportInsight = (insight) => {
      // 创建导出内容
      const exportContent = {
        title: insight.title,
        content: insight.content,
        importance: insight.importance === 'high' ? '重要' : (insight.importance === 'medium' ? '中等' : '一般'),
        type: insight.type
      }
      
      // 导出为JSON
      exportService.exportToJSON(exportContent, `AI洞察_${insight.title}`)
      ElMessage.success('导出成功')
    }
    
    // 组件挂载时加载门店列表
    onMounted(() => {
      loadStores()
      
      // 设置默认日期范围为最近30天
      const endDate = new Date()
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - 30)
      
      dateRange.value = [
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0]
      ]
    })
    
    return {
      loading,
      activeTab,
      storeId,
      dateRange,
      stores,
      comprehensiveInsights,
      salesInsights,
      customerInsights,
      employeeInsights,
      businessInsights,
      hasComprehensiveInsights,
      hasSalesInsights,
      hasCustomerInsights,
      hasEmployeeInsights,
      hasBusinessInsights,
      runAnalysis,
      handleTabClick,
      exportInsight
    }
  }
}
</script>

<style scoped>
.ai-analytics-container {
  padding: 20px;
}

.header-card {
  margin-bottom: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.page-title {
  margin-bottom: 10px;
}

.page-title h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

.page-description {
  margin: 8px 0 0;
  color: #666;
}

.filter-section {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 10px;
}

.analysis-tabs {
  margin-top: 20px;
}

.loading-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.empty-container {
  padding: 40px 0;
}

.summary-card {
  background-color: #f0f9ff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  border-left: 4px solid #409EFF;
}

.summary-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.summary-header h2 {
  margin: 0 0 0 8px;
  font-size: 18px;
  font-weight: 500;
}

.summary-content {
  color: #606266;
  line-height: 1.6;
}

.insights-container {
  margin-top: 20px;
}

@media screen and (max-width: 768px) {
  .ai-analytics-container {
    padding: 12px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .filter-section {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .filter-section .el-select,
  .filter-section .el-date-picker {
    width: 100% !important;
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .filter-section .el-button {
    width: 100%;
  }
}
</style> 