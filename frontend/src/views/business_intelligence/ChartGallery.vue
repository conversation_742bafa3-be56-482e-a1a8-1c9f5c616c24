<template>
  <div class="chart-gallery-container">
    <el-card class="header-card">
      <div class="header-content">
        <div class="header-info">
          <h1>图表展示库</h1>
          <p>查看所有可用的图表类型</p>
        </div>
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </el-card>

    <el-alert
      title="图表展示库"
      description="本页面展示系统支持的所有图表类型，您可以在创建报表时选择这些图表类型。"
      type="info"
      show-icon
      :closable="false"
      style="margin: 20px 0;"
    />

    <el-divider content-position="left">基础图表</el-divider>

    <el-row :gutter="16">
      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <el-card class="chart-card">
          <template #header>
            <span>柱状图</span>
          </template>
          <bar-chart
            :chart-data="chartData.barData"
            :categories="chartData.categories"
            title="销售额统计"
            height="300px"
          />
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <el-card class="chart-card">
          <template #header>
            <span>折线图</span>
          </template>
          <line-chart
            :chart-data="chartData.lineData"
            :categories="chartData.timeCategories"
            title="销售趋势"
            height="300px"
          />
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <el-card class="chart-card">
          <template #header>
            <span>饼图</span>
          </template>
          <pie-chart
            :chart-data="chartData.pieData"
            title="销售占比"
            height="300px"
          />
        </el-card>
      </el-col>
    </el-row>
    
    <a-divider>高级图表</a-divider>
    
    <a-row :gutter="[16, 16]">
      <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <a-card title="雷达图" :bordered="false">
          <radar-chart
            :data="chartData.radarData"
            :indicators="chartData.radarIndicators"
            title="门店能力评估"
            height="300px"
          />
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <a-card title="热力图" :bordered="false">
          <heatmap-chart
            :data="chartData.heatmapData"
            :x-categories="chartData.weekDays"
            :y-categories="chartData.timeSlots"
            title="预约热度分布"
            height="300px"
          />
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <a-card title="散点图" :bordered="false">
          <scatter-chart
            :data="chartData.scatterData"
            title="客户价值分布"
            height="300px"
          />
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <a-card title="漏斗图" :bordered="false">
          <funnel-chart
            :data="chartData.funnelData"
            title="客户转化漏斗"
            height="300px"
          />
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <a-card title="仪表盘" :bordered="false">
          <gauge-chart
            :value="chartData.gaugeValue"
            :min="0"
            :max="100"
            title="目标完成率"
            height="300px"
          />
        </a-card>
      </a-col>
    </a-row>
    
    <a-divider>新增高级图表</a-divider>
    
    <a-row :gutter="[16, 16]">
      <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <a-card title="桑基图" :bordered="false">
          <sankey-chart
            :nodes="chartData.sankeyNodes"
            :links="chartData.sankeyLinks"
            title="客户流向分析"
            height="400px"
          />
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <a-card title="树图" :bordered="false">
          <tree-chart
            :data="chartData.treeData"
            title="组织架构图"
            height="400px"
          />
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <a-card title="旭日图" :bordered="false">
          <sunburst-chart
            :data="chartData.sunburstData"
            title="销售层级占比"
            height="400px"
          />
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <a-card title="箱线图" :bordered="false">
          <box-plot-chart
            :data="chartData.boxplotData"
            :categories="chartData.boxplotCategories"
            title="门店业绩分布"
            height="400px"
          />
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <a-card title="词云图" :bordered="false">
          <word-cloud-chart
            :data="chartData.wordcloudData"
            title="客户评价关键词"
            height="400px"
          />
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { defineComponent, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'

// 导入所有图表组件
import {
  BarChart,
  LineChart,
  PieChart,
  RadarChart,
  HeatmapChart,
  ScatterChart,
  FunnelChart,
  GaugeChart,
  SankeyChart,
  TreeChart,
  SunburstChart,
  BoxPlotChart,
  WordCloudChart
} from '@/components/charts'

export default defineComponent({
  name: 'ChartGallery',
  components: {
    Refresh,
    BarChart,
    LineChart,
    PieChart,
    RadarChart,
    HeatmapChart,
    ScatterChart,
    FunnelChart,
    GaugeChart,
    SankeyChart,
    TreeChart,
    SunburstChart,
    BoxPlotChart,
    WordCloudChart
  },
  setup() {
    // 图表数据
    const chartData = reactive({
      // 基础图表数据
      categories: ['门店A', '门店B', '门店C', '门店D', '门店E'],
      timeCategories: ['1月', '2月', '3月', '4月', '5月', '6月'],
      barData: [
        { name: '销售额', data: [5000, 7000, 3600, 8900, 6700] }
      ],
      lineData: [
        { name: '销售额', data: [5000, 7000, 3600, 8900, 6700, 9200] },
        { name: '利润', data: [2500, 3500, 1800, 4450, 3350, 4600] }
      ],
      pieData: [
        { name: '门店A', value: 5000 },
        { name: '门店B', value: 7000 },
        { name: '门店C', value: 3600 },
        { name: '门店D', value: 8900 },
        { name: '门店E', value: 6700 }
      ],
      
      // 高级图表数据
      radarIndicators: [
        { name: '服务质量', max: 100 },
        { name: '环境', max: 100 },
        { name: '技师水平', max: 100 },
        { name: '客户满意度', max: 100 },
        { name: '性价比', max: 100 }
      ],
      radarData: [
        { name: '门店A', value: [90, 85, 95, 88, 82] },
        { name: '门店B', value: [85, 92, 80, 85, 90] }
      ],
      weekDays: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      timeSlots: ['9:00', '10:00', '11:00', '14:00', '15:00', '16:00', '17:00'],
      heatmapData: [
        [0, 0, 5], [0, 1, 7], [0, 2, 3], [0, 3, 5], [0, 4, 2], [0, 5, 8], [0, 6, 9],
        [1, 0, 6], [1, 1, 4], [1, 2, 7], [1, 3, 3], [1, 4, 5], [1, 5, 7], [1, 6, 8],
        [2, 0, 3], [2, 1, 5], [2, 2, 6], [2, 3, 8], [2, 4, 4], [2, 5, 6], [2, 6, 7],
        [3, 0, 4], [3, 1, 6], [3, 2, 5], [3, 3, 7], [3, 4, 9], [3, 5, 5], [3, 6, 4],
        [4, 0, 7], [4, 1, 8], [4, 2, 6], [4, 3, 4], [4, 4, 5], [4, 5, 9], [4, 6, 10],
        [5, 0, 5], [5, 1, 7], [5, 2, 9], [5, 3, 6], [5, 4, 8], [5, 5, 10], [5, 6, 12],
        [6, 0, 6], [6, 1, 8], [6, 2, 7], [6, 3, 5], [6, 4, 7], [6, 5, 9], [6, 6, 11]
      ],
      scatterData: [
        { name: '客户A', value: [3500, 85] },
        { name: '客户B', value: [5200, 92] },
        { name: '客户C', value: [1800, 75] },
        { name: '客户D', value: [4300, 88] },
        { name: '客户E', value: [2700, 80] },
        { name: '客户F', value: [6100, 95] },
        { name: '客户G', value: [3200, 82] },
        { name: '客户H', value: [4800, 90] },
        { name: '客户I', value: [2200, 78] },
        { name: '客户J', value: [5500, 93] }
      ],
      funnelData: [
        { name: '访问', value: 1000 },
        { name: '咨询', value: 800 },
        { name: '预约', value: 600 },
        { name: '到店', value: 500 },
        { name: '成交', value: 400 },
        { name: '复购', value: 200 }
      ],
      gaugeValue: 75,
      
      // 新增高级图表数据
      sankeyNodes: [
        { name: '新客户' },
        { name: '老客户' },
        { name: '门店A' },
        { name: '门店B' },
        { name: '门店C' },
        { name: '按摩服务' },
        { name: '推拿服务' },
        { name: 'SPA服务' }
      ],
      sankeyLinks: [
        { source: '新客户', target: '门店A', value: 30 },
        { source: '新客户', target: '门店B', value: 25 },
        { source: '新客户', target: '门店C', value: 20 },
        { source: '老客户', target: '门店A', value: 40 },
        { source: '老客户', target: '门店B', value: 35 },
        { source: '老客户', target: '门店C', value: 30 },
        { source: '门店A', target: '按摩服务', value: 35 },
        { source: '门店A', target: '推拿服务', value: 20 },
        { source: '门店A', target: 'SPA服务', value: 15 },
        { source: '门店B', target: '按摩服务', value: 25 },
        { source: '门店B', target: '推拿服务', value: 25 },
        { source: '门店B', target: 'SPA服务', value: 10 },
        { source: '门店C', target: '按摩服务', value: 20 },
        { source: '门店C', target: '推拿服务', value: 15 },
        { source: '门店C', target: 'SPA服务', value: 15 }
      ],
      treeData: {
        name: '总部',
        children: [
          {
            name: '华南区',
            children: [
              { name: '广州门店', value: 15 },
              { name: '深圳门店', value: 12 },
              { name: '佛山门店', value: 8 }
            ]
          },
          {
            name: '华东区',
            children: [
              { name: '上海门店', value: 20 },
              { name: '杭州门店', value: 15 },
              { name: '南京门店', value: 10 }
            ]
          },
          {
            name: '华北区',
            children: [
              { name: '北京门店', value: 18 },
              { name: '天津门店', value: 12 }
            ]
          }
        ]
      },
      sunburstData: [
        {
          name: '华南区',
          children: [
            {
              name: '广州门店',
              value: 1500000,
              children: [
                { name: '按摩服务', value: 800000 },
                { name: '推拿服务', value: 400000 },
                { name: 'SPA服务', value: 300000 }
              ]
            },
            {
              name: '深圳门店',
              value: 1200000,
              children: [
                { name: '按摩服务', value: 600000 },
                { name: '推拿服务', value: 350000 },
                { name: 'SPA服务', value: 250000 }
              ]
            }
          ]
        },
        {
          name: '华东区',
          children: [
            {
              name: '上海门店',
              value: 2000000,
              children: [
                { name: '按摩服务', value: 1000000 },
                { name: '推拿服务', value: 600000 },
                { name: 'SPA服务', value: 400000 }
              ]
            },
            {
              name: '杭州门店',
              value: 1500000,
              children: [
                { name: '按摩服务', value: 800000 },
                { name: '推拿服务', value: 400000 },
                { name: 'SPA服务', value: 300000 }
              ]
            }
          ]
        }
      ],
      boxplotCategories: ['门店A', '门店B', '门店C', '门店D', '门店E'],
      boxplotData: [
        [2500, 3500, 4500, 6000, 8000], // 门店A [min, Q1, median, Q3, max]
        [3000, 4000, 5000, 6500, 9000], // 门店B
        [2000, 3000, 4000, 5500, 7500], // 门店C
        [3500, 4500, 5500, 7000, 9500], // 门店D
        [2800, 3800, 4800, 6300, 8500]  // 门店E
      ],
      wordcloudData: [
        { name: '专业', value: 100 },
        { name: '舒适', value: 85 },
        { name: '环境好', value: 70 },
        { name: '服务周到', value: 65 },
        { name: '技术一流', value: 60 },
        { name: '价格合理', value: 55 },
        { name: '干净整洁', value: 50 },
        { name: '态度好', value: 45 },
        { name: '效果明显', value: 40 },
        { name: '放松', value: 38 },
        { name: '专注', value: 36 },
        { name: '贴心', value: 34 },
        { name: '推荐', value: 32 },
        { name: '满意', value: 30 },
        { name: '热情', value: 28 },
        { name: '细致', value: 26 },
        { name: '温馨', value: 24 },
        { name: '专注', value: 22 },
        { name: '耐心', value: 20 },
        { name: '精准', value: 18 }
      ]
    })

    // 刷新数据
    const refreshData = () => {
      ElMessage.info('数据刷新中...')
      // 这里可以添加从后端获取最新数据的逻辑
      setTimeout(() => {
        ElMessage.success('数据已刷新')
      }, 1000)
    }

    // 页面加载时初始化
    onMounted(() => {
      // 可以在这里从后端获取数据
    })

    return {
      chartData,
      refreshData
    }
  }
})
</script>

<style scoped>
.chart-gallery-container {
  padding: 20px;
}

.header-card {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.chart-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

@media (max-width: 768px) {
  .chart-gallery-container {
    padding: 10px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}
</style>