<template>
  <div class="multi-dimensional-analysis-container">
    <el-card class="analysis-header">
      <div class="analysis-title">
        <h1>{{ reportConfig.title || '多维度分析报表' }}</h1>
        <div class="analysis-filters">
          <el-select v-model="storeId" placeholder="选择门店" clearable style="width: 150px; margin-right: 10px;">
            <el-option v-for="store in stores" :key="store.id" :label="store.name" :value="store.id" />
          </el-select>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            style="width: 300px; margin-right: 10px;"
          />
          <el-button type="primary" @click="openTemplates">选择模板</el-button>
        </div>
      </div>
    </el-card>

    <!-- 报表配置 -->
    <el-card class="analysis-card">
      <template #header>
        <div class="card-header">
          <h2>报表配置</h2>
          <div class="config-actions">
            <el-button type="primary" @click="generateReport">生成报表</el-button>
            <el-button @click="saveConfig">保存配置</el-button>
            <el-button @click="exportReport">导出报表</el-button>
            <el-button type="success" @click="showShareModal">
              <el-icon><Share /></el-icon> 分享
            </el-button>
          </div>
        </div>
      </template>
      <div class="report-config">
        <el-form :model="reportConfig" label-width="120px">
          <el-form-item label="报表名称">
            <el-input v-model="reportConfig.title" placeholder="请输入报表名称" />
          </el-form-item>
          <el-form-item label="数据类型">
            <el-select v-model="reportConfig.dataType" placeholder="选择数据类型" style="width: 100%;">
              <el-option label="订单数据" value="orders" />
              <el-option label="客户数据" value="customers" />
              <el-option label="员工数据" value="employees" />
            </el-select>
          </el-form-item>
          <el-form-item label="分析维度">
            <el-select 
              v-model="reportConfig.dimensions" 
              multiple 
              placeholder="选择分析维度"
              style="width: 100%;"
            >
              <el-option
                v-for="item in dimensionOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="分析指标">
            <el-select 
              v-model="reportConfig.metrics" 
              multiple 
              placeholder="选择分析指标"
              style="width: 100%;"
            >
              <el-option
                v-for="item in metricOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="高级筛选">
            <div v-for="(filter, index) in reportConfig.filters" :key="index" class="filter-item">
              <el-select v-model="filter.field" placeholder="选择字段" style="width: 30%;">
                <el-option label="门店" value="store_id" />
                <el-option label="员工" value="employee_id" />
                <el-option label="会员等级" value="customer_level_id" />
                <el-option label="项目类型" value="item_type" />
                <el-option label="支付方式" value="payment_method" />
              </el-select>
              <el-select v-model="filter.operator" placeholder="运算符" style="width: 20%; margin: 0 10px;">
                <el-option label="等于" value="eq" />
                <el-option label="不等于" value="ne" />
                <el-option label="大于" value="gt" />
                <el-option label="小于" value="lt" />
                <el-option label="包含" value="in" />
              </el-select>
              <el-input v-model="filter.value" placeholder="输入值" style="width: 30%;" />
              <el-button type="danger" icon="Delete" circle @click="removeFilter(index)" style="margin-left: 10px;" />
            </div>
            <el-button type="primary" plain @click="addFilter" style="margin-top: 10px;">添加筛选条件</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 报表结果 -->
    <el-card class="analysis-card" v-loading="loading">
      <template #header>
        <div class="card-header">
          <h2>报表结果</h2>
          <div class="view-selector">
            <el-radio-group v-model="viewType" @change="switchView">
              <el-radio-button label="chart">图表</el-radio-button>
              <el-radio-button label="table">表格</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>
      <div v-if="!reportData.length" class="empty-data">
        <el-empty description="请配置并生成报表"></el-empty>
      </div>
      <div v-else>
        <!-- 图表视图 -->
        <div v-show="viewType === 'chart'" class="chart-container" ref="chartRef"></div>
        
        <!-- 表格视图 -->
        <div v-show="viewType === 'table'" class="table-container">
          <div class="table-actions">
            <el-select v-model="sortField" placeholder="排序字段" style="width: 150px; margin-right: 10px;">
              <el-option
                v-for="col in tableColumns"
                :key="col.prop"
                :label="col.label"
                :value="col.prop"
              />
            </el-select>
            <el-select v-model="sortOrder" placeholder="排序方式" style="width: 120px; margin-right: 10px;">
              <el-option label="升序" value="asc" />
              <el-option label="降序" value="desc" />
            </el-select>
            <el-button @click="sortTable">排序</el-button>
          </div>
          
          <el-table
            :data="reportData"
            style="width: 100%; margin-top: 10px;"
            border
            stripe
            :max-height="500"
          >
            <el-table-column
              v-for="col in tableColumns"
              :key="col.prop"
              :prop="col.prop"
              :label="col.label"
              :formatter="col.formatter"
              :sortable="col.sortable"
            />
          </el-table>
        </div>
      </div>
    </el-card>
    
    <!-- 保存配置对话框 -->
    <el-dialog v-model="saveDialogVisible" title="保存报表配置" width="30%">
      <el-form :model="saveForm" label-width="100px">
        <el-form-item label="配置名称">
          <el-input v-model="saveForm.name" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="saveForm.description" type="textarea" placeholder="请输入配置描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="saveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSave">确认保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 模板选择对话框 -->
    <el-dialog v-model="templateDialogVisible" title="选择报表模板" width="70%">
      <div class="template-dialog-content">
        <div class="template-search">
          <el-input
            v-model="templateSearchQuery"
            placeholder="搜索模板"
            prefix-icon="Search"
            clearable
            style="width: 300px; margin-right: 10px;"
          />
          <el-select v-model="templateCategoryFilter" placeholder="分类筛选" clearable style="width: 150px;">
            <el-option
              v-for="category in templateCategories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </div>
        
        <el-tabs v-model="templateActiveTab">
          <el-tab-pane label="常用模板" name="favorites">
            <el-row :gutter="20">
              <el-col :span="8" v-for="template in filteredFavoriteTemplates" :key="template.id" class="template-col">
                <report-template-card
                  :template="template"
                  :selected="selectedTemplate?.id === template.id"
                  @select="selectTemplate"
                  @use="useTemplate"
                />
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="全部模板" name="all">
            <el-row :gutter="20">
              <el-col :span="8" v-for="template in filteredTemplates" :key="template.id" class="template-col">
                <report-template-card
                  :template="template"
                  :selected="selectedTemplate?.id === template.id"
                  @select="selectTemplate"
                  @use="useTemplate"
                />
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="templateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="applyTemplate">应用模板</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 分享模态框 -->
    <share-report-modal
      v-model:visible="shareModalVisible"
      :reportConfig="analysisConfig"
      reportType="analysis"
      :title="reportConfig.title || '多维度分析报表'"
      @close="shareModalVisible = false"
    />

    <!-- 导出选项模态框 -->
    <export-options-modal
      v-model:visible="exportModalVisible"
      :title="reportConfig.title || '多维度分析报表'"
      :data="reportData"
      :headers="exportHeaders"
      :element-id="'report-container'"
      @export-success="handleExportSuccess"
      @export-error="handleExportError"
    />
  </div>
</template>

<script>
import { ref, reactive, onMounted, nextTick, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import { Share } from '@element-plus/icons-vue'
import { getStores } from '@/api/store'
import biApi from '@/api/business_intelligence'
// 导入报表模板卡片组件
import ReportTemplateCard from '@/components/business_intelligence/ReportTemplateCard.vue'
// 导入导出服务
import exportService from '@/utils/exportService'
// 导入导出模态框组件
import ExportOptionsModal from '@/components/business_intelligence/ExportOptionsModal.vue'

export default {
  name: 'MultiDimensionalAnalysis',
  components: {
    ReportTemplateCard,
    Share,
    ExportOptionsModal
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    
    // 筛选条件
    const storeId = ref('')
    const dateRange = ref([])
    const stores = ref([])
    
    // 报表配置
    const reportConfig = reactive({
      title: '多维度分析报表',
      dataType: 'orders',
      dimensions: ['time_monthly', 'store'],
      metrics: ['total_revenue', 'order_count'],
      filters: []
    })
    
    // 维度和指标选项
    const dimensionOptions = [
      { label: '按天', value: 'time_daily' },
      { label: '按周', value: 'time_weekly' },
      { label: '按月', value: 'time_monthly' },
      { label: '按门店', value: 'store' },
      { label: '按会员等级', value: 'customer_level' },
      { label: '按项目类型', value: 'item_type' },
      { label: '按员工', value: 'employee' },
      { label: '按支付方式', value: 'payment_method' }
    ]
    
    const metricOptions = [
      { label: '订单数', value: 'order_count' },
      { label: '总营收', value: 'total_revenue' },
      { label: '实际营收', value: 'actual_revenue' },
      { label: '优惠金额', value: 'discount_amount' },
      { label: '客户数', value: 'customer_count' },
      { label: '客单价', value: 'avg_order_amount' },
      { label: '服务次数', value: 'service_count' },
      { label: '产品销量', value: 'product_count' }
    ]
    
    // 报表数据和视图控制
    const reportData = ref([])
    const loading = ref(false)
    const viewType = ref('chart')
    const chartRef = ref(null)
    let chart = null
    
    // 表格排序
    const sortField = ref('')
    const sortOrder = ref('desc')
    
    // 保存配置
    const saveDialogVisible = ref(false)
    const saveForm = reactive({
      name: '',
      description: ''
    })
    
    // 计算表格列
    const tableColumns = computed(() => {
      const columns = []
      
      // 添加维度列
      if (reportConfig.dimensions.includes('time_daily') || 
          reportConfig.dimensions.includes('time_weekly') || 
          reportConfig.dimensions.includes('time_monthly')) {
        columns.push({
          prop: 'time',
          label: '时间',
          sortable: true
        })
      }
      
      if (reportConfig.dimensions.includes('store')) {
        columns.push({
          prop: 'store_name',
          label: '门店',
          sortable: true
        })
      }
      
      if (reportConfig.dimensions.includes('customer_level')) {
        columns.push({
          prop: 'level_name',
          label: '会员等级',
          sortable: true
        })
      }
      
      if (reportConfig.dimensions.includes('item_type')) {
        columns.push({
          prop: 'item_type',
          label: '项目类型',
          sortable: true
        })
      }
      
      if (reportConfig.dimensions.includes('employee')) {
        columns.push({
          prop: 'employee_name',
          label: '员工',
          sortable: true
        })
      }
      
      if (reportConfig.dimensions.includes('payment_method')) {
        columns.push({
          prop: 'payment_method',
          label: '支付方式',
          sortable: true
        })
      }
      
      // 添加指标列
      if (reportConfig.metrics.includes('order_count')) {
        columns.push({
          prop: 'order_count',
          label: '订单数',
          sortable: true
        })
      }
      
      if (reportConfig.metrics.includes('total_revenue')) {
        columns.push({
          prop: 'total_revenue',
          label: '总营收',
          formatter: (row) => `¥${row.total_revenue.toFixed(2)}`,
          sortable: true
        })
      }
      
      if (reportConfig.metrics.includes('actual_revenue')) {
        columns.push({
          prop: 'actual_revenue',
          label: '实际营收',
          formatter: (row) => `¥${row.actual_revenue.toFixed(2)}`,
          sortable: true
        })
      }
      
      if (reportConfig.metrics.includes('discount_amount')) {
        columns.push({
          prop: 'discount_amount',
          label: '优惠金额',
          formatter: (row) => `¥${row.discount_amount.toFixed(2)}`,
          sortable: true
        })
      }
      
      if (reportConfig.metrics.includes('customer_count')) {
        columns.push({
          prop: 'customer_count',
          label: '客户数',
          sortable: true
        })
      }
      
      if (reportConfig.metrics.includes('avg_order_amount')) {
        columns.push({
          prop: 'avg_order_amount',
          label: '客单价',
          formatter: (row) => `¥${row.avg_order_amount.toFixed(2)}`,
          sortable: true
        })
      }
      
      if (reportConfig.metrics.includes('service_count')) {
        columns.push({
          prop: 'service_count',
          label: '服务次数',
          sortable: true
        })
      }
      
      if (reportConfig.metrics.includes('product_count')) {
        columns.push({
          prop: 'product_count',
          label: '产品销量',
          sortable: true
        })
      }
      
      return columns
    })
    
    // 获取门店列表
    const fetchStores = async () => {
      try {
        const res = await getStores()
        stores.value = res.items
      } catch (error) {
        console.error('获取门店列表失败:', error)
        ElMessage.error('获取门店列表失败')
      }
    }
    
    // 添加筛选条件
    const addFilter = () => {
      reportConfig.filters.push({
        field: '',
        operator: 'eq',
        value: ''
      })
    }
    
    // 移除筛选条件
    const removeFilter = (index) => {
      reportConfig.filters.splice(index, 1)
    }
    
    // 生成报表
    const generateReport = async () => {
      if (reportConfig.dimensions.length === 0) {
        ElMessage.warning('请至少选择一个维度')
        return
      }
      
      if (reportConfig.metrics.length === 0) {
        ElMessage.warning('请至少选择一个指标')
        return
      }
      
      loading.value = true
      
      try {
        const params = {
          data_type: reportConfig.dataType,
          dimensions: reportConfig.dimensions,
          metrics: reportConfig.metrics,
          filters: reportConfig.filters
        }
        
        if (storeId.value) params.store_ids = [storeId.value]
        if (dateRange.value && dateRange.value.length === 2) {
          params.start_date = dateRange.value[0]
          params.end_date = dateRange.value[1]
        }
        
        const res = await biApi.getMultiDimensionalAnalysis(params)
        reportData.value = res.data
        
        nextTick(() => {
          if (viewType.value === 'chart') {
            renderChart()
          }
        })
      } catch (error) {
        console.error('生成报表失败:', error)
        ElMessage.error('生成报表失败')
      } finally {
        loading.value = false
      }
    }
    
    // 切换视图
    const switchView = () => {
      if (viewType.value === 'chart' && reportData.value.length > 0) {
        nextTick(() => {
          renderChart()
        })
      }
    }
    
    // 渲染图表
    const renderChart = () => {
      if (!chartRef.value) return
      
      if (!chart) {
        chart = echarts.init(chartRef.value)
      }
      
      // 根据选择的维度和指标，动态生成图表配置
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: reportConfig.metrics.map(metric => {
            const metricObj = metricOptions.find(m => m.value === metric)
            return metricObj ? metricObj.label : metric
          })
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        }
      }
      
      // 处理坐标轴
      if (reportConfig.dimensions.includes('time_daily') || 
          reportConfig.dimensions.includes('time_weekly') || 
          reportConfig.dimensions.includes('time_monthly')) {
        // 时间维度作为X轴
        option.xAxis = {
          type: 'category',
          data: [...new Set(reportData.value.map(item => item.time))].sort()
        }
        option.yAxis = {
          type: 'value'
        }
      } else if (reportConfig.dimensions.includes('store')) {
        // 门店维度作为X轴
        option.xAxis = {
          type: 'category',
          data: [...new Set(reportData.value.map(item => item.store_name))]
        }
        option.yAxis = {
          type: 'value'
        }
      } else {
        // 其他维度，使用条形图
        option.yAxis = {
          type: 'category',
          data: reportConfig.dimensions.includes('customer_level') 
            ? [...new Set(reportData.value.map(item => item.level_name))]
            : [...new Set(reportData.value.map(item => item.item_type))]
        }
        option.xAxis = {
          type: 'value'
        }
      }
      
      // 处理系列
      option.series = reportConfig.metrics.map(metric => {
        const metricObj = metricOptions.find(m => m.value === metric)
        const seriesName = metricObj ? metricObj.label : metric
        
        let seriesData = []
        if (option.xAxis && option.xAxis.type === 'category') {
          // 对于类别X轴，按X轴数据顺序提取指标值
          seriesData = option.xAxis.data.map(xValue => {
            const item = reportData.value.find(d => {
              if (reportConfig.dimensions.includes('time_daily') || 
                  reportConfig.dimensions.includes('time_weekly') || 
                  reportConfig.dimensions.includes('time_monthly')) {
                return d.time === xValue
              } else {
                return d.store_name === xValue
              }
            })
            return item ? item[metric] : 0
          })
        } else {
          // 对于类别Y轴，按Y轴数据顺序提取指标值
          seriesData = option.yAxis.data.map(yValue => {
            const item = reportData.value.find(d => {
              if (reportConfig.dimensions.includes('customer_level')) {
                return d.level_name === yValue
              } else {
                return d.item_type === yValue
              }
            })
            return item ? item[metric] : 0
          })
        }
        
        return {
          name: seriesName,
          type: ['order_count', 'customer_count', 'service_count', 'product_count'].includes(metric) ? 'bar' : 'line',
          data: seriesData,
          label: {
            show: true,
            position: 'top',
            formatter: function(params) {
              if (['total_revenue', 'actual_revenue', 'discount_amount', 'avg_order_amount'].includes(metric)) {
                return `¥${params.value.toFixed(2)}`
              }
              return params.value
            }
          }
        }
      })
      
      chart.setOption(option)
    }
    
    // 表格排序
    const sortTable = () => {
      if (!sortField.value) return
      
      reportData.value.sort((a, b) => {
        if (sortOrder.value === 'asc') {
          return a[sortField.value] > b[sortField.value] ? 1 : -1
        } else {
          return a[sortField.value] < b[sortField.value] ? 1 : -1
        }
      })
    }
    
    // 保存配置
    const saveConfig = () => {
      saveForm.name = reportConfig.title
      saveDialogVisible.value = true
    }
    
    // 确认保存配置
    const confirmSave = async () => {
      if (!saveForm.name) {
        ElMessage.warning('请输入配置名称')
        return
      }
      
      try {
        // 这里可以调用API保存配置
        ElMessage.success('配置保存成功')
        saveDialogVisible.value = false
      } catch (error) {
        console.error('保存配置失败:', error)
        ElMessage.error('保存配置失败')
      }
    }
    
    // 导出相关
    const exportModalVisible = ref(false)
    const exportHeaders = ref([])
    
    // 导出报表
    const exportReport = () => {
      if (reportData.value.length === 0) {
        ElMessage.warning('没有数据可导出')
        return
      }
      
      // 准备表头
      exportHeaders.value = []
      
      // 添加维度列
      if (reportConfig.dimensions.includes('time_daily') || 
          reportConfig.dimensions.includes('time_weekly') || 
          reportConfig.dimensions.includes('time_monthly')) {
        exportHeaders.value.push({ key: 'time', label: '时间' })
      }
      
      if (reportConfig.dimensions.includes('store')) {
        exportHeaders.value.push({ key: 'store_name', label: '门店' })
      }
      
      if (reportConfig.dimensions.includes('customer_level')) {
        exportHeaders.value.push({ key: 'level_name', label: '会员等级' })
      }
      
      if (reportConfig.dimensions.includes('item_type')) {
        exportHeaders.value.push({ key: 'item_type', label: '项目类型' })
      }
      
      if (reportConfig.dimensions.includes('employee')) {
        exportHeaders.value.push({ key: 'employee_name', label: '员工' })
      }
      
      if (reportConfig.dimensions.includes('payment_method')) {
        exportHeaders.value.push({ key: 'payment_method', label: '支付方式' })
      }
      
      // 添加指标列
      if (reportConfig.metrics.includes('order_count')) {
        exportHeaders.value.push({ key: 'order_count', label: '订单数' })
      }
      
      if (reportConfig.metrics.includes('total_revenue')) {
        exportHeaders.value.push({ key: 'total_revenue', label: '总营收' })
      }
      
      if (reportConfig.metrics.includes('actual_revenue')) {
        exportHeaders.value.push({ key: 'actual_revenue', label: '实际营收' })
      }
      
      if (reportConfig.metrics.includes('discount_amount')) {
        exportHeaders.value.push({ key: 'discount_amount', label: '优惠金额' })
      }
      
      if (reportConfig.metrics.includes('customer_count')) {
        exportHeaders.value.push({ key: 'customer_count', label: '客户数' })
      }
      
      if (reportConfig.metrics.includes('avg_order_amount')) {
        exportHeaders.value.push({ key: 'avg_order_amount', label: '客单价' })
      }
      
      if (reportConfig.metrics.includes('service_count')) {
        exportHeaders.value.push({ key: 'service_count', label: '服务次数' })
      }
      
      if (reportConfig.metrics.includes('product_count')) {
        exportHeaders.value.push({ key: 'product_count', label: '产品销量' })
      }
      
      // 显示导出选项模态框
      exportModalVisible.value = true
    }
    
    // 处理导出成功
    const handleExportSuccess = (type) => {
      ElMessage.success(`导出${type}成功`)
    }
    
    // 处理导出失败
    const handleExportError = (type, error) => {
      console.error(`导出${type}失败:`, error)
      ElMessage.error(`导出${type}失败`)
    }
    
    // 监听窗口大小变化，调整图表大小
    const handleResize = () => {
      if (chart) chart.resize()
    }
    
    // 模板相关
    const templateDialogVisible = ref(false)
    const templateSearchQuery = ref('')
    const templateCategoryFilter = ref('')
    const templateActiveTab = ref('favorites')
    const selectedTemplate = ref(null)
    
    // 模板数据
    const templates = ref([
      {
        id: '1',
        name: '销售趋势分析',
        description: '按时间维度分析销售趋势，包括营收、订单数和客单价',
        category: '销售分析',
        tags: ['趋势', '营收', '基础'],
        icon: 'TrendCharts',
        favorite: true,
        dimensions: ['time_monthly'],
        metrics: ['total_revenue', 'order_count', 'avg_order_amount']
      },
      {
        id: '2',
        name: '门店销售对比',
        description: '对比不同门店的销售业绩，包括营收、客流量和转化率',
        category: '门店分析',
        tags: ['对比', '门店', '业绩'],
        icon: 'House',
        favorite: true,
        dimensions: ['store'],
        metrics: ['total_revenue', 'customer_count', 'avg_order_amount']
      },
      {
        id: '3',
        name: '员工业绩排行',
        description: '分析员工业绩排名，包括业绩金额、服务次数和客户评分',
        category: '员工分析',
        tags: ['排名', '员工', '业绩'],
        icon: 'User',
        favorite: false,
        dimensions: ['employee'],
        metrics: ['total_revenue', 'service_count']
      },
      {
        id: '4',
        name: '会员等级消费分析',
        description: '分析不同会员等级的消费行为和偏好',
        category: '客户分析',
        tags: ['会员', '消费', '等级'],
        icon: 'UserFilled',
        favorite: true,
        dimensions: ['customer_level'],
        metrics: ['total_revenue', 'order_count', 'avg_order_amount']
      },
      {
        id: '5',
        name: '项目销售排行',
        description: '分析热门项目销售情况，包括销售额、销售量和客单价',
        category: '项目分析',
        tags: ['排名', '项目', '热门'],
        icon: 'List',
        favorite: false,
        dimensions: ['item_type'],
        metrics: ['total_revenue', 'service_count', 'product_count']
      },
      {
        id: '6',
        name: '支付方式分析',
        description: '分析不同支付方式的使用情况和趋势',
        category: '销售分析',
        tags: ['支付', '趋势'],
        icon: 'Money',
        favorite: false,
        dimensions: ['payment_method'],
        metrics: ['total_revenue', 'order_count']
      },
      {
        id: '7',
        name: '每周客流分析',
        description: '分析一周内各时段的客流量分布情况',
        category: '客户分析',
        tags: ['客流', '时段', '分布'],
        icon: 'Timer',
        favorite: false,
        dimensions: ['time_daily'],
        metrics: ['customer_count', 'order_count']
      },
      {
        id: '8',
        name: '门店月度业绩对比',
        description: '对比分析各门店的月度业绩变化',
        category: '门店分析',
        tags: ['月度', '对比', '业绩'],
        icon: 'Calendar',
        favorite: false,
        dimensions: ['store', 'time_monthly'],
        metrics: ['total_revenue', 'customer_count']
      }
    ])
    
    // 计算模板分类
    const templateCategories = computed(() => {
      return [...new Set(templates.value.map(template => template.category))]
    })
    
    // 计算收藏模板
    const favoriteTemplates = computed(() => {
      return templates.value.filter(template => template.favorite)
    })
    
    // 计算过滤后的收藏模板
    const filteredFavoriteTemplates = computed(() => {
      let result = favoriteTemplates.value
      
      if (templateSearchQuery.value) {
        const query = templateSearchQuery.value.toLowerCase()
        result = result.filter(template => 
          template.name.toLowerCase().includes(query) || 
          template.description.toLowerCase().includes(query) ||
          template.tags.some(tag => tag.toLowerCase().includes(query))
        )
      }
      
      if (templateCategoryFilter.value) {
        result = result.filter(template => template.category === templateCategoryFilter.value)
      }
      
      return result
    })
    
    // 计算过滤后的全部模板
    const filteredTemplates = computed(() => {
      let result = templates.value
      
      if (templateSearchQuery.value) {
        const query = templateSearchQuery.value.toLowerCase()
        result = result.filter(template => 
          template.name.toLowerCase().includes(query) || 
          template.description.toLowerCase().includes(query) ||
          template.tags.some(tag => tag.toLowerCase().includes(query))
        )
      }
      
      if (templateCategoryFilter.value) {
        result = result.filter(template => template.category === templateCategoryFilter.value)
      }
      
      return result
    })
    
    // 打开模板选择对话框
    const openTemplates = () => {
      templateDialogVisible.value = true
    }
    
    // 选择模板
    const selectTemplate = (template) => {
      selectedTemplate.value = template
    }
    
    // 使用模板
    const useTemplate = (template) => {
      applyTemplateConfig(template)
      templateDialogVisible.value = false
    }
    
    // 应用选中的模板
    const applyTemplate = () => {
      if (!selectedTemplate.value) {
        ElMessage.warning('请先选择一个模板')
        return
      }
      
      applyTemplateConfig(selectedTemplate.value)
      templateDialogVisible.value = false
    }
    
    // 应用模板配置
    const applyTemplateConfig = (template) => {
      reportConfig.title = template.name
      reportConfig.dimensions = [...template.dimensions]
      reportConfig.metrics = [...template.metrics]
      
      ElMessage.success(`已应用模板: ${template.name}`)
    }
    
    // 分享功能相关
    const shareModalVisible = ref(false)
    const analysisConfig = computed(() => {
      return {
        title: reportConfig.title || '多维度分析报表',
        description: '多维度分析报表',
        dimensions: reportConfig.dimensions.map(d => {
          return {
            type: d,
            granularity: d === 'time' ? 'month' : null
          }
        }),
        measures: reportConfig.metrics,
        filters: reportConfig.filters.map(f => {
          return {
            field: f.field,
            operator: f.operator,
            value: f.value
          }
        }),
        data: reportData,
        charts: [
          {
            title: '分析图表',
            span: 24,
            height: 400,
            chartType: viewType.value, // 使用当前视图类型
            data: reportData
          }
        ]
      }
    })
    
    // 显示分享模态框
    const showShareModal = () => {
      if (reportData.value.length === 0) {
        ElMessage.warning('请先生成报表再分享')
        return
      }
      shareModalVisible.value = true
    }

    // 导出函数
    const exportToExcel = () => {
      if (reportData.value.length === 0) {
        ElMessage.warning('没有数据可导出')
        return
      }
      try {
        exportService.exportToExcel(reportData.value, '多维度分析报表')
        ElMessage.success('导出Excel成功')
      } catch (error) {
        console.error('导出Excel失败:', error)
        ElMessage.error('导出Excel失败')
      }
    }

    const exportToCSV = () => {
      if (reportData.value.length === 0) {
        ElMessage.warning('没有数据可导出')
        return
      }
      try {
        exportService.exportToCSV(reportData.value, '多维度分析报表')
        ElMessage.success('导出CSV成功')
      } catch (error) {
        console.error('导出CSV失败:', error)
        ElMessage.error('导出CSV失败')
      }
    }

    const exportToPDF = () => {
      if (reportData.value.length === 0) {
        ElMessage.warning('没有数据可导出')
        return
      }
      try {
        exportService.exportToPDF('multi-dimensional-analysis-container', '多维度分析报表')
        ElMessage.success('导出PDF成功')
      } catch (error) {
        console.error('导出PDF失败:', error)
        ElMessage.error('导出PDF失败')
      }
    }

    const exportToImage = () => {
      if (!chartRef.value) {
        ElMessage.warning('没有图表可导出')
        return
      }
      try {
        const canvas = chart.getDataURL({
          type: 'png',
          pixelRatio: 2,
          backgroundColor: '#fff'
        })
        const link = document.createElement('a')
        link.download = '多维度分析图表.png'
        link.href = canvas
        link.click()
        ElMessage.success('导出图片成功')
      } catch (error) {
        console.error('导出图片失败:', error)
        ElMessage.error('导出图片失败')
      }
    }
    
    // 生命周期钩子
    onMounted(() => {
      fetchStores()
      
      // 检查是否有模板参数
      const templateId = route.query.template
      if (templateId) {
        const template = templates.value.find(t => t.id === templateId)
        if (template) {
          applyTemplateConfig(template)
        }
      }
      
      window.addEventListener('resize', handleResize)
    })
    
    // 监听视图变化
    watch(viewType, (newVal) => {
      if (newVal === 'chart' && reportData.value.length > 0) {
        nextTick(() => {
          renderChart()
        })
      }
    })
    
    return {
      storeId,
      dateRange,
      stores,
      reportConfig,
      dimensionOptions,
      metricOptions,
      reportData,
      loading,
      viewType,
      chartRef,
      tableColumns,
      sortField,
      sortOrder,
      saveDialogVisible,
      saveForm,
      addFilter,
      removeFilter,
      generateReport,
      switchView,
      sortTable,
      saveConfig,
      confirmSave,
      exportReport,
      
      // 模板相关
      templateDialogVisible,
      templateSearchQuery,
      templateCategoryFilter,
      templateActiveTab,
      selectedTemplate,
      templates,
      templateCategories,
      favoriteTemplates,
      filteredFavoriteTemplates,
      filteredTemplates,
      openTemplates,
      selectTemplate,
      useTemplate,
      applyTemplate,
      exportToExcel,
      exportToCSV,
      exportToPDF,
      exportToImage,
      
      // 分享相关
      shareModalVisible,
      analysisConfig,
      showShareModal,

      // 导出相关
      exportModalVisible,
      exportHeaders,
      handleExportSuccess,
      handleExportError
    }
  }
}
</script>

<style scoped>
.multi-dimensional-analysis-container {
  padding: 20px;
}

.analysis-header {
  margin-bottom: 20px;
}

.analysis-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.analysis-title h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.analysis-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.report-config {
  padding: 10px 0;
}

.filter-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.chart-container {
  height: 500px;
}

.table-container {
  margin-top: 20px;
}

.table-actions {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.template-dialog-content {
  max-height: 600px;
  overflow-y: auto;
}

.template-search {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.template-col {
  margin-bottom: 20px;
}

.el-button + .el-button {
  margin-left: 10px;
}
</style> 