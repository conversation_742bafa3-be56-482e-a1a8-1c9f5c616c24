<template>
  <div class="member-levels-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>会员等级管理</h1>
      <p>管理会员等级、设置权益和升级规则</p>
    </div>

    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="showCreateDialog">
        <el-icon><Plus /></el-icon>
        新建等级
      </el-button>
      <el-button @click="refreshData">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 等级列表 - 改为表格形式 -->
    <div class="levels-table">
      <el-table
        :data="levels"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="level_order" label="等级" width="80" align="center">
          <template #default="{ row }">
            <el-tag type="primary" size="small">{{ row.level_order }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="等级名称" width="120" />

        <el-table-column label="升级条件" width="200">
          <template #default="{ row }">
            <div class="condition-info">
              <div>充值: ¥{{ parseFloat(row.min_recharge_amount || 0).toFixed(2) }}</div>
              <div>消费: ¥{{ parseFloat(row.min_consumption_amount || 0).toFixed(2) }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="会员权益" width="180">
          <template #default="{ row }">
            <div class="benefits-info">
              <div>折扣: {{ row.discount_percentage || ((1 - parseFloat(row.discount_rate || 1)) * 100).toFixed(0) }}% OFF</div>
              <div>积分: {{ parseFloat(row.points_multiplier || 1).toFixed(2) }}x</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />

        <el-table-column label="专享权益" width="200">
          <template #default="{ row }">
            <div class="benefits-tags" v-if="row.benefits_detail && row.benefits_detail.length > 0">
              <el-tag
                v-for="benefit in row.benefits_detail.slice(0, 2)"
                :key="benefit.id"
                size="small"
                type="success"
                style="margin: 2px;"
              >
                {{ benefit.benefit_display }}
              </el-tag>
              <el-tag
                v-if="row.benefits_detail.length > 2"
                size="small"
                type="info"
                style="margin: 2px;"
              >
                +{{ row.benefits_detail.length - 2 }}
              </el-tag>
            </div>
            <span v-else class="text-muted">无</span>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
              {{ row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="showEditDialog(row)"
              :icon="Edit"
            >
              编辑
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="showBenefitsDialog(row)"
              :icon="Setting"
            >
              权益
            </el-button>
            <el-dropdown @command="(command) => handleDropdownCommand(command, row)">
              <el-button type="info" size="small" :icon="MoreFilled">
                更多
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="toggle">
                    {{ row.is_active ? '禁用' : '启用' }}
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="isEdit ? '编辑会员等级' : '新建会员等级'"
      width="600px"
    >
      <el-form 
        ref="formRef" 
        :model="form" 
        :rules="rules" 
        label-width="120px"
      >
        <el-form-item label="等级名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入等级名称" />
        </el-form-item>

        <el-form-item label="等级顺序" prop="level_order">
          <el-input-number 
            v-model="form.level_order" 
            :min="1" 
            :max="100"
            placeholder="数字越大等级越高"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="充值门槛" prop="min_recharge_amount">
              <el-input-number 
                v-model="form.min_recharge_amount" 
                :min="0" 
                :precision="2"
                placeholder="最低充值金额"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="消费门槛" prop="min_consumption_amount">
              <el-input-number 
                v-model="form.min_consumption_amount" 
                :min="0" 
                :precision="2"
                placeholder="最低消费金额"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="折扣率" prop="discount_rate">
              <el-input-number 
                v-model="form.discount_rate" 
                :min="0" 
                :max="1"
                :precision="4"
                :step="0.01"
                placeholder="1.0为无折扣，0.9为9折"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="积分倍率" prop="points_multiplier">
              <el-input-number 
                v-model="form.points_multiplier" 
                :min="0" 
                :precision="2"
                :step="0.1"
                placeholder="积分倍率"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="等级描述" prop="description">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入等级描述"
          />
        </el-form-item>

        <el-form-item label="是否启用" prop="is_active">
          <el-switch v-model="form.is_active" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 权益管理对话框 -->
    <BenefitsDialog 
      v-model="benefitsDialogVisible"
      :level-id="selectedLevelId"
      :level-name="selectedLevelName"
      @refresh="refreshData"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, MoreFilled, Edit, Setting } from '@element-plus/icons-vue'
import { memberLevelApi } from '@/api/member-level'
import BenefitsDialog from './components/BenefitsDialog.vue'

// 响应式数据
const levels = ref([])
const loading = ref(false)
const dialogVisible = ref(false)
const benefitsDialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const selectedLevelId = ref(null)
const selectedLevelName = ref('')

// 表单数据
const form = reactive({
  name: '',
  level_order: 1,
  min_recharge_amount: 0,
  min_consumption_amount: 0,
  discount_rate: 1.0000,
  points_multiplier: 1.00,
  description: '',
  is_active: true
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入等级名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  level_order: [
    { required: true, message: '请输入等级顺序', trigger: 'blur' }
  ],
  discount_rate: [
    { required: true, message: '请输入折扣率', trigger: 'blur' }
  ],
  points_multiplier: [
    { required: true, message: '请输入积分倍率', trigger: 'blur' }
  ]
}

const formRef = ref()

// 方法
const fetchLevels = async () => {
  try {
    loading.value = true
    const response = await memberLevelApi.getLevels()
    levels.value = response.items || []
  } catch (error) {
    ElMessage.error('获取会员等级失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  fetchLevels()
}

const showCreateDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

const showEditDialog = (level) => {
  isEdit.value = true
  Object.assign(form, {
    id: level.id,
    name: level.name,
    level_order: level.level_order,
    min_recharge_amount: parseFloat(level.min_recharge_amount),
    min_consumption_amount: parseFloat(level.min_consumption_amount),
    discount_rate: parseFloat(level.discount_rate),
    points_multiplier: parseFloat(level.points_multiplier),
    description: level.description,
    is_active: level.is_active
  })
  dialogVisible.value = true
}

const resetForm = () => {
  Object.assign(form, {
    name: '',
    level_order: 1,
    min_recharge_amount: 0,
    min_consumption_amount: 0,
    discount_rate: 1.0000,
    points_multiplier: 1.00,
    description: '',
    is_active: true
  })
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true
    
    if (isEdit.value) {
      await memberLevelApi.updateLevel(form.id, form)
      ElMessage.success('会员等级更新成功')
    } else {
      await memberLevelApi.createLevel(form)
      ElMessage.success('会员等级创建成功')
    }
    
    dialogVisible.value = false
    refreshData()
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    submitting.value = false
  }
}

const handleCommand = (command) => {
  const [action, id] = command.split('-')
  const level = levels.value.find(l => l.id == id)

  switch (action) {
    case 'edit':
      showEditDialog(level)
      break
    case 'benefits':
      selectedLevelId.value = parseInt(id)
      selectedLevelName.value = level.name
      benefitsDialogVisible.value = true
      break
    case 'toggle':
      toggleLevel(level)
      break
    case 'delete':
      deleteLevel(level)
      break
  }
}

// 新增：处理表格中的下拉菜单命令
const handleDropdownCommand = (command, level) => {
  switch (command) {
    case 'toggle':
      toggleLevel(level)
      break
    case 'delete':
      deleteLevel(level)
      break
  }
}

// 新增：显示权益管理对话框
const showBenefitsDialog = (level) => {
  selectedLevelId.value = level.id
  selectedLevelName.value = level.name
  benefitsDialogVisible.value = true
}

const toggleLevel = async (level) => {
  try {
    await memberLevelApi.updateLevel(level.id, { is_active: !level.is_active })
    ElMessage.success(`会员等级已${level.is_active ? '禁用' : '启用'}`)
    refreshData()
  } catch (error) {
    ElMessage.error('操作失败: ' + error.message)
  }
}

const deleteLevel = async (level) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除会员等级"${level.name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await memberLevelApi.deleteLevel(level.id)
    ElMessage.success('会员等级删除成功')
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

// 生命周期
onMounted(() => {
  fetchLevels()
})
</script>

<style scoped>
.member-levels-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.toolbar {
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
}

.levels-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.condition-info {
  font-size: 13px;
  line-height: 1.4;
}

.condition-info div {
  margin-bottom: 2px;
}

.benefits-info {
  font-size: 13px;
  line-height: 1.4;
}

.benefits-info div {
  margin-bottom: 2px;
}

.benefits-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.text-muted {
  color: #909399;
  font-size: 13px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
