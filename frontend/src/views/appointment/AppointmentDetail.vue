<template>
  <div class="appointment-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" type="text" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h1>预约详情</h1>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="editAppointment" v-if="appointment.status !== 'completed' && appointment.status !== 'cancelled'">
          <el-icon><Edit /></el-icon>
          编辑预约
        </el-button>
        <el-dropdown @command="handleCommand">
          <el-button>
            <el-icon><More /></el-icon>
            更多操作
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="confirm" v-if="appointment.status === 'pending'">确认预约</el-dropdown-item>
              <el-dropdown-item command="complete" v-if="appointment.status === 'confirmed'">完成服务</el-dropdown-item>
              <el-dropdown-item command="cancel" v-if="appointment.status !== 'completed' && appointment.status !== 'cancelled'">取消预约</el-dropdown-item>
              <el-dropdown-item command="reschedule" v-if="appointment.status !== 'completed' && appointment.status !== 'cancelled'">重新安排</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 预约详情内容 -->
    <div v-else class="appointment-content">
      <!-- 基本信息卡片 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>预约信息</span>
            <el-tag :type="getStatusType(appointment.status)" effect="dark">
              {{ getStatusText(appointment.status) }}
            </el-tag>
          </div>
        </template>
        
        <div class="appointment-basic-info">
          <div class="info-grid">
            <div class="info-item">
              <span class="label">预约号</span>
              <span class="value">{{ appointment.appointmentNo }}</span>
            </div>
            <div class="info-item">
              <span class="label">预约时间</span>
              <span class="value">{{ formatDateTime(appointment.appointmentTime) }}</span>
            </div>
            <div class="info-item">
              <span class="label">服务项目</span>
              <span class="value">{{ appointment.service }}</span>
            </div>
            <div class="info-item">
              <span class="label">服务技师</span>
              <span class="value">{{ appointment.technician }}</span>
            </div>
            <div class="info-item">
              <span class="label">服务时长</span>
              <span class="value">{{ appointment.duration || 60 }}分钟</span>
            </div>
            <div class="info-item">
              <span class="label">服务价格</span>
              <span class="value">¥{{ appointment.price || 298 }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 客户信息卡片 -->
      <el-card class="info-card">
        <template #header>
          <span>客户信息</span>
        </template>
        
        <div class="customer-info">
          <div class="info-grid">
            <div class="info-item">
              <span class="label">客户姓名</span>
              <span class="value">{{ appointment.customerName || '张三' }}</span>
            </div>
            <div class="info-item">
              <span class="label">联系电话</span>
              <span class="value">{{ appointment.customerPhone || '13800138001' }}</span>
            </div>
            <div class="info-item">
              <span class="label">会员等级</span>
              <span class="value">{{ appointment.memberLevel || '金卡会员' }}</span>
            </div>
            <div class="info-item">
              <span class="label">预约来源</span>
              <span class="value">{{ appointment.source || '线上预约' }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 备注信息 -->
      <el-card class="info-card" v-if="appointment.notes">
        <template #header>
          <span>备注信息</span>
        </template>
        <div class="notes-content">
          {{ appointment.notes }}
        </div>
      </el-card>

      <!-- 操作记录 -->
      <el-card class="info-card">
        <template #header>
          <span>操作记录</span>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="record in operationRecords"
            :key="record.id"
            :timestamp="formatDateTime(record.time)"
            placement="top"
          >
            <div class="operation-record">
              <div class="operation-title">{{ record.operation }}</div>
              <div class="operation-operator">操作人：{{ record.operator }}</div>
              <div class="operation-remark" v-if="record.remark">{{ record.remark }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft, Edit, More
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const appointment = ref({
  id: null,
  appointmentNo: '',
  appointmentTime: '',
  service: '',
  technician: '',
  status: 'pending',
  customerName: '',
  customerPhone: '',
  memberLevel: '',
  source: '',
  notes: '',
  duration: 60,
  price: 298
})

const operationRecords = ref([])

// 方法
const formatDateTime = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString()
}

const getStatusType = (status) => {
  const types = {
    pending: 'warning',
    confirmed: 'success',
    completed: 'success',
    cancelled: 'danger',
    no_show: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待确认',
    confirmed: '已确认',
    completed: '已完成',
    cancelled: '已取消',
    no_show: '未到店'
  }
  return texts[status] || '未知'
}

const goBack = () => {
  router.go(-1)
}

const editAppointment = () => {
  ElMessage.info('编辑预约功能开发中...')
}

const handleCommand = (command) => {
  switch (command) {
    case 'confirm':
      confirmAppointment()
      break
    case 'complete':
      completeAppointment()
      break
    case 'cancel':
      cancelAppointment()
      break
    case 'reschedule':
      rescheduleAppointment()
      break
  }
}

const confirmAppointment = async () => {
  try {
    await ElMessageBox.confirm('确定要确认这个预约吗？', '确认预约', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    appointment.value.status = 'confirmed'
    ElMessage.success('预约已确认')
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认预约失败:', error)
      ElMessage.error('确认预约失败')
    }
  }
}

const completeAppointment = async () => {
  try {
    await ElMessageBox.confirm('确定要完成这个预约吗？', '完成服务', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    appointment.value.status = 'completed'
    ElMessage.success('服务已完成')
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('完成预约失败:', error)
      ElMessage.error('完成预约失败')
    }
  }
}

const cancelAppointment = async () => {
  try {
    await ElMessageBox.confirm('确定要取消这个预约吗？此操作不可恢复。', '取消预约', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    appointment.value.status = 'cancelled'
    ElMessage.success('预约已取消')
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消预约失败:', error)
      ElMessage.error('取消预约失败')
    }
  }
}

const rescheduleAppointment = () => {
  ElMessage.info('重新安排预约功能开发中...')
}

// 加载预约详情
const loadAppointmentDetail = async () => {
  const appointmentId = route.params.id
  if (!appointmentId) {
    ElMessage.error('预约ID不存在')
    router.push('/customers')
    return
  }

  loading.value = true
  try {
    // 这里应该调用API获取预约详情
    // const response = await getAppointmentDetail(appointmentId)
    // appointment.value = response.data
    
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    appointment.value = {
      id: appointmentId,
      appointmentNo: 'APT202401200001',
      appointmentTime: '2024-01-20 15:00:00',
      service: '全身按摩',
      technician: '李师傅',
      status: 'confirmed',
      customerName: '张三',
      customerPhone: '13800138001',
      memberLevel: '金卡会员',
      source: '线上预约',
      notes: '客户要求轻柔一些，有颈椎问题',
      duration: 90,
      price: 298
    }
    
    operationRecords.value = [
      {
        id: 1,
        operation: '创建预约',
        operator: '系统',
        time: '2024-01-19 10:30:00',
        remark: '客户线上预约'
      },
      {
        id: 2,
        operation: '确认预约',
        operator: '前台小王',
        time: '2024-01-19 11:00:00',
        remark: '已联系客户确认'
      }
    ]

  } catch (error) {
    console.error('加载预约详情失败:', error)
    ElMessage.error('加载预约详情失败')
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  loadAppointmentDetail()
})
</script>

<style lang="scss" scoped>
.appointment-detail {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .back-button {
      padding: 8px;
      
      .el-icon {
        margin-right: 4px;
      }
    }
    
    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .header-right {
    display: flex;
    gap: 12px;
  }
}

.loading-container {
  padding: 24px;
}

.appointment-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
  }
}

.appointment-basic-info,
.customer-info {
  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    
    .info-item {
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      .label {
        font-size: 14px;
        color: #909399;
      }
      
      .value {
        font-size: 16px;
        color: #303133;
        font-weight: 500;
      }
    }
  }
}

.notes-content {
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 6px;
  color: #606266;
  line-height: 1.6;
}

.operation-record {
  .operation-title {
    font-weight: 600;
    color: #303133;
    margin-bottom: 4px;
  }
  
  .operation-operator {
    font-size: 14px;
    color: #909399;
    margin-bottom: 4px;
  }
  
  .operation-remark {
    font-size: 14px;
    color: #606266;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .appointment-basic-info,
  .customer-info {
    .info-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
