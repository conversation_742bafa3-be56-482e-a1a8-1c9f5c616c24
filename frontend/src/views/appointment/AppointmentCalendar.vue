<template>
  <div class="appointment-calendar">
    <div class="filter-container">
      <el-select v-model="storeId" placeholder="选择门店" clearable @change="fetchAppointments">
        <el-option v-for="store in stores" :key="store.id" :label="store.name" :value="store.id" />
      </el-select>
      <el-select v-model="employeeId" placeholder="选择技师" clearable @change="fetchAppointments">
        <el-option v-for="employee in employees" :key="employee.id" :label="employee.name" :value="employee.id" />
      </el-select>
      <el-button type="primary" @click="handleAddAppointment">新增预约</el-button>
    </div>

    <el-calendar v-model="currentDate">
      <template #dateCell="{ data }">
        <div class="calendar-day">
          <p>{{ data.day.split('-').slice(2).join('-') }}</p>
          <div class="appointment-list">
            <div
              v-for="appointment in getAppointmentsByDate(data.day)"
              :key="appointment.id"
              class="appointment-item"
              :class="getStatusClass(appointment.status)"
              @click="handleAppointmentClick(appointment)"
            >
              {{ formatTime(appointment.start_time) }} - {{ appointment.customer.name }} - {{ appointment.store_item.name }}
            </div>
          </div>
        </div>
      </template>
    </el-calendar>

    <!-- 预约详情对话框 -->
    <el-dialog
      :title="dialogType === 'add' ? '新增预约' : '预约详情'"
      v-model="dialogVisible"
      width="600px"
      :before-close="handleDialogClose"
    >
      <el-form :model="appointmentForm" :rules="rules" ref="appointmentForm" label-width="100px">
        <el-form-item label="门店" prop="store_id">
          <el-select v-model="appointmentForm.store_id" placeholder="选择门店" :disabled="dialogType === 'view'">
            <el-option v-for="store in stores" :key="store.id" :label="store.name" :value="store.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="客户" prop="customer_id">
          <el-select 
            v-model="appointmentForm.customer_id" 
            placeholder="选择客户" 
            :disabled="dialogType === 'view'"
            filterable
            remote
            :remote-method="searchCustomers"
            :loading="customerLoading"
          >
            <el-option v-for="customer in customers" :key="customer.id" :label="customer.name" :value="customer.id">
              <span>{{ customer.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ customer.phone }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="技师" prop="employee_id">
          <el-select v-model="appointmentForm.employee_id" placeholder="选择技师" :disabled="dialogType === 'view'">
            <el-option v-for="employee in employees" :key="employee.id" :label="employee.name" :value="employee.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="服务项目" prop="store_item_id">
          <el-select v-model="appointmentForm.store_item_id" placeholder="选择服务项目" :disabled="dialogType === 'view'">
            <el-option v-for="item in storeItems" :key="item.id" :label="item.group_item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="资源" prop="resource_id">
          <el-select v-model="appointmentForm.resource_id" placeholder="选择资源(可选)" clearable :disabled="dialogType === 'view'">
            <el-option v-for="resource in resources" :key="resource.id" :label="resource.name" :value="resource.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间" prop="start_time">
          <el-date-picker
            v-model="appointmentForm.start_time"
            type="datetime"
            placeholder="选择开始时间"
            :disabled="dialogType === 'view'"
          />
        </el-form-item>
        <el-form-item label="结束时间" prop="end_time">
          <el-date-picker
            v-model="appointmentForm.end_time"
            type="datetime"
            placeholder="选择结束时间"
            :disabled="dialogType === 'view'"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status" v-if="dialogType !== 'add'">
          <el-select v-model="appointmentForm.status" placeholder="选择状态" :disabled="dialogType === 'view'">
            <el-option label="待到店" value="pending" />
            <el-option label="已到店" value="arrived" />
            <el-option label="服务中" value="in_service" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
            <el-option label="未到店" value="no_show" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="appointmentForm.remark" type="textarea" :rows="3" placeholder="请输入备注" :disabled="dialogType === 'view'" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSaveAppointment" v-if="dialogType !== 'view'">保存</el-button>
          <el-button type="primary" @click="handleEditAppointment" v-if="dialogType === 'view'">编辑</el-button>
          <el-button type="danger" @click="handleDeleteAppointment" v-if="dialogType === 'view'">删除</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getAppointments, getAppointmentById, createAppointment, updateAppointment, deleteAppointment } from '@/api/appointment';
import { getStores } from '@/api/store';
import { getEmployees } from '@/api/employee';
import { getStoreItems } from '@/api/item';
import { getResources } from '@/api/appointment';
import { getCustomerList } from '@/api/customer';

export default {
  name: 'AppointmentCalendar',
  setup() {
    // 日历相关
    const currentDate = ref(new Date());
    
    // 筛选相关
    const storeId = ref('');
    const employeeId = ref('');
    
    // 数据列表
    const appointments = ref([]);
    const stores = ref([]);
    const employees = ref([]);
    const storeItems = ref([]);
    const resources = ref([]);
    const customers = ref([]);
    const customerLoading = ref(false);
    
    // 对话框相关
    const dialogVisible = ref(false);
    const dialogType = ref('add'); // add, edit, view
    const appointmentForm = reactive({
      store_id: '',
      customer_id: '',
      employee_id: '',
      store_item_id: '',
      resource_id: '',
      start_time: '',
      end_time: '',
      status: 'pending',
      remark: '',
      source: 'backend'
    });
    const rules = {
      store_id: [{ required: true, message: '请选择门店', trigger: 'change' }],
      customer_id: [{ required: true, message: '请选择客户', trigger: 'change' }],
      employee_id: [{ required: true, message: '请选择技师', trigger: 'change' }],
      store_item_id: [{ required: true, message: '请选择服务项目', trigger: 'change' }],
      start_time: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
      end_time: [{ required: true, message: '请选择结束时间', trigger: 'change' }]
    };
    const appointmentFormRef = ref(null);
    
    // 获取预约列表
    const fetchAppointments = async () => {
      try {
        const params = {};
        if (storeId.value) params.store_id = storeId.value;
        if (employeeId.value) params.employee_id = employeeId.value;
        
        const res = await getAppointments(params);
        appointments.value = res.data;
      } catch (error) {
        console.error('获取预约列表失败:', error);
        ElMessage.error('获取预约列表失败');
      }
    };
    
    // 获取门店列表
    const fetchStores = async () => {
      try {
        const res = await getStores();
        stores.value = res.data;
      } catch (error) {
        console.error('获取门店列表失败:', error);
        ElMessage.error('获取门店列表失败');
      }
    };
    
    // 获取员工列表
    const fetchEmployees = async () => {
      try {
        const params = {};
        if (storeId.value) params.store_id = storeId.value;
        
        const res = await getEmployees(params);
        employees.value = res.data.filter(emp => emp.role === 'technician');
      } catch (error) {
        console.error('获取员工列表失败:', error);
        ElMessage.error('获取员工列表失败');
      }
    };
    
    // 获取服务项目列表
    const fetchStoreItems = async () => {
      try {
        const params = {};
        if (storeId.value) params.store_id = storeId.value;
        
        const res = await getStoreItems(params);
        storeItems.value = res.data;
      } catch (error) {
        console.error('获取服务项目列表失败:', error);
        ElMessage.error('获取服务项目列表失败');
      }
    };
    
    // 获取资源列表
    const fetchResources = async () => {
      try {
        const params = {};
        if (storeId.value) params.store_id = storeId.value;
        
        const res = await getResources(params);
        resources.value = res.data;
      } catch (error) {
        console.error('获取资源列表失败:', error);
        ElMessage.error('获取资源列表失败');
      }
    };
    
    // 搜索客户
    const searchCustomers = async (query) => {
      if (query) {
        customerLoading.value = true;
        try {
          const res = await getCustomerList({ search: query });
          customers.value = res.data;
        } catch (error) {
          console.error('搜索客户失败:', error);
        } finally {
          customerLoading.value = false;
        }
      } else {
        customers.value = [];
      }
    };
    
    // 根据日期获取预约
    const getAppointmentsByDate = (dateString) => {
      const date = new Date(dateString);
      return appointments.value.filter(appointment => {
        const appointmentDate = new Date(appointment.start_time);
        return (
          appointmentDate.getFullYear() === date.getFullYear() &&
          appointmentDate.getMonth() === date.getMonth() &&
          appointmentDate.getDate() === date.getDate()
        );
      });
    };
    
    // 格式化时间
    const formatTime = (timeString) => {
      const date = new Date(timeString);
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    };
    
    // 获取预约状态对应的样式类
    const getStatusClass = (status) => {
      const statusMap = {
        pending: 'status-pending',
        arrived: 'status-arrived',
        in_service: 'status-in-service',
        completed: 'status-completed',
        cancelled: 'status-cancelled',
        no_show: 'status-no-show'
      };
      return statusMap[status] || '';
    };
    
    // 点击预约
    const handleAppointmentClick = async (appointment) => {
      try {
        const res = await getAppointmentById(appointment.id);
        Object.assign(appointmentForm, res.data);
        dialogType.value = 'view';
        dialogVisible.value = true;
      } catch (error) {
        console.error('获取预约详情失败:', error);
        ElMessage.error('获取预约详情失败');
      }
    };
    
    // 新增预约
    const handleAddAppointment = () => {
      resetForm();
      dialogType.value = 'add';
      dialogVisible.value = true;
      
      // 设置默认值
      if (storeId.value) appointmentForm.store_id = storeId.value;
      if (employeeId.value) appointmentForm.employee_id = employeeId.value;
      
      // 设置默认开始时间为当前时间向上取整到最近的半小时
      const now = new Date();
      const minutes = now.getMinutes();
      const roundedMinutes = minutes < 30 ? 30 : 60;
      now.setMinutes(roundedMinutes);
      now.setSeconds(0);
      now.setMilliseconds(0);
      appointmentForm.start_time = new Date(now);
      
      // 设置默认结束时间为开始时间后1小时
      const end = new Date(now);
      end.setHours(end.getHours() + 1);
      appointmentForm.end_time = new Date(end);
    };
    
    // 编辑预约
    const handleEditAppointment = () => {
      dialogType.value = 'edit';
    };
    
    // 删除预约
    const handleDeleteAppointment = () => {
      ElMessageBox.confirm('确定要删除该预约吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteAppointment(appointmentForm.id);
          ElMessage.success('删除预约成功');
          dialogVisible.value = false;
          fetchAppointments();
        } catch (error) {
          console.error('删除预约失败:', error);
          ElMessage.error('删除预约失败');
        }
      }).catch(() => {});
    };
    
    // 保存预约
    const handleSaveAppointment = async () => {
      if (!appointmentFormRef.value) return;
      
      appointmentFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            if (dialogType.value === 'add') {
              await createAppointment(appointmentForm);
              ElMessage.success('新增预约成功');
            } else {
              await updateAppointment(appointmentForm.id, appointmentForm);
              ElMessage.success('更新预约成功');
            }
            dialogVisible.value = false;
            fetchAppointments();
          } catch (error) {
            console.error('保存预约失败:', error);
            ElMessage.error(error.response?.data?.detail || '保存预约失败');
          }
        }
      });
    };
    
    // 关闭对话框
    const handleDialogClose = () => {
      dialogVisible.value = false;
      resetForm();
    };
    
    // 重置表单
    const resetForm = () => {
      Object.keys(appointmentForm).forEach(key => {
        appointmentForm[key] = '';
      });
      appointmentForm.status = 'pending';
      appointmentForm.source = 'backend';
      
      if (appointmentFormRef.value) {
        appointmentFormRef.value.resetFields();
      }
    };
    
    onMounted(() => {
      fetchAppointments();
      fetchStores();
      fetchEmployees();
      fetchStoreItems();
      fetchResources();
    });
    
    return {
      currentDate,
      storeId,
      employeeId,
      appointments,
      stores,
      employees,
      storeItems,
      resources,
      customers,
      customerLoading,
      dialogVisible,
      dialogType,
      appointmentForm,
      rules,
      appointmentFormRef,
      fetchAppointments,
      getAppointmentsByDate,
      formatTime,
      getStatusClass,
      handleAppointmentClick,
      handleAddAppointment,
      handleEditAppointment,
      handleDeleteAppointment,
      handleSaveAppointment,
      handleDialogClose,
      searchCustomers
    };
  }
};
</script>

<style scoped>
.appointment-calendar {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.calendar-day {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.appointment-list {
  flex: 1;
  overflow-y: auto;
  font-size: 12px;
}

.appointment-item {
  margin: 2px 0;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.appointment-item:hover {
  opacity: 0.8;
}

/* 预约状态样式 */
.status-pending {
  background-color: #e6f7ff;
  border-left: 3px solid #1890ff;
}

.status-arrived {
  background-color: #f6ffed;
  border-left: 3px solid #52c41a;
}

.status-in-service {
  background-color: #fff7e6;
  border-left: 3px solid #fa8c16;
}

.status-completed {
  background-color: #f9f9f9;
  border-left: 3px solid #8c8c8c;
}

.status-cancelled {
  background-color: #fff1f0;
  border-left: 3px solid #f5222d;
}

.status-no-show {
  background-color: #fff0f6;
  border-left: 3px solid #eb2f96;
}
</style> 