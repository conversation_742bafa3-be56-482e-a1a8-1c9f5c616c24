<template>
  <div class="shareholder-equity-detail-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h1>股权管理详情</h1>
        <p v-if="shareholderInfo">{{ shareholderInfo.name }} 的股权结构配置</p>
      </div>
      <div class="header-actions">
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <el-button type="primary" @click="showAddEquityDialog">
          <el-icon><Plus /></el-icon>
          新增股权
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 股东基本信息 -->
    <el-card shadow="never" class="shareholder-info-card" v-if="shareholderInfo">
      <template #header>
        <span>股东基本信息</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="info-item">
            <label>股东姓名：</label>
            <span>{{ shareholderInfo.name }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <label>手机号：</label>
            <span>{{ shareholderInfo.phone }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <label>身份证号：</label>
            <span>{{ shareholderInfo.id_card_number || '未填写' }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <label>创建时间：</label>
            <span>{{ formatDate(shareholderInfo.created_at) }}</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 股权结构概览 -->
    <el-row :gutter="20" class="equity-overview">
      <el-col :span="6">
        <el-card shadow="never" class="stat-card">
          <div class="stat-number">{{ equityStats.totalStores }}</div>
          <div class="stat-label">参股门店数</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="never" class="stat-card">
          <div class="stat-number">{{ equityStats.totalInvestment.toFixed(2) }}</div>
          <div class="stat-label">总投资金额（万元）</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="never" class="stat-card">
          <div class="stat-number">{{ equityStats.averageEquityRatio.toFixed(2) }}%</div>
          <div class="stat-label">平均股权比例</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="never" class="stat-card">
          <div class="stat-number">{{ equityStats.totalDividend.toFixed(2) }}</div>
          <div class="stat-label">累计分红（万元）</div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能选项卡 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="equity-tabs">
      <!-- 股权结构 -->
      <el-tab-pane label="股权结构" name="structure">
        <div class="tab-content">
          <!-- 搜索和筛选 -->
          <div class="filter-section">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-input
                  v-model="filters.search"
                  placeholder="搜索门店名称"
                  clearable
                  @input="handleSearch"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </el-col>
              <el-col :span="4">
                <el-select v-model="filters.status" placeholder="状态" clearable @change="handleFilter">
                  <el-option label="有效" value="active" />
                  <el-option label="无效" value="inactive" />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-button @click="resetFilters">重置</el-button>
                <el-button type="primary" @click="exportEquityData">导出</el-button>
              </el-col>
            </el-row>
          </div>

          <!-- 股权结构列表 -->
          <div class="table-container">
            <el-table
              :data="filteredEquityList"
              v-loading="loading"
              stripe
              border
              style="width: 100%"
            >
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="store_name" label="门店名称" min-width="150" />
              <el-table-column prop="equity_ratio" label="股权比例" width="120">
                <template #default="{ row }">
                  <span class="equity-ratio">{{ row.equity_ratio }}%</span>
                </template>
              </el-table-column>
              <el-table-column prop="investment_amount" label="投资金额" width="120">
                <template #default="{ row }">
                  <span class="investment-amount">¥{{ Number(row.investment_amount || 0).toFixed(2) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="investment_date" label="投资日期" width="120">
                <template #default="{ row }">
                  {{ formatDate(row.investment_date) }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                    {{ row.status === 'active' ? '有效' : '无效' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="notes" label="备注" min-width="200" show-overflow-tooltip />
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                  <el-button size="small" @click="handleEditEquity(row)">编辑</el-button>
                  <el-button size="small" type="info" @click="viewEquityDetail(row)">详情</el-button>
                  <el-button size="small" type="danger" @click="handleDeleteEquity(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="pagination.page"
                v-model:page-size="pagination.size"
                :page-sizes="[10, 20, 50, 100]"
                :total="pagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handlePageSizeChange"
                @current-change="handlePageChange"
              />
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 分红记录 -->
      <el-tab-pane label="分红记录" name="dividend">
        <div class="tab-content">
          <div class="dividend-summary">
            <h3>分红统计</h3>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="summary-item">
                  <div class="summary-value">{{ dividendStats.totalAmount.toFixed(2) }}</div>
                  <div class="summary-label">累计分红金额（万元）</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="summary-item">
                  <div class="summary-value">{{ dividendStats.lastYearAmount.toFixed(2) }}</div>
                  <div class="summary-label">去年分红金额（万元）</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="summary-item">
                  <div class="summary-value">{{ dividendStats.averageROI.toFixed(2) }}%</div>
                  <div class="summary-label">平均投资回报率</div>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 分红记录表格 -->
          <div class="table-container">
            <el-table
              :data="dividendList"
              v-loading="dividendLoading"
              stripe
              border
              style="width: 100%"
            >
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="store_name" label="门店名称" min-width="150" />
              <el-table-column prop="dividend_period" label="分红期间" width="150" />
              <el-table-column prop="dividend_amount" label="分红金额" width="120">
                <template #default="{ row }">
                  <span class="dividend-amount">¥{{ Number(row.dividend_amount || 0).toFixed(2) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="dividend_ratio" label="分红比例" width="100">
                <template #default="{ row }">
                  {{ row.dividend_ratio }}%
                </template>
              </el-table-column>
              <el-table-column prop="dividend_date" label="分红日期" width="120">
                <template #default="{ row }">
                  {{ formatDate(row.dividend_date) }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getDividendStatusType(row.status)">
                    {{ getDividendStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" fixed="right">
                <template #default="{ row }">
                  <el-button size="small" type="info" @click="viewDividendDetail(row)">详情</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-tab-pane>

      <!-- 股权变更历史 -->
      <el-tab-pane label="变更历史" name="history">
        <div class="tab-content">
          <div class="table-container">
            <el-table
              :data="historyList"
              v-loading="historyLoading"
              stripe
              border
              style="width: 100%"
            >
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="change_type" label="变更类型" width="120">
                <template #default="{ row }">
                  <el-tag :type="getChangeTypeColor(row.change_type)">
                    {{ getChangeTypeText(row.change_type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="store_name" label="门店名称" min-width="150" />
              <el-table-column prop="old_equity_ratio" label="原股权比例" width="120">
                <template #default="{ row }">
                  {{ row.old_equity_ratio || '-' }}%
                </template>
              </el-table-column>
              <el-table-column prop="new_equity_ratio" label="新股权比例" width="120">
                <template #default="{ row }">
                  {{ row.new_equity_ratio || '-' }}%
                </template>
              </el-table-column>
              <el-table-column prop="change_reason" label="变更原因" min-width="200" show-overflow-tooltip />
              <el-table-column prop="change_date" label="变更日期" width="120">
                <template #default="{ row }">
                  {{ formatDate(row.change_date) }}
                </template>
              </el-table-column>
              <el-table-column prop="operator" label="操作人" width="100" />
            </el-table>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 新增/编辑股权对话框 -->
    <el-dialog
      v-model="equityDialogVisible"
      :title="equityDialogType === 'add' ? '新增股权' : '编辑股权'"
      width="600px"
      destroy-on-close
    >
      <el-form
        ref="equityFormRef"
        :model="equityForm"
        :rules="equityRules"
        label-width="120px"
      >
        <el-form-item label="门店" prop="store_id">
          <el-select v-model="equityForm.store_id" placeholder="请选择门店" style="width: 100%">
            <el-option v-for="store in stores" :key="store.id" :label="store.name" :value="store.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="股权比例" prop="equity_ratio">
          <el-input-number v-model="equityForm.equity_ratio" :min="0" :max="100" :precision="2" style="width: 100%" />
          <span style="margin-left: 8px;">%</span>
        </el-form-item>
        <el-form-item label="投资金额" prop="investment_amount">
          <el-input-number v-model="equityForm.investment_amount" :min="0" :precision="2" style="width: 100%" />
          <span style="margin-left: 8px;">万元</span>
        </el-form-item>
        <el-form-item label="投资日期" prop="investment_date">
          <el-date-picker
            v-model="equityForm.investment_date"
            type="date"
            placeholder="选择投资日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch v-model="equityForm.is_active" active-text="有效" inactive-text="无效" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="equityForm.notes" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="equityDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEquityForm" :loading="equitySubmitting">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Plus, Refresh, Search } from '@element-plus/icons-vue'
import shareholderApi from '@/api/shareholder'
import storeApi from '@/api/store'

// 路由
const route = useRoute()
const router = useRouter()

// 响应式数据
const shareholderId = ref(route.params.id)
const activeTab = ref('structure')
const loading = ref(false)
const dividendLoading = ref(false)
const historyLoading = ref(false)

// 股东信息
const shareholderInfo = ref(null)

// 股权结构数据
const equityList = ref([])
const filteredEquityList = ref([])
const filters = reactive({
  search: '',
  status: ''
})
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 分红记录数据
const dividendList = ref([])

// 变更历史数据
const historyList = ref([])

// 门店列表
const stores = ref([])

// 股权对话框
const equityDialogVisible = ref(false)
const equityDialogType = ref('add')
const equitySubmitting = ref(false)
const equityFormRef = ref()
const equityForm = reactive({
  store_id: null,
  equity_ratio: 0,
  investment_amount: 0,
  investment_date: null,
  is_active: true,
  notes: ''
})

// 表单验证规则
const equityRules = {
  store_id: [{ required: true, message: '请选择门店', trigger: 'change' }],
  equity_ratio: [{ required: true, message: '请输入股权比例', trigger: 'blur' }],
  investment_amount: [{ required: true, message: '请输入投资金额', trigger: 'blur' }],
  investment_date: [{ required: true, message: '请选择投资日期', trigger: 'change' }]
}

// 计算属性
const equityStats = computed(() => {
  const stats = {
    totalStores: equityList.value.length,
    totalInvestment: 0,
    averageEquityRatio: 0,
    totalDividend: 0
  }
  
  if (equityList.value.length > 0) {
    stats.totalInvestment = equityList.value.reduce((sum, item) => sum + Number(item.investment_amount || 0), 0)
    stats.averageEquityRatio = equityList.value.reduce((sum, item) => sum + Number(item.equity_ratio || 0), 0) / equityList.value.length
  }
  
  if (dividendList.value.length > 0) {
    stats.totalDividend = dividendList.value.reduce((sum, item) => sum + Number(item.dividend_amount || 0), 0)
  }
  
  return stats
})

const dividendStats = computed(() => {
  const stats = {
    totalAmount: 0,
    lastYearAmount: 0,
    averageROI: 0
  }
  
  if (dividendList.value.length > 0) {
    stats.totalAmount = dividendList.value.reduce((sum, item) => sum + Number(item.dividend_amount || 0), 0)
    
    const lastYear = new Date().getFullYear() - 1
    stats.lastYearAmount = dividendList.value
      .filter(item => new Date(item.dividend_date).getFullYear() === lastYear)
      .reduce((sum, item) => sum + Number(item.dividend_amount || 0), 0)
    
    if (equityStats.value.totalInvestment > 0) {
      stats.averageROI = (stats.totalAmount / equityStats.value.totalInvestment) * 100
    }
  }
  
  return stats
})

// 生命周期
onMounted(() => {
  fetchShareholderInfo()
  fetchStores()
  fetchEquityList()
})

// 方法
const goBack = () => {
  router.go(-1)
}

const handleTabClick = (tab) => {
  if (tab.name === 'dividend') {
    fetchDividendList()
  } else if (tab.name === 'history') {
    fetchHistoryList()
  }
}

// 获取股东信息
const fetchShareholderInfo = async () => {
  try {
    const response = await shareholderApi.getShareholderDetail(shareholderId.value)
    shareholderInfo.value = response
  } catch (error) {
    ElMessage.error('获取股东信息失败: ' + error.message)
  }
}

// 获取门店列表
const fetchStores = async () => {
  try {
    const response = await storeApi.getStoreList()
    stores.value = response || []
  } catch (error) {
    ElMessage.error('获取门店列表失败: ' + error.message)
  }
}

// 获取股权结构列表
const fetchEquityList = async () => {
  loading.value = true
  try {
    const params = {
      shareholder_id: shareholderId.value,
      page: pagination.page,
      size: pagination.size
    }
    const response = await shareholderApi.getShareholderStructureList(params)
    equityList.value = response.items || []
    pagination.total = response.total || 0
    applyFilters()
  } catch (error) {
    ElMessage.error('获取股权结构失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 获取分红记录
const fetchDividendList = async () => {
  dividendLoading.value = true
  try {
    // 这里需要实现分红记录API
    dividendList.value = []
  } catch (error) {
    ElMessage.error('获取分红记录失败: ' + error.message)
  } finally {
    dividendLoading.value = false
  }
}

// 获取变更历史
const fetchHistoryList = async () => {
  historyLoading.value = true
  try {
    // 这里需要实现变更历史API
    historyList.value = []
  } catch (error) {
    ElMessage.error('获取变更历史失败: ' + error.message)
  } finally {
    historyLoading.value = false
  }
}

// 应用筛选
const applyFilters = () => {
  let filtered = [...equityList.value]
  
  if (filters.search) {
    filtered = filtered.filter(item => 
      item.store_name && item.store_name.toLowerCase().includes(filters.search.toLowerCase())
    )
  }
  
  if (filters.status) {
    const isActive = filters.status === 'active'
    filtered = filtered.filter(item => item.is_active === isActive)
  }
  
  filteredEquityList.value = filtered
}

// 搜索
const handleSearch = () => {
  applyFilters()
}

// 筛选
const handleFilter = () => {
  applyFilters()
}

// 重置筛选
const resetFilters = () => {
  Object.assign(filters, {
    search: '',
    status: ''
  })
  applyFilters()
}

// 刷新数据
const refreshData = () => {
  fetchShareholderInfo()
  fetchEquityList()
  if (activeTab.value === 'dividend') {
    fetchDividendList()
  } else if (activeTab.value === 'history') {
    fetchHistoryList()
  }
}

// 显示新增股权对话框
const showAddEquityDialog = () => {
  equityDialogType.value = 'add'
  resetEquityForm()
  equityDialogVisible.value = true
}

// 重置股权表单
const resetEquityForm = () => {
  Object.assign(equityForm, {
    store_id: null,
    equity_ratio: 0,
    investment_amount: 0,
    investment_date: null,
    is_active: true,
    notes: ''
  })
  if (equityFormRef.value) {
    equityFormRef.value.resetFields()
  }
}

// 提交股权表单
const submitEquityForm = async () => {
  if (!equityFormRef.value) return
  
  await equityFormRef.value.validate(async (valid) => {
    if (valid) {
      equitySubmitting.value = true
      try {
        const formData = {
          ...equityForm,
          shareholder_id: shareholderId.value,
          status: equityForm.is_active ? 'active' : 'inactive'
        }
        
        if (equityDialogType.value === 'add') {
          await shareholderApi.createShareholderStructure(formData)
          ElMessage.success('新增成功')
        } else {
          await shareholderApi.updateShareholderStructure(equityForm.id, formData)
          ElMessage.success('更新成功')
        }
        
        equityDialogVisible.value = false
        fetchEquityList()
      } catch (error) {
        ElMessage.error('保存失败: ' + error.message)
      } finally {
        equitySubmitting.value = false
      }
    }
  })
}

// 编辑股权
const handleEditEquity = (row) => {
  equityDialogType.value = 'edit'
  Object.assign(equityForm, {
    ...row,
    is_active: row.status === 'active'
  })
  equityDialogVisible.value = true
}

// 删除股权
const handleDeleteEquity = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该股权记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await shareholderApi.deleteShareholderStructure(row.id)
    ElMessage.success('删除成功')
    fetchEquityList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

// 分页处理
const handlePageChange = (page) => {
  pagination.page = page
  fetchEquityList()
}

const handlePageSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  fetchEquityList()
}

// 工具方法
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

const getDividendStatusType = (status) => {
  const statusMap = {
    'paid': 'success',
    'pending': 'warning',
    'cancelled': 'danger'
  }
  return statusMap[status] || 'info'
}

const getDividendStatusText = (status) => {
  const statusMap = {
    'paid': '已支付',
    'pending': '待支付',
    'cancelled': '已取消'
  }
  return statusMap[status] || '未知'
}

const getChangeTypeColor = (type) => {
  const typeMap = {
    'add': 'success',
    'update': 'warning',
    'delete': 'danger'
  }
  return typeMap[type] || 'info'
}

const getChangeTypeText = (type) => {
  const typeMap = {
    'add': '新增',
    'update': '修改',
    'delete': '删除'
  }
  return typeMap[type] || '未知'
}

// 占位方法
const viewEquityDetail = (row) => {
  ElMessage.info('股权详情功能开发中...')
}

const viewDividendDetail = (row) => {
  ElMessage.info('分红详情功能开发中...')
}

const exportEquityData = () => {
  ElMessage.info('导出功能开发中...')
}
</script>

<style scoped>
.shareholder-equity-detail-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.shareholder-info-card {
  margin-bottom: 20px;
}

.info-item {
  margin-bottom: 10px;
}

.info-item label {
  font-weight: 600;
  color: #606266;
}

.equity-overview {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.equity-tabs {
  margin-top: 20px;
}

.tab-content {
  padding: 20px 0;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.pagination-container {
  padding: 20px;
  text-align: right;
  background: white;
  border-top: 1px solid #ebeef5;
}

.equity-ratio {
  font-weight: 600;
  color: #409eff;
}

.investment-amount {
  font-weight: 600;
  color: #e6a23c;
}

.dividend-amount {
  font-weight: 600;
  color: #67c23a;
}

.dividend-summary {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.dividend-summary h3 {
  margin: 0 0 20px 0;
  color: #303133;
}

.summary-item {
  text-align: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #67c23a;
  margin-bottom: 8px;
}

.summary-label {
  color: #909399;
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-tabs__item) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.el-table th) {
  background-color: #fafafa;
}
</style>
