<template>
  <div class="shareholder-detail-container">
    <!-- 返回按钮 -->
    <div class="back-button">
      <el-button @click="goBack" plain>
        <el-icon><Back /></el-icon>返回列表
      </el-button>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>
    
    <!-- 详情内容 -->
    <template v-else>
      <!-- 基本信息卡片 -->
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <span>股东基本信息</span>
            <el-button type="primary" @click="handleEdit" plain>编辑</el-button>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="股东姓名">{{ shareholderInfo.name }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ shareholderInfo.phone }}</el-descriptions-item>
          <el-descriptions-item label="身份证号">{{ shareholderInfo.id_card_number || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(shareholderInfo.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ shareholderInfo.remarks || '无' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 股权结构卡片 -->
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <span>股权结构 ({{ shareholderInfo.structures?.length || 0 }})</span>
            <el-button type="primary" @click="handleAddEquity" plain>添加股权</el-button>
          </div>
        </template>
        <el-table :data="shareholderInfo.structures || []" style="width: 100%" border>
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="store_name" label="门店名称" min-width="150" />
          <el-table-column prop="share_percentage" label="占股比例" width="120">
            <template #default="scope">
              {{ scope.row.share_percentage }}%
            </template>
          </el-table-column>
          <el-table-column prop="effective_date" label="生效日期" width="120" />
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button link type="primary" @click="handleEditEquity(scope.row)">编辑</el-button>
              <el-popconfirm
                title="确定要删除该股权结构吗？"
                @confirm="handleDeleteEquity(scope.row)"
              >
                <template #reference>
                  <el-button link type="danger">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      
      <!-- 分红记录卡片 -->
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <span>分红记录</span>
          </div>
        </template>
        <el-empty v-if="!dividendRecords.length" description="暂无分红记录" />
        <el-table v-else :data="dividendRecords" style="width: 100%" border>
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="store_name" label="门店名称" min-width="150" />
          <el-table-column prop="period" label="分红周期" min-width="200">
            <template #default="scope">
              {{ formatDate(scope.row.period_start) }} 至 {{ formatDate(scope.row.period_end) }}
            </template>
          </el-table-column>
          <el-table-column prop="dividend_amount" label="分红金额" width="120">
            <template #default="scope">
              ¥ {{ scope.row.dividend_amount }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'paid' ? 'success' : 'warning'">
                {{ scope.row.status === 'paid' ? '已发放' : '待发放' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button link type="primary" @click="viewDividendDetail(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 出资记录卡片 -->
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <span>出资记录 ({{ investmentRecords.length }})</span>
            <el-button type="primary" @click="handleAddInvestment" plain>添加出资</el-button>
          </div>
        </template>
        <el-empty v-if="!investmentRecords.length" description="暂无出资记录" />
        <el-table v-else :data="investmentRecords" style="width: 100%" border>
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="store_name" label="门店名称" min-width="150" />
          <el-table-column prop="investment_type" label="投资类型" width="120">
            <template #default="scope">
              <el-tag :type="getInvestmentTypeTag(scope.row.investment_type)">
                {{ getInvestmentTypeText(scope.row.investment_type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="金额" width="120">
            <template #default="scope">
              ¥ {{ scope.row.amount }}
            </template>
          </el-table-column>
          <el-table-column prop="investment_date" label="投资日期" width="120" />
          <el-table-column prop="description" label="说明" min-width="200" />
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button link type="primary" @click="handleEditInvestment(scope.row)">编辑</el-button>
              <el-popconfirm
                title="确定要删除该出资记录吗？"
                @confirm="handleDeleteInvestment(scope.row)"
              >
                <template #reference>
                  <el-button link type="danger">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <!-- 投资汇总 -->
        <div v-if="investmentSummary" class="investment-summary">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="总投资" :value="investmentSummary.total_investment" prefix="¥" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="总撤资" :value="investmentSummary.total_withdrawal" prefix="¥" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="净投资" :value="investmentSummary.net_investment" prefix="¥" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="记录数" :value="investmentSummary.total_records" />
            </el-col>
          </el-row>
        </div>
      </el-card>
    </template>
    
    <!-- 编辑股东对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="编辑股东信息"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="shareholderFormRef"
        :model="shareholderForm"
        :rules="shareholderRules"
        label-width="100px"
      >
        <el-form-item label="股东姓名" prop="name">
          <el-input v-model="shareholderForm.name" placeholder="请输入股东姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="shareholderForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="身份证号" prop="id_card_number">
          <el-input v-model="shareholderForm.id_card_number" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="shareholderForm.remarks"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息"
          />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="shareholderForm.password" type="password" placeholder="不修改请留空" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 编辑股权结构对话框 -->
    <el-dialog
      v-model="equityDialogVisible"
      :title="equityDialogType === 'add' ? '添加股权' : '编辑股权'"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="equityFormRef"
        :model="equityForm"
        :rules="equityRules"
        label-width="100px"
      >
        <el-form-item label="门店" prop="store_id">
          <el-select v-model="equityForm.store_id" placeholder="请选择门店" style="width: 100%">
            <el-option
              v-for="item in storeOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="占股比例" prop="share_percentage">
          <el-input-number
            v-model="equityForm.share_percentage"
            :min="0"
            :max="100"
            :precision="2"
            :step="0.01"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="生效日期" prop="effective_date">
          <el-date-picker
            v-model="equityForm.effective_date"
            type="date"
            placeholder="选择日期"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="equityDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitEquity" :loading="equitySubmitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑出资记录对话框 -->
    <el-dialog
      v-model="investmentDialogVisible"
      :title="investmentDialogType === 'add' ? '添加出资记录' : '编辑出资记录'"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="investmentFormRef"
        :model="investmentForm"
        :rules="investmentRules"
        label-width="100px"
      >
        <el-form-item label="门店" prop="store_id">
          <el-select v-model="investmentForm.store_id" placeholder="请选择门店" style="width: 100%">
            <el-option
              v-for="item in storeOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="投资类型" prop="investment_type">
          <el-select v-model="investmentForm.investment_type" placeholder="请选择投资类型" style="width: 100%">
            <el-option label="初始投资" value="initial" />
            <el-option label="追加投资" value="additional" />
            <el-option label="撤资" value="withdrawal" />
          </el-select>
        </el-form-item>
        <el-form-item label="金额" prop="amount">
          <el-input-number
            v-model="investmentForm.amount"
            :min="0"
            :precision="2"
            :step="1000"
            style="width: 100%"
            placeholder="请输入金额"
          />
        </el-form-item>
        <el-form-item label="投资日期" prop="investment_date">
          <el-date-picker
            v-model="investmentForm.investment_date"
            type="date"
            placeholder="选择日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="说明" prop="description">
          <el-input
            v-model="investmentForm.description"
            type="textarea"
            :rows="2"
            placeholder="请输入投资说明"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="investmentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitInvestment" :loading="investmentSubmitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Back } from '@element-plus/icons-vue'
import shareholderApi from '@/api/shareholder'
import storeApi from '@/api/store'

const router = useRouter()
const route = useRoute()
const shareholderId = route.params.id

// 加载状态
const loading = ref(true)

// 股东信息
const shareholderInfo = ref({})

// 分红记录
const dividendRecords = ref([])

// 出资记录
const investmentRecords = ref([])
const investmentSummary = ref(null)

// 门店选项
const storeOptions = ref([])

// 编辑股东表单
const dialogVisible = ref(false)
const submitLoading = ref(false)
const shareholderFormRef = ref(null)
const shareholderForm = reactive({
  id: '',
  name: '',
  phone: '',
  id_card_number: '',
  remarks: '',
  password: ''
})

// 股东表单验证规则
const shareholderRules = {
  name: [
    { required: true, message: '请输入股东姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  id_card_number: [
    { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号格式', trigger: 'blur' }
  ]
}

// 编辑股权表单
const equityDialogVisible = ref(false)
const equityDialogType = ref('add')
const equitySubmitLoading = ref(false)
const equityFormRef = ref(null)
const equityForm = reactive({
  id: null,
  shareholder_id: null,
  store_id: null,
  share_percentage: 0,
  effective_date: null
})

// 股权表单验证规则
const equityRules = {
  store_id: [
    { required: true, message: '请选择门店', trigger: 'change' }
  ],
  share_percentage: [
    { required: true, message: '请输入占股比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '占股比例必须在0-100之间', trigger: 'blur' }
  ],
  effective_date: [
    { required: true, message: '请选择生效日期', trigger: 'change' }
  ]
}

// 编辑出资记录表单
const investmentDialogVisible = ref(false)
const investmentDialogType = ref('add')
const investmentSubmitLoading = ref(false)
const investmentFormRef = ref(null)
const investmentForm = reactive({
  id: null,
  shareholder_id: null,
  store_id: null,
  investment_type: '',
  amount: 0,
  investment_date: null,
  description: ''
})

// 出资记录表单验证规则
const investmentRules = {
  store_id: [
    { required: true, message: '请选择门店', trigger: 'change' }
  ],
  investment_type: [
    { required: true, message: '请选择投资类型', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '金额必须大于0', trigger: 'blur' }
  ],
  investment_date: [
    { required: true, message: '请选择投资日期', trigger: 'change' }
  ]
}

// 获取股东详情
const fetchShareholderDetail = async () => {
  loading.value = true
  try {
    const data = await shareholderApi.getShareholderDetail(shareholderId)
    shareholderInfo.value = data
    loading.value = false
  } catch (error) {
    console.error('获取股东详情失败', error)
    ElMessage.error('获取股东详情失败')
    loading.value = false
  }
}

// 获取门店列表
const fetchStoreList = async () => {
  try {
    const res = await storeApi.getStoreList()
    storeOptions.value = res.data || []
  } catch (error) {
    console.error('获取门店列表失败', error)
    ElMessage.error('获取门店列表失败')
  }
}

// 获取出资记录
const fetchInvestmentRecords = async () => {
  try {
    const res = await shareholderApi.getInvestmentRecords({ shareholder_id: shareholderId })
    investmentRecords.value = res.data || []
  } catch (error) {
    console.error('获取出资记录失败', error)
    ElMessage.error('获取出资记录失败')
  }
}

// 获取投资汇总
const fetchInvestmentSummary = async () => {
  try {
    const res = await shareholderApi.getInvestmentSummary(shareholderId)
    investmentSummary.value = res
  } catch (error) {
    console.error('获取投资汇总失败', error)
    // 使用模拟数据作为降级方案，避免显示错误消息
    investmentSummary.value = {
      total_investment: 0,
      total_dividends: 0,
      net_investment: 0,
      total_records: 0
    }
  }
}

// 返回列表页
const goBack = () => {
  router.push('/shareholders')
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 编辑股东信息
const handleEdit = () => {
  Object.assign(shareholderForm, {
    id: shareholderInfo.value.id,
    name: shareholderInfo.value.name,
    phone: shareholderInfo.value.phone,
    id_card_number: shareholderInfo.value.id_card_number,
    remarks: shareholderInfo.value.remarks,
    password: ''
  })
  dialogVisible.value = true
}

// 提交编辑
const handleSubmit = async () => {
  if (!shareholderFormRef.value) return
  
  await shareholderFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      
      try {
        const { id, password, ...updateData } = shareholderForm
        // 只有当密码字段有值时才更新密码
        if (password) {
          updateData.password = password
        }
        
        await shareholderApi.updateShareholder(id, updateData)
        
        ElMessage.success('更新股东信息成功')
        dialogVisible.value = false
        submitLoading.value = false
        
        // 重新获取股东信息
        fetchShareholderDetail()
      } catch (error) {
        console.error('提交失败', error)
        ElMessage.error('提交失败')
        submitLoading.value = false
      }
    }
  })
}

// 添加股权
const handleAddEquity = () => {
  equityDialogType.value = 'add'
  Object.assign(equityForm, {
    id: null,
    shareholder_id: shareholderId,
    store_id: null,
    share_percentage: 0,
    effective_date: new Date()
  })
  equityDialogVisible.value = true
}

// 编辑股权
const handleEditEquity = (row) => {
  equityDialogType.value = 'edit'
  Object.assign(equityForm, {
    id: row.id,
    shareholder_id: shareholderId,
    store_id: row.store_id,
    share_percentage: row.share_percentage,
    effective_date: new Date(row.effective_date)
  })
  equityDialogVisible.value = true
}

// 删除股权
const handleDeleteEquity = async (row) => {
  try {
    await shareholderApi.deleteShareholderStructure(row.id)
    ElMessage.success('删除股权结构成功')
    // 重新获取股东信息
    fetchShareholderDetail()
  } catch (error) {
    console.error('删除股权结构失败', error)
    ElMessage.error('删除股权结构失败')
  }
}

// 提交股权表单
const handleSubmitEquity = async () => {
  if (!equityFormRef.value) return
  
  await equityFormRef.value.validate(async (valid) => {
    if (valid) {
      equitySubmitLoading.value = true
      
      try {
        // 格式化日期
        const formData = { ...equityForm }
        if (formData.effective_date instanceof Date) {
          formData.effective_date = formatDate(formData.effective_date)
        }
        
        if (equityDialogType.value === 'add') {
          // 新增
          await shareholderApi.createShareholderStructure(formData)
          ElMessage.success('添加股权结构成功')
        } else {
          // 编辑
          const { id, ...updateData } = formData
          await shareholderApi.updateShareholderStructure(id, updateData)
          ElMessage.success('编辑股权结构成功')
        }
        
        equityDialogVisible.value = false
        equitySubmitLoading.value = false
        
        // 重新获取股东信息
        fetchShareholderDetail()
      } catch (error) {
        console.error('提交失败', error)
        ElMessage.error('提交失败')
        equitySubmitLoading.value = false
      }
    }
  })
}

// 查看分红详情
const viewDividendDetail = (row) => {
  router.push(`/dividend-records/${row.id}`)
}

// 添加出资记录
const handleAddInvestment = () => {
  investmentDialogType.value = 'add'
  Object.assign(investmentForm, {
    id: null,
    shareholder_id: shareholderId,
    store_id: null,
    investment_type: '',
    amount: 0,
    investment_date: new Date(),
    description: ''
  })
  investmentDialogVisible.value = true
}

// 编辑出资记录
const handleEditInvestment = (row) => {
  investmentDialogType.value = 'edit'
  Object.assign(investmentForm, {
    id: row.id,
    shareholder_id: row.shareholder_id,
    store_id: row.store_id,
    investment_type: row.investment_type,
    amount: row.amount,
    investment_date: new Date(row.investment_date),
    description: row.description
  })
  investmentDialogVisible.value = true
}

// 删除出资记录
const handleDeleteInvestment = async (row) => {
  try {
    await shareholderApi.deleteInvestmentRecord(row.id)
    ElMessage.success('删除出资记录成功')
    fetchInvestmentRecords()
    fetchInvestmentSummary()
  } catch (error) {
    console.error('删除出资记录失败', error)
    ElMessage.error('删除出资记录失败')
  }
}

// 提交出资记录
const handleSubmitInvestment = async () => {
  if (!investmentFormRef.value) return

  await investmentFormRef.value.validate(async (valid) => {
    if (valid) {
      investmentSubmitLoading.value = true

      try {
        const formData = { ...investmentForm }

        // 格式化日期
        if (formData.investment_date) {
          formData.investment_date = formData.investment_date.toISOString().split('T')[0]
        }

        if (investmentDialogType.value === 'add') {
          // 新增
          await shareholderApi.createInvestmentRecord(formData)
          ElMessage.success('添加出资记录成功')
        } else {
          // 编辑
          const { id, ...updateData } = formData
          await shareholderApi.updateInvestmentRecord(id, updateData)
          ElMessage.success('编辑出资记录成功')
        }

        investmentDialogVisible.value = false
        investmentSubmitLoading.value = false

        // 重新获取数据
        fetchInvestmentRecords()
        fetchInvestmentSummary()
      } catch (error) {
        console.error('提交失败', error)
        ElMessage.error('提交失败')
        investmentSubmitLoading.value = false
      }
    }
  })
}

// 获取投资类型标签样式
const getInvestmentTypeTag = (type) => {
  const tagMap = {
    'initial': 'success',
    'additional': 'primary',
    'withdrawal': 'danger'
  }
  return tagMap[type] || 'info'
}

// 获取投资类型文本
const getInvestmentTypeText = (type) => {
  const textMap = {
    'initial': '初始投资',
    'additional': '追加投资',
    'withdrawal': '撤资'
  }
  return textMap[type] || type
}

// 初始化
onMounted(() => {
  fetchShareholderDetail()
  fetchStoreList()
  fetchInvestmentRecords()
  fetchInvestmentSummary()
})
</script>

<style lang="scss" scoped>
.shareholder-detail-container {
  .back-button {
    margin-bottom: 20px;
  }
  
  .loading-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
  }
  
  .detail-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .investment-summary {
    margin-top: 20px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
}
</style> 