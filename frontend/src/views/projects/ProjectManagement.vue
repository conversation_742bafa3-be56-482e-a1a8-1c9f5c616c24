<template>
  <div class="project-management-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>项目管理</h1>
      <p>管理集团项目和门店项目，支持分类显示、定价管理和状态控制</p>
    </div>

    <!-- 功能选项卡 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="project-tabs">
      <!-- 集团项目管理 -->
      <el-tab-pane label="集团项目" name="group">
        <div class="tab-content">
          <div class="section-header">
            <div class="header-left">
              <h3>集团项目库</h3>
              <span class="item-count">共 {{ groupItems.length }} 个项目</span>
            </div>
            <div class="header-actions">
              <el-button type="primary" @click="showCreateGroupItemDialog">
                <el-icon><Plus /></el-icon>
                新增集团项目
              </el-button>
              <el-button @click="refreshGroupItems">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </div>

          <!-- 搜索和筛选 -->
          <div class="filter-section">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-input
                  v-model="groupFilters.search"
                  placeholder="搜索项目名称"
                  clearable
                  @input="handleGroupSearch"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </el-col>
              <el-col :span="4">
                <el-select v-model="groupFilters.type" placeholder="项目类型" clearable @change="handleGroupFilter">
                  <el-option label="服务项目" value="service" />
                  <el-option label="产品项目" value="product" />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select v-model="groupFilters.category" placeholder="项目分类" clearable @change="handleGroupFilter">
                  <el-option v-for="category in categoryOptions" :key="category" :label="category" :value="category" />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select v-model="groupFilters.status" placeholder="状态" clearable @change="handleGroupFilter">
                  <el-option label="启用" value="active" />
                  <el-option label="禁用" value="inactive" />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-button @click="resetGroupFilters">重置</el-button>
                <el-button type="primary" @click="exportGroupItems">导出</el-button>
              </el-col>
            </el-row>
          </div>

          <!-- 集团项目列表 -->
          <div class="table-container">
            <el-table
              :data="filteredGroupItems"
              v-loading="groupLoading"
              stripe
              border
              style="width: 100%"
            >
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="name" label="项目名称" min-width="150" />
              <el-table-column prop="type" label="类型" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.type === 'service' ? 'primary' : 'success'">
                    {{ row.type === 'service' ? '服务' : '产品' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="category" label="分类" width="120" />
              <el-table-column prop="group_standard_price" label="标准价格" width="120">
                <template #default="{ row }">
                  <span class="price">¥{{ Number(row.group_standard_price || 0).toFixed(2) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="duration" label="时长(分钟)" width="100" />
              <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
              <el-table-column prop="is_active" label="状态" width="80">
                <template #default="{ row }">
                  <el-switch
                    v-model="row.is_active"
                    @change="handleGroupItemStatusChange(row)"
                    :loading="row.statusLoading"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                  <el-button size="small" @click="handleEditGroupItem(row)">编辑</el-button>
                  <el-button size="small" type="info" @click="viewGroupItemDetail(row)">详情</el-button>
                  <el-button size="small" type="danger" @click="handleDeleteGroupItem(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="groupPagination.page"
                v-model:page-size="groupPagination.size"
                :page-sizes="[10, 20, 50, 100]"
                :total="groupPagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleGroupPageSizeChange"
                @current-change="handleGroupPageChange"
              />
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 门店项目管理 -->
      <el-tab-pane label="门店项目" name="store">
        <div class="tab-content">
          <div class="section-header">
            <div class="header-left">
              <h3>门店项目库</h3>
              <span class="item-count">共 {{ storeItems.length }} 个项目</span>
            </div>
            <div class="header-actions">
              <el-select v-model="selectedStoreId" placeholder="选择门店" @change="handleStoreChange" style="width: 200px; margin-right: 10px;">
                <el-option v-for="store in stores" :key="store.id" :label="store.name" :value="store.id" />
              </el-select>
              <el-button type="primary" @click="showCreateStoreItemDialog">
                <el-icon><Plus /></el-icon>
                新增门店项目
              </el-button>
              <el-button @click="refreshStoreItems">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </div>

          <!-- 门店项目搜索和筛选 -->
          <div class="filter-section">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-input
                  v-model="storeFilters.search"
                  placeholder="搜索项目名称"
                  clearable
                  @input="handleStoreSearch"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </el-col>
              <el-col :span="4">
                <el-select v-model="storeFilters.type" placeholder="项目类型" clearable @change="handleStoreFilter">
                  <el-option label="服务项目" value="service" />
                  <el-option label="产品项目" value="product" />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select v-model="storeFilters.status" placeholder="状态" clearable @change="handleStoreFilter">
                  <el-option label="启用" value="active" />
                  <el-option label="禁用" value="inactive" />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-button @click="resetStoreFilters">重置</el-button>
                <el-button type="primary" @click="exportStoreItems">导出</el-button>
              </el-col>
            </el-row>
          </div>

          <!-- 门店项目列表 -->
          <div class="table-container">
            <el-table
              :data="filteredStoreItems"
              v-loading="storeLoading"
              stripe
              border
              style="width: 100%"
            >
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="name" label="项目名称" min-width="150" />
              <el-table-column prop="group_item_name" label="集团项目" min-width="150" />
              <el-table-column prop="store_price" label="门店价格" width="120">
                <template #default="{ row }">
                  <span class="price">¥{{ Number(row.store_price || 0).toFixed(2) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="member_price" label="会员价格" width="120">
                <template #default="{ row }">
                  <span class="price member-price">¥{{ Number(row.member_price || 0).toFixed(2) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="is_active" label="状态" width="80">
                <template #default="{ row }">
                  <el-switch
                    v-model="row.is_active"
                    @change="handleStoreItemStatusChange(row)"
                    :loading="row.statusLoading"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                  <el-button size="small" @click="handleEditStoreItem(row)">编辑</el-button>
                  <el-button size="small" type="info" @click="viewStoreItemDetail(row)">详情</el-button>
                  <el-button size="small" type="danger" @click="handleDeleteStoreItem(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="storePagination.page"
                v-model:page-size="storePagination.size"
                :page-sizes="[10, 20, 50, 100]"
                :total="storePagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleStorePageSizeChange"
                @current-change="handleStorePageChange"
              />
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 项目统计 -->
      <el-tab-pane label="项目统计" name="statistics">
        <div class="tab-content">
          <div class="statistics-section">
            <h3>项目统计概览</h3>
            <el-row :gutter="20" class="stats-cards">
              <el-col :span="6">
                <div class="stat-card">
                  <div class="stat-number">{{ groupItems.length }}</div>
                  <div class="stat-label">集团项目总数</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-card">
                  <div class="stat-number">{{ storeItems.length }}</div>
                  <div class="stat-label">门店项目总数</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-card">
                  <div class="stat-number">{{ activeGroupItems }}</div>
                  <div class="stat-label">启用集团项目</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-card">
                  <div class="stat-number">{{ activeStoreItems }}</div>
                  <div class="stat-label">启用门店项目</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 新增/编辑集团项目对话框 -->
    <el-dialog
      v-model="groupItemDialogVisible"
      :title="groupItemDialogType === 'add' ? '新增集团项目' : '编辑集团项目'"
      width="800px"
      destroy-on-close
    >
      <el-form
        ref="groupItemFormRef"
        :model="groupItemForm"
        :rules="groupItemRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="name">
              <el-input v-model="groupItemForm.name" placeholder="请输入项目名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目类型" prop="type">
              <el-select v-model="groupItemForm.type" placeholder="请选择项目类型">
                <el-option label="服务项目" value="service" />
                <el-option label="产品项目" value="product" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目分类" prop="category">
              <el-select v-model="groupItemForm.category" placeholder="选择或输入分类" filterable allow-create>
                <el-option v-for="category in categoryOptions" :key="category" :label="category" :value="category" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标准价格" prop="group_standard_price">
              <el-input-number v-model="groupItemForm.group_standard_price" :min="0" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务时长(分钟)" prop="duration">
              <el-input-number v-model="groupItemForm.duration" :min="1" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-switch v-model="groupItemForm.is_active" active-text="启用" inactive-text="禁用" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="项目描述">
          <el-input v-model="groupItemForm.description" type="textarea" :rows="3" placeholder="请输入项目描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="groupItemDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitGroupItemForm" :loading="groupItemSubmitting">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Search } from '@element-plus/icons-vue'
import itemApi from '@/api/item'
import storeApi from '@/api/store'

// 响应式数据
const activeTab = ref('group')
const groupLoading = ref(false)
const storeLoading = ref(false)

// 集团项目数据
const groupItems = ref([])
const filteredGroupItems = ref([])
const groupFilters = reactive({
  search: '',
  type: '',
  category: '',
  status: ''
})
const groupPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 门店项目数据
const storeItems = ref([])
const filteredStoreItems = ref([])
const stores = ref([])
const selectedStoreId = ref(null)
const storeFilters = reactive({
  search: '',
  type: '',
  status: ''
})
const storePagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 分类选项
const categoryOptions = ref(['按摩', '足疗', '美容', '养生', '其他'])

// 集团项目对话框
const groupItemDialogVisible = ref(false)
const groupItemDialogType = ref('add')
const groupItemSubmitting = ref(false)
const groupItemFormRef = ref()
const groupItemForm = reactive({
  name: '',
  type: 'service',
  category: '',
  group_standard_price: 0,
  duration: 60,
  description: '',
  is_active: true
})

// 表单验证规则
const groupItemRules = {
  name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择项目类型', trigger: 'change' }],
  category: [{ required: true, message: '请选择项目分类', trigger: 'change' }],
  group_standard_price: [{ required: true, message: '请输入标准价格', trigger: 'blur' }]
}

// 计算属性
const activeGroupItems = computed(() => groupItems.value.filter(item => item.is_active).length)
const activeStoreItems = computed(() => storeItems.value.filter(item => item.is_active).length)

// 生命周期
onMounted(() => {
  fetchStores()
  fetchGroupItems()
})

// 方法
const handleTabClick = (tab) => {
  if (tab.name === 'group') {
    fetchGroupItems()
  } else if (tab.name === 'store') {
    fetchStoreItems()
  }
}

// 获取门店列表
const fetchStores = async () => {
  try {
    const response = await storeApi.getList()
    stores.value = response.items || []
    if (stores.value.length > 0) {
      selectedStoreId.value = stores.value[0].id
      fetchStoreItems()
    }
  } catch (error) {
    ElMessage.error('获取门店列表失败: ' + error.message)
  }
}

// 获取集团项目列表
const fetchGroupItems = async () => {
  groupLoading.value = true
  try {
    const params = {
      page: groupPagination.page,
      size: groupPagination.size
    }
    const response = await itemApi.groupItem.getList(params)
    groupItems.value = response.items || []
    groupPagination.total = response.total || 0
    applyGroupFilters()
  } catch (error) {
    ElMessage.error('获取集团项目列表失败: ' + error.message)
  } finally {
    groupLoading.value = false
  }
}

// 获取门店项目列表
const fetchStoreItems = async () => {
  if (!selectedStoreId.value) return
  
  storeLoading.value = true
  try {
    const params = {
      store_id: selectedStoreId.value,
      page: storePagination.page,
      size: storePagination.size
    }
    const response = await itemApi.storeItem.getList(params)
    storeItems.value = response.items || []
    storePagination.total = response.total || 0
    applyStoreFilters()
  } catch (error) {
    ElMessage.error('获取门店项目列表失败: ' + error.message)
  } finally {
    storeLoading.value = false
  }
}

// 应用集团项目筛选
const applyGroupFilters = () => {
  let filtered = [...groupItems.value]
  
  if (groupFilters.search) {
    filtered = filtered.filter(item => 
      item.name.toLowerCase().includes(groupFilters.search.toLowerCase())
    )
  }
  
  if (groupFilters.type) {
    filtered = filtered.filter(item => item.type === groupFilters.type)
  }
  
  if (groupFilters.category) {
    filtered = filtered.filter(item => item.category === groupFilters.category)
  }
  
  if (groupFilters.status) {
    const isActive = groupFilters.status === 'active'
    filtered = filtered.filter(item => item.is_active === isActive)
  }
  
  filteredGroupItems.value = filtered
}

// 应用门店项目筛选
const applyStoreFilters = () => {
  let filtered = [...storeItems.value]
  
  if (storeFilters.search) {
    filtered = filtered.filter(item => 
      item.name.toLowerCase().includes(storeFilters.search.toLowerCase())
    )
  }
  
  if (storeFilters.type) {
    filtered = filtered.filter(item => item.type === storeFilters.type)
  }
  
  if (storeFilters.status) {
    const isActive = storeFilters.status === 'active'
    filtered = filtered.filter(item => item.is_active === isActive)
  }
  
  filteredStoreItems.value = filtered
}

// 集团项目搜索
const handleGroupSearch = () => {
  applyGroupFilters()
}

// 集团项目筛选
const handleGroupFilter = () => {
  applyGroupFilters()
}

// 门店项目搜索
const handleStoreSearch = () => {
  applyStoreFilters()
}

// 门店项目筛选
const handleStoreFilter = () => {
  applyStoreFilters()
}

// 重置集团项目筛选
const resetGroupFilters = () => {
  Object.assign(groupFilters, {
    search: '',
    type: '',
    category: '',
    status: ''
  })
  applyGroupFilters()
}

// 重置门店项目筛选
const resetStoreFilters = () => {
  Object.assign(storeFilters, {
    search: '',
    type: '',
    status: ''
  })
  applyStoreFilters()
}

// 刷新集团项目
const refreshGroupItems = () => {
  fetchGroupItems()
}

// 刷新门店项目
const refreshStoreItems = () => {
  fetchStoreItems()
}

// 门店切换
const handleStoreChange = () => {
  fetchStoreItems()
}

// 显示新增集团项目对话框
const showCreateGroupItemDialog = () => {
  groupItemDialogType.value = 'add'
  resetGroupItemForm()
  groupItemDialogVisible.value = true
}

// 重置集团项目表单
const resetGroupItemForm = () => {
  Object.assign(groupItemForm, {
    name: '',
    type: 'service',
    category: '',
    group_standard_price: 0,
    duration: 60,
    description: '',
    is_active: true
  })
  if (groupItemFormRef.value) {
    groupItemFormRef.value.resetFields()
  }
}

// 提交集团项目表单
const submitGroupItemForm = async () => {
  if (!groupItemFormRef.value) return
  
  await groupItemFormRef.value.validate(async (valid) => {
    if (valid) {
      groupItemSubmitting.value = true
      try {
        if (groupItemDialogType.value === 'add') {
          await itemApi.groupItem.create(groupItemForm)
          ElMessage.success('新增成功')
        } else {
          await itemApi.groupItem.update(groupItemForm.id, groupItemForm)
          ElMessage.success('更新成功')
        }
        
        groupItemDialogVisible.value = false
        fetchGroupItems()
      } catch (error) {
        ElMessage.error('保存失败: ' + error.message)
      } finally {
        groupItemSubmitting.value = false
      }
    }
  })
}

// 编辑集团项目
const handleEditGroupItem = (row) => {
  groupItemDialogType.value = 'edit'
  Object.assign(groupItemForm, row)
  groupItemDialogVisible.value = true
}

// 删除集团项目
const handleDeleteGroupItem = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该项目吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await itemApi.groupItem.delete(row.id)
    ElMessage.success('删除成功')
    fetchGroupItems()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

// 集团项目状态变更
const handleGroupItemStatusChange = async (row) => {
  row.statusLoading = true
  try {
    await itemApi.groupItem.update(row.id, { is_active: row.is_active })
    ElMessage.success('状态更新成功')
  } catch (error) {
    row.is_active = !row.is_active // 回滚状态
    ElMessage.error('状态更新失败: ' + error.message)
  } finally {
    row.statusLoading = false
  }
}

// 门店项目状态变更
const handleStoreItemStatusChange = async (row) => {
  row.statusLoading = true
  try {
    await itemApi.storeItem.update(row.id, { is_active: row.is_active })
    ElMessage.success('状态更新成功')
  } catch (error) {
    row.is_active = !row.is_active // 回滚状态
    ElMessage.error('状态更新失败: ' + error.message)
  } finally {
    row.statusLoading = false
  }
}

// 分页处理
const handleGroupPageChange = (page) => {
  groupPagination.page = page
  fetchGroupItems()
}

const handleGroupPageSizeChange = (size) => {
  groupPagination.size = size
  groupPagination.page = 1
  fetchGroupItems()
}

const handleStorePageChange = (page) => {
  storePagination.page = page
  fetchStoreItems()
}

const handleStorePageSizeChange = (size) => {
  storePagination.size = size
  storePagination.page = 1
  fetchStoreItems()
}

// 导出功能
const exportGroupItems = () => {
  ElMessage.info('导出功能开发中...')
}

const exportStoreItems = () => {
  ElMessage.info('导出功能开发中...')
}

// 查看详情
const viewGroupItemDetail = (row) => {
  ElMessage.info('详情功能开发中...')
}

const viewStoreItemDetail = (row) => {
  ElMessage.info('详情功能开发中...')
}

// 新增门店项目
const showCreateStoreItemDialog = () => {
  ElMessage.info('新增门店项目功能开发中...')
}

// 编辑门店项目
const handleEditStoreItem = (row) => {
  ElMessage.info('编辑门店项目功能开发中...')
}

// 删除门店项目
const handleDeleteStoreItem = (row) => {
  ElMessage.info('删除门店项目功能开发中...')
}
</script>

<style scoped>
.project-management-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.project-tabs {
  margin-top: 20px;
}

.tab-content {
  padding: 20px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-left h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.item-count {
  color: #909399;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.pagination-container {
  padding: 20px;
  text-align: right;
  background: white;
  border-top: 1px solid #ebeef5;
}

.price {
  font-weight: 600;
  color: #e6a23c;
}

.member-price {
  color: #67c23a;
}

.statistics-section {
  padding: 20px;
}

.stats-cards {
  margin-top: 20px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  text-align: center;
}

.stat-number {
  font-size: 32px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-tabs__item) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.el-table th) {
  background-color: #fafafa;
}
</style>
