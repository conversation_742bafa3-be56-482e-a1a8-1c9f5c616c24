<template>
  <div class="business-role-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>业务角色管理</h1>
      <p>管理业务系统的角色和权限</p>
    </div>

    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button @click="fetchRoles">
        刷新
      </el-button>
      <el-button type="primary" @click="showPermissionMatrix">
        权限矩阵
      </el-button>
    </div>

    <!-- 角色列表 -->
    <div class="roles-container">
      <el-table
        :data="roles"
        style="width: 100%"
        v-loading="loading"
        border
        stripe
      >
        <el-table-column prop="code" label="角色代码" width="150">
          <template #default="scope">
            <el-tag size="small">{{ scope.row.code }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="角色名称" width="200" />
        <el-table-column prop="description" label="角色描述" min-width="300" />
        <el-table-column prop="permission_count" label="权限数量" width="120" align="center">
          <template #default="scope">
            <el-tag :type="getRoleTagType(scope.row.code)">
              {{ scope.row.permission_count }} 个权限
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="viewRolePermissions(scope.row)"
            >
              查看权限
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="assignRole(scope.row)"
            >
              分配角色
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 角色权限详情对话框 -->
    <el-dialog
      title="角色权限详情"
      v-model="permissionDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedRolePermissions" class="permission-detail">
        <div class="role-info">
          <h3>{{ selectedRolePermissions.role_name }}</h3>
          <p>{{ selectedRolePermissions.role_description }}</p>
          <el-tag type="info">总计 {{ selectedRolePermissions.total_permissions }} 个权限</el-tag>
        </div>
        
        <el-divider />
        
        <div class="permission-categories">
          <div 
            v-for="(permissions, category) in selectedRolePermissions.permissions" 
            :key="category"
            class="permission-category"
          >
            <h4>{{ category }}</h4>
            <div class="permission-tags">
              <el-tag 
                v-for="permission in permissions" 
                :key="permission.code"
                class="permission-tag"
                size="small"
              >
                {{ permission.name }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 分配角色对话框 -->
    <el-dialog
      title="分配角色"
      v-model="assignDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="assign-role-content">
        <div class="role-info">
          <h3>{{ selectedRole?.name }}</h3>
          <p>{{ selectedRole?.description }}</p>
        </div>
        
        <el-divider />
        
        <div class="employee-search">
          <el-input
            v-model="employeeSearchText"
            placeholder="搜索员工姓名或手机号"
            :prefix-icon="Search"
            clearable
            @input="searchEmployees"
          />
        </div>
        
        <div class="employee-list">
          <el-table :data="filteredEmployees" style="width: 100%" max-height="400">
            <el-table-column prop="name" label="姓名" width="120" />
            <el-table-column prop="phone" label="手机号" width="130" />
            <el-table-column prop="current_role" label="当前角色" width="120">
              <template #default="scope">
                <el-tag size="small">{{ getRoleName(scope.row.role) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="store_name" label="门店" />
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button 
                  type="text" 
                  size="small"
                  :disabled="scope.row.role === selectedRole?.code"
                  @click="confirmAssignRole(scope.row)"
                >
                  分配
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="assignDialogVisible = false">取消</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 权限矩阵对话框 -->
    <el-dialog
      title="权限矩阵"
      v-model="matrixDialogVisible"
      width="90%"
      :close-on-click-modal="false"
      fullscreen
    >
      <div class="permission-matrix">
        <div class="matrix-toolbar">
          <el-input
            v-model="matrixSearchText"
            placeholder="搜索权限..."
            style="width: 300px"
            :prefix-icon="Search"
            clearable
          />
        </div>
        
        <div class="matrix-table">
          <el-table :data="matrixData" style="width: 100%" border>
            <el-table-column prop="permission_name" label="权限" width="200" fixed="left" />
            <el-table-column prop="category" label="分类" width="120" />
            <el-table-column 
              v-for="role in roles" 
              :key="role.code"
              :label="role.name"
              width="100"
              align="center"
            >
              <template #default="scope">
                <el-icon v-if="hasPermission(role.code, scope.row.permission_code)" color="#67c23a">
                  <Check />
                </el-icon>
                <el-icon v-else color="#f56c6c">
                  <Close />
                </el-icon>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getBusinessRoles,
  getBusinessPermissions,
  getRolePermissions,
  updateEmployeeRole
} from '@/api/admin'

// 响应式数据
const loading = ref(false)
const permissionDialogVisible = ref(false)
const assignDialogVisible = ref(false)
const matrixDialogVisible = ref(false)
const employeeSearchText = ref('')
const matrixSearchText = ref('')

const roles = ref([])
const allPermissions = ref({})
const selectedRole = ref(null)
const selectedRolePermissions = ref(null)
const employees = ref([])

// 计算属性
const filteredEmployees = computed(() => {
  if (!employeeSearchText.value) return employees.value
  
  const searchText = employeeSearchText.value.toLowerCase()
  return employees.value.filter(emp => 
    emp.name.toLowerCase().includes(searchText) ||
    emp.phone.includes(searchText)
  )
})

const matrixData = computed(() => {
  if (!allPermissions.value) return []
  
  const data = []
  Object.entries(allPermissions.value).forEach(([category, permissions]) => {
    permissions.forEach(permission => {
      if (!matrixSearchText.value || 
          permission.name.toLowerCase().includes(matrixSearchText.value.toLowerCase()) ||
          category.toLowerCase().includes(matrixSearchText.value.toLowerCase())) {
        data.push({
          permission_code: permission.code,
          permission_name: permission.name,
          category: category
        })
      }
    })
  })
  
  return data
})

// 获取角色标签类型
const getRoleTagType = (roleCode) => {
  if (roleCode.includes('manager')) return 'danger'
  if (roleCode.includes('senior') || roleCode.includes('chief')) return 'warning'
  if (roleCode.includes('cashier') || roleCode.includes('technician')) return 'success'
  return 'info'
}

// 获取角色名称
const getRoleName = (roleCode) => {
  const role = roles.value.find(r => r.code === roleCode)
  return role ? role.name : roleCode
}

// 检查角色是否有权限
const hasPermission = (roleCode, permissionCode) => {
  const role = roles.value.find(r => r.code === roleCode)
  return role ? role.permissions.includes(permissionCode) : false
}

// 获取业务角色列表
const fetchRoles = async () => {
  loading.value = true
  try {
    const response = await getBusinessRoles()
    roles.value = response.data || response || []
  } catch (error) {
    console.error('获取业务角色失败:', error)
    ElMessage.error('获取业务角色失败')
  } finally {
    loading.value = false
  }
}

// 获取所有权限
const fetchPermissions = async () => {
  try {
    const response = await getBusinessPermissions()
    allPermissions.value = response.data || response || {}
  } catch (error) {
    console.error('获取权限列表失败:', error)
  }
}

// 获取员工列表
const fetchEmployees = async () => {
  // 暂时注释掉，避免导入问题
  employees.value = [
    { id: 1, name: '张三', phone: '***********', role: 'cashier', store_name: '总店' },
    { id: 2, name: '李四', phone: '***********', role: 'technician', store_name: '总店' },
    { id: 3, name: '王五', phone: '***********', role: 'store_manager', store_name: '分店' }
  ]
}

// 查看角色权限
const viewRolePermissions = async (role) => {
  try {
    const response = await getRolePermissions(role.code)
    selectedRolePermissions.value = response.data || response
    permissionDialogVisible.value = true
  } catch (error) {
    console.error('获取角色权限失败:', error)
    ElMessage.error('获取角色权限失败')
  }
}

// 分配角色
const assignRole = (role) => {
  selectedRole.value = role
  assignDialogVisible.value = true
}

// 确认分配角色
const confirmAssignRole = async (employee) => {
  try {
    await ElMessageBox.confirm(
      `确定要将角色 "${selectedRole.value.name}" 分配给员工 "${employee.name}" 吗？`,
      '确认分配',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await updateEmployeeRole(employee.id, { role_code: selectedRole.value.code })
    ElMessage.success('角色分配成功')
    
    // 更新员工列表中的角色
    const emp = employees.value.find(e => e.id === employee.id)
    if (emp) {
      emp.role = selectedRole.value.code
    }
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('分配角色失败:', error)
      ElMessage.error('分配角色失败')
    }
  }
}

// 显示权限矩阵
const showPermissionMatrix = () => {
  matrixDialogVisible.value = true
}

// 搜索员工
const searchEmployees = () => {
  // 搜索逻辑在计算属性中处理
}

// 初始化
onMounted(() => {
  fetchRoles()
  fetchPermissions()
  fetchEmployees()
})
</script>

<style scoped>
.business-role-list {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.toolbar {
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
}

.roles-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.permission-detail {
  max-height: 500px;
  overflow-y: auto;
}

.role-info h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.role-info p {
  margin: 0 0 12px 0;
  color: #606266;
}

.permission-categories {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.permission-category h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.permission-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.permission-tag {
  margin: 0;
}

.assign-role-content {
  max-height: 600px;
  overflow-y: auto;
}

.employee-search {
  margin-bottom: 16px;
}

.employee-list {
  max-height: 400px;
  overflow-y: auto;
}

.permission-matrix {
  height: 70vh;
  display: flex;
  flex-direction: column;
}

.matrix-toolbar {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.matrix-table {
  flex: 1;
  overflow: auto;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .role-card {
    height: auto;
  }
  
  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
