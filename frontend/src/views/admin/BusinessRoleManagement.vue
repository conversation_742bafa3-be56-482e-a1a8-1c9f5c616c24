<template>
  <div class="business-role-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>业务角色管理</h1>
      <p>管理业务系统的角色和权限分配</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="24" color="#409EFF"><User /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ totalRoles }}</div>
              <div class="stats-label">业务角色</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="24" color="#67C23A"><UserFilled /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ totalEmployees }}</div>
              <div class="stats-label">员工总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="24" color="#E6A23C"><Key /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ totalPermissions }}</div>
              <div class="stats-label">权限总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="24" color="#F56C6C"><Shop /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ totalStores }}</div>
              <div class="stats-label">门店数量</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="showCreateRoleDialog">
        <el-icon><Plus /></el-icon>
        新增角色
      </el-button>
      <el-button @click="refreshData">
        <el-icon><Refresh /></el-icon>
        刷新数据
      </el-button>
      <el-button type="primary" @click="showPermissionMatrix">
        <el-icon><Grid /></el-icon>
        权限矩阵
      </el-button>
      <el-button type="success" @click="showRoleStatistics">
        <el-icon><DataAnalysis /></el-icon>
        角色统计
      </el-button>
      <el-button type="warning" @click="showPermissionManagement">
        <el-icon><Setting /></el-icon>
        权限管理
      </el-button>
    </div>

    <!-- 角色列表 -->
    <div class="roles-container">
      <el-table
        :data="businessRoles"
        style="width: 100%"
        border
        stripe
        v-loading="loading"
      >
        <el-table-column label="角色信息" width="250">
          <template #default="scope">
            <div class="role-info-cell">
              <div class="role-icon-name">
                <el-icon :color="getRoleIconColor(scope.row.code)" size="20">
                  <component :is="getRoleIcon(scope.row.code)" />
                </el-icon>
                <span class="role-name">{{ scope.row.name }}</span>
              </div>
              <div class="role-code">{{ scope.row.code }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="角色描述" min-width="300" />
        <el-table-column label="权限数量" width="120" align="center">
          <template #default="scope">
            <el-tag :type="getRoleTagType(scope.row.code)" size="small">
              {{ scope.row.permission_count }} 权限
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="员工数量" width="100" align="center">
          <template #default="scope">
            <el-button
              type="text"
              class="employee-count-btn"
              @click="viewRoleEmployees(scope.row)"
              :disabled="getEmployeeCountByRole(scope.row.code) === 0"
            >
              {{ getEmployeeCountByRole(scope.row.code) }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="权限级别" width="100" align="center">
          <template #default="scope">
            <el-tag size="small" :type="getRoleLevelType(scope.row.code)">
              {{ getRoleLevel(scope.row.code) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="400" align="center">
          <template #default="scope">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="viewRolePermissions(scope.row)">
                <el-icon><View /></el-icon>
                查看权限
              </el-button>
              <el-button type="warning" size="small" @click="editRole(scope.row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="success" size="small" @click="assignRole(scope.row)">
                <el-icon><User /></el-icon>
                分配
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="deleteRole(scope.row)"
                :disabled="scope.row.is_system || scope.row.employee_count > 0"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 角色权限详情对话框 -->
    <el-dialog
      title="角色权限详情"
      v-model="rolePermissionDialogVisible"
      width="900px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedRolePermissions" class="permission-detail">
        <div class="role-info">
          <div class="role-info-header">
            <el-icon :color="getRoleIconColor(selectedRolePermissions.role_code)">
              <component :is="getRoleIcon(selectedRolePermissions.role_code)" />
            </el-icon>
            <div>
              <h3>{{ selectedRolePermissions.role_name }}</h3>
              <p>{{ selectedRolePermissions.role_description }}</p>
            </div>
          </div>
          <el-tag type="info" size="large">
            总计 {{ selectedRolePermissions.total_permissions }} 个权限
          </el-tag>
        </div>
        
        <el-divider />
        
        <div class="permission-categories">
          <div 
            v-for="(permissions, category) in selectedRolePermissions.permissions" 
            :key="category"
            class="permission-category"
          >
            <div class="category-header">
              <h4>{{ category }}</h4>
              <el-tag size="small">{{ permissions.length }} 个权限</el-tag>
            </div>
            <div class="permission-grid">
              <el-tag 
                v-for="permission in permissions" 
                :key="permission.code"
                class="permission-tag"
                size="small"
                effect="plain"
              >
                {{ permission.name }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 分配角色对话框 -->
    <el-dialog
      title="分配角色"
      v-model="assignDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="assign-role-content">
        <div class="role-info">
          <div class="role-info-header">
            <el-icon :color="getRoleIconColor(selectedRole?.code)">
              <component :is="getRoleIcon(selectedRole?.code)" />
            </el-icon>
            <div>
              <h3>{{ selectedRole?.name }}</h3>
              <p>{{ selectedRole?.description }}</p>
            </div>
          </div>
        </div>
        
        <el-divider />
        
        <div class="employee-search">
          <el-input
            v-model="employeeSearchText"
            placeholder="搜索员工姓名、手机号或门店"
            clearable
            @input="searchEmployees"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        
        <div class="employee-list">
          <el-table :data="filteredEmployees" style="width: 100%" max-height="400">
            <el-table-column prop="name" label="姓名" width="120" />
            <el-table-column prop="phone" label="手机号" width="130" />
            <el-table-column prop="current_role" label="当前角色" width="120">
              <template #default="scope">
                <el-tag size="small" :type="getRoleTagType(scope.row.role)">
                  {{ getRoleName(scope.row.role) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="store_name" label="门店" />
            <el-table-column prop="status" label="状态" width="80">
              <template #default="scope">
                <el-tag :type="scope.row.is_active ? 'success' : 'danger'" size="small">
                  {{ scope.row.is_active ? '在职' : '离职' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button 
                  type="primary" 
                  size="small"
                  :disabled="scope.row.role === selectedRole?.code || !scope.row.is_active"
                  @click="confirmAssignRole(scope.row)"
                >
                  {{ scope.row.role === selectedRole?.code ? '已分配' : '分配' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="assignDialogVisible = false">取消</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 权限矩阵对话框 -->
    <el-dialog
      title="权限矩阵"
      v-model="matrixDialogVisible"
      width="95%"
      :close-on-click-modal="false"
      fullscreen
    >
      <div class="permission-matrix">
        <div class="matrix-toolbar">
          <el-input
            v-model="matrixSearchText"
            placeholder="搜索权限或分类..."
            style="width: 300px"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <div class="matrix-legend">
            <span class="legend-item">
              <el-icon color="#67c23a"><Check /></el-icon>
              有权限
            </span>
            <span class="legend-item">
              <el-icon color="#f56c6c"><Close /></el-icon>
              无权限
            </span>
          </div>
        </div>
        
        <div class="matrix-table">
          <el-table :data="matrixData" style="width: 100%" border stripe>
            <el-table-column prop="permission_name" label="权限名称" width="200" fixed="left" />
            <el-table-column prop="category" label="分类" width="120" />
            <el-table-column 
              v-for="role in businessRoles" 
              :key="role.code"
              :label="role.name"
              width="100"
              align="center"
            >
              <template #default="scope">
                <el-icon v-if="hasPermission(role.code, scope.row.permission_code)" color="#67c23a" size="16">
                  <Check />
                </el-icon>
                <el-icon v-else color="#f56c6c" size="16">
                  <Close />
                </el-icon>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- 角色统计对话框 -->
    <el-dialog
      title="角色统计分析"
      v-model="statisticsDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="role-statistics">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="chart-container">
              <h4>角色分布</h4>
              <div class="role-distribution">
                <div v-for="stat in roleStatistics" :key="stat.role_code" class="distribution-item">
                  <div class="distribution-info">
                    <span class="role-name">{{ stat.role_name }}</span>
                    <span class="employee-count">{{ stat.employee_count }} 人</span>
                  </div>
                  <div class="distribution-bar">
                    <div 
                      class="bar-fill" 
                      :style="{ width: (stat.employee_count / totalEmployees * 100) + '%' }"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="chart-container">
              <h4>权限级别分布</h4>
              <div class="permission-levels">
                <div v-for="level in permissionLevels" :key="level.name" class="level-item">
                  <el-tag :type="level.type" size="large">{{ level.name }}</el-tag>
                  <span class="level-count">{{ level.count }} 个角色</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 角色编辑对话框 -->
    <el-dialog
      :title="isEditMode ? '编辑角色' : '新增角色'"
      v-model="roleDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form :model="roleForm" :rules="roleRules" ref="roleFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="角色代码" prop="code">
              <el-input
                v-model="roleForm.code"
                placeholder="请输入角色代码"
                :disabled="isEditMode"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="角色名称" prop="name">
              <el-input v-model="roleForm.name" placeholder="请输入角色名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="角色级别" prop="level">
              <el-select v-model="roleForm.level" placeholder="请选择角色级别">
                <el-option label="基础级别" :value="1" />
                <el-option label="中级级别" :value="2" />
                <el-option label="高级级别" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sort_order">
              <el-input-number v-model="roleForm.sort_order" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="角色描述" prop="description">
          <el-input
            v-model="roleForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>

        <el-form-item label="是否启用" prop="is_active">
          <el-switch v-model="roleForm.is_active" />
        </el-form-item>

        <el-form-item label="权限配置">
          <div class="permission-selection">
            <div v-for="(permissions, category) in allPermissions" :key="category" class="permission-category">
              <div class="category-header">
                <el-checkbox
                  :indeterminate="isIndeterminate(category)"
                  v-model="checkAll[category]"
                  @change="handleCheckAllChange(category, $event)"
                >
                  {{ category }}
                </el-checkbox>
              </div>
              <div class="permission-list">
                <el-checkbox-group v-model="roleForm.permission_ids">
                  <el-checkbox
                    v-for="permission in permissions"
                    :key="permission.id"
                    :label="permission.id"
                    @change="handlePermissionChange(category)"
                  >
                    {{ permission.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="roleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRole" :loading="loading">
            {{ isEditMode ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 权限管理对话框 -->
    <el-dialog
      title="权限管理"
      v-model="permissionManagementDialogVisible"
      width="90%"
      :close-on-click-modal="false"
      fullscreen
    >
      <div class="permission-management">
        <div class="permission-toolbar">
          <el-button type="primary" @click="showCreatePermissionDialog">
            <el-icon><Plus /></el-icon>
            新增权限
          </el-button>
          <el-input
            v-model="permissionSearchText"
            placeholder="搜索权限..."
            style="width: 300px"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <el-table :data="filteredPermissions" style="width: 100%" border stripe>
          <el-table-column prop="name" label="权限名称" width="200" />
          <el-table-column prop="code" label="权限代码" width="200" />
          <el-table-column prop="category" label="分类" width="120" />
          <el-table-column prop="description" label="描述" />
          <el-table-column prop="is_active" label="状态" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.is_active ? 'success' : 'danger'" size="small">
                {{ scope.row.is_active ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="is_system" label="类型" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.is_system ? 'warning' : 'info'" size="small">
                {{ scope.row.is_system ? '系统' : '自定义' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" align="center">
            <template #default="scope">
              <el-button type="primary" size="small" @click="editPermission(scope.row)">
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="deletePermission(scope.row)"
                :disabled="scope.row.is_system"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 权限编辑对话框 -->
    <el-dialog
      :title="isEditPermissionMode ? '编辑权限' : '新增权限'"
      v-model="permissionEditDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="permissionForm" :rules="permissionRules" ref="permissionFormRef" label-width="100px">
        <el-form-item label="权限代码" prop="code">
          <el-input
            v-model="permissionForm.code"
            placeholder="请输入权限代码，如：store:read"
            :disabled="isEditPermissionMode"
          />
        </el-form-item>

        <el-form-item label="权限名称" prop="name">
          <el-input v-model="permissionForm.name" placeholder="请输入权限名称" />
        </el-form-item>

        <el-form-item label="权限分类" prop="category">
          <el-select v-model="permissionForm.category" placeholder="请选择权限分类" style="width: 100%">
            <el-option
              v-for="category in permissionCategories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="资源类型" prop="resource">
              <el-input v-model="permissionForm.resource" placeholder="如：store, customer" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作类型" prop="action">
              <el-input v-model="permissionForm.action" placeholder="如：read, create, update" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="权限描述" prop="description">
          <el-input
            v-model="permissionForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入权限描述"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否启用" prop="is_active">
              <el-switch v-model="permissionForm.is_active" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sort_order">
              <el-input-number v-model="permissionForm.sort_order" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="permissionEditDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="savePermission" :loading="loading">
            {{ isEditPermissionMode ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 角色员工名单对话框 -->
    <el-dialog
      title="角色员工名单"
      v-model="employeeDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="employee-dialog-content">
        <div class="role-info">
          <div class="role-header">
            <el-icon class="role-icon"><UserFilled /></el-icon>
            <div class="role-details">
              <h3>{{ selectedRoleEmployees.role_name }}</h3>
              <p>{{ selectedRoleEmployees.role_description }}</p>
            </div>
          </div>
          <div class="employee-count">
            总计 {{ selectedRoleEmployees.employees?.length || 0 }} 名员工
          </div>
        </div>

        <el-divider />

        <div class="employee-list" v-if="selectedRoleEmployees.employees?.length > 0">
          <el-table :data="selectedRoleEmployees.employees" style="width: 100%">
            <el-table-column prop="name" label="员工姓名" width="120" />
            <el-table-column prop="employee_id" label="员工编号" width="120" />
            <el-table-column prop="department" label="部门" width="100" />
            <el-table-column prop="position" label="职位" width="100" />
            <el-table-column prop="phone" label="联系电话" width="130" />
            <el-table-column prop="status" label="状态" width="80" align="center">
              <template #default="scope">
                <el-tag :type="scope.row.status === '在职' ? 'success' : 'info'" size="small">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="hire_date" label="入职时间" width="100" />
          </el-table>
        </div>

        <div class="empty-state" v-else>
          <el-empty description="该角色暂无员工" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  User, UserFilled, Key, Shop, Refresh, Grid, DataAnalysis, Search, Check, Close, View,
  Management, Money, Service, Tools, Phone, Star, Plus, Setting, Edit, Delete
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const rolePermissionDialogVisible = ref(false)  // 角色权限详情对话框
const permissionEditDialogVisible = ref(false)  // 权限编辑对话框
const employeeDialogVisible = ref(false)  // 员工名单对话框
const assignDialogVisible = ref(false)
const matrixDialogVisible = ref(false)
const statisticsDialogVisible = ref(false)
const roleDialogVisible = ref(false)
const permissionManagementDialogVisible = ref(false)
const employeeSearchText = ref('')
const matrixSearchText = ref('')
const isEditMode = ref(false)
const isEditPermissionMode = ref(false)
const currentRole = ref(null)
const currentPermission = ref(null)

const businessRoles = ref([])
const allPermissions = ref({})
const selectedRole = ref(null)
const selectedRolePermissions = ref(null)
const employees = ref([])
const permissionSearchText = ref('')
const checkAll = ref({})

// 表单数据
const roleForm = ref({
  code: '',
  name: '',
  description: '',
  level: 1,
  is_active: true,
  sort_order: 0,
  permission_ids: []
})

const roleFormRef = ref(null)
const permissionFormRef = ref(null)

// 员工名单数据
const selectedRoleEmployees = ref({
  role_code: '',
  role_name: '',
  role_description: '',
  employees: []
})

// 权限表单数据
const permissionForm = ref({
  code: '',
  name: '',
  description: '',
  category: '',
  resource: '',
  action: '',
  is_active: true,
  sort_order: 0
})

// 表单验证规则
const roleRules = {
  code: [
    { required: true, message: '请输入角色代码', trigger: 'blur' },
    { pattern: /^[a-z_]+$/, message: '角色代码只能包含小写字母和下划线', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择角色级别', trigger: 'change' }
  ]
}

const permissionRules = {
  code: [
    { required: true, message: '请输入权限代码', trigger: 'blur' },
    { pattern: /^[a-z_:]+$/, message: '权限代码只能包含小写字母、下划线和冒号', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择权限分类', trigger: 'change' }
  ]
}

// 模拟数据
const mockRoles = [
  {
    code: 'store_manager',
    name: '店长',
    description: '负责门店整体运营管理，拥有门店内大部分权限',
    permission_count: 45,
    permissions: ['store:read', 'store:update', 'employee:create', 'employee:read', 'employee:update', 'customer:create', 'customer:read', 'customer:update', 'order:create', 'order:read', 'order:update', 'order:refund', 'finance:read', 'finance:report']
  },
  {
    code: 'assistant_manager',
    name: '副店长',
    description: '协助店长管理门店，拥有部分管理权限',
    permission_count: 32,
    permissions: ['store:read', 'employee:read', 'employee:update', 'customer:create', 'customer:read', 'customer:update', 'order:create', 'order:read', 'order:update', 'finance:read']
  },
  {
    code: 'cashier',
    name: '收银员',
    description: '负责收银结账等基础操作',
    permission_count: 15,
    permissions: ['customer:create', 'customer:read', 'customer:update', 'order:create', 'order:read', 'cashier:checkout', 'cashier:shift_start', 'cashier:shift_end']
  },
  {
    code: 'senior_cashier',
    name: '高级收银员',
    description: '高级收银员，拥有更多收银相关权限',
    permission_count: 22,
    permissions: ['customer:create', 'customer:read', 'customer:update', 'member:create', 'member:read', 'order:create', 'order:read', 'order:refund', 'cashier:checkout', 'cashier:refund', 'cashier:discount']
  },
  {
    code: 'technician',
    name: '技师',
    description: '提供专业技师服务',
    permission_count: 8,
    permissions: ['customer:read', 'order:read', 'technician:service_start', 'technician:service_end', 'technician:customer_record']
  },
  {
    code: 'senior_technician',
    name: '高级技师',
    description: '高级技师，拥有更多服务权限',
    permission_count: 12,
    permissions: ['customer:read', 'customer:update', 'order:read', 'order:update', 'technician:service_start', 'technician:service_end', 'technician:customer_record']
  },
  {
    code: 'receptionist',
    name: '前台接待',
    description: '前台接待，负责客户接待和预约管理',
    permission_count: 18,
    permissions: ['customer:create', 'customer:read', 'customer:update', 'appointment:create', 'appointment:read', 'appointment:update', 'appointment:confirm']
  },
  {
    code: 'customer_service',
    name: '客服',
    description: '客户服务，处理客户问题和投诉',
    permission_count: 20,
    permissions: ['customer:create', 'customer:read', 'customer:update', 'member:read', 'appointment:create', 'appointment:read', 'appointment:update', 'order:read']
  }
]

const mockEmployees = [
  { id: 1, name: '张三', phone: '13800000001', role: 'store_manager', store_name: '总店', is_active: true },
  { id: 2, name: '李四', phone: '13800000002', role: 'cashier', store_name: '总店', is_active: true },
  { id: 3, name: '王五', phone: '13800000003', role: 'technician', store_name: '总店', is_active: true },
  { id: 4, name: '赵六', phone: '13800000004', role: 'senior_cashier', store_name: '分店A', is_active: true },
  { id: 5, name: '钱七', phone: '***********', role: 'receptionist', store_name: '分店A', is_active: true },
  { id: 6, name: '孙八', phone: '***********', role: 'senior_technician', store_name: '分店B', is_active: true },
  { id: 7, name: '周九', phone: '***********', role: 'customer_service', store_name: '分店B', is_active: false },
  { id: 8, name: '吴十', phone: '***********', role: 'assistant_manager', store_name: '分店C', is_active: true }
]

// 计算属性
const totalRoles = computed(() => businessRoles.value.length)
const totalEmployees = computed(() => employees.value.filter(emp => emp.is_active).length)
const totalPermissions = computed(() => {
  const allPerms = new Set()
  businessRoles.value.forEach(role => {
    role.permissions.forEach(perm => allPerms.add(perm))
  })
  return allPerms.size
})
const totalStores = computed(() => {
  const stores = new Set(employees.value.map(emp => emp.store_name))
  return stores.size
})

const filteredEmployees = computed(() => {
  if (!employeeSearchText.value) return employees.value
  
  const searchText = employeeSearchText.value.toLowerCase()
  return employees.value.filter(emp => 
    emp.name.toLowerCase().includes(searchText) ||
    emp.phone.includes(searchText) ||
    emp.store_name.toLowerCase().includes(searchText)
  )
})

const matrixData = computed(() => {
  if (!allPermissions.value) return []
  
  const data = []
  Object.entries(allPermissions.value).forEach(([category, permissions]) => {
    permissions.forEach(permission => {
      if (!matrixSearchText.value || 
          permission.name.toLowerCase().includes(matrixSearchText.value.toLowerCase()) ||
          category.toLowerCase().includes(matrixSearchText.value.toLowerCase())) {
        data.push({
          permission_code: permission.code,
          permission_name: permission.name,
          category: category
        })
      }
    })
  })
  
  return data
})

const roleStatistics = computed(() => {
  return businessRoles.value.map(role => ({
    role_code: role.code,
    role_name: role.name,
    employee_count: getEmployeeCountByRole(role.code),
    permission_count: role.permission_count
  })).sort((a, b) => b.employee_count - a.employee_count)
})

const permissionLevels = computed(() => {
  const levels = [
    { name: '高级权限', type: 'danger', count: 0 },
    { name: '中级权限', type: 'warning', count: 0 },
    { name: '基础权限', type: 'success', count: 0 }
  ]

  businessRoles.value.forEach(role => {
    if (role.permission_count >= 30) {
      levels[0].count++
    } else if (role.permission_count >= 15) {
      levels[1].count++
    } else {
      levels[2].count++
    }
  })

  return levels
})

const filteredPermissions = computed(() => {
  if (!permissionSearchText.value) {
    return Object.values(allPermissions.value).flat()
  }

  const searchText = permissionSearchText.value.toLowerCase()
  return Object.values(allPermissions.value).flat().filter(permission =>
    permission.name.toLowerCase().includes(searchText) ||
    permission.code.toLowerCase().includes(searchText) ||
    permission.category.toLowerCase().includes(searchText)
  )
})

const permissionCategories = computed(() => {
  return Object.keys(allPermissions.value)
})

// 方法
const getRoleTagType = (roleCode) => {
  if (roleCode.includes('manager')) return 'danger'
  if (roleCode.includes('senior') || roleCode.includes('chief')) return 'warning'
  if (roleCode.includes('cashier') || roleCode.includes('technician')) return 'success'
  return 'info'
}

const getRoleIcon = (roleCode) => {
  if (roleCode.includes('manager')) return Management
  if (roleCode.includes('cashier')) return Money
  if (roleCode.includes('technician')) return Tools
  if (roleCode.includes('service')) return Phone
  if (roleCode.includes('receptionist')) return Service
  return User
}

const getRoleIconColor = (roleCode) => {
  if (roleCode.includes('manager')) return '#F56C6C'
  if (roleCode.includes('cashier')) return '#67C23A'
  if (roleCode.includes('technician')) return '#409EFF'
  if (roleCode.includes('service')) return '#E6A23C'
  return '#909399'
}

const getRoleName = (roleCode) => {
  const role = businessRoles.value.find(r => r.code === roleCode)
  return role ? role.name : roleCode
}

const getRoleLevel = (roleCode) => {
  const role = businessRoles.value.find(r => r.code === roleCode)
  if (!role) return '未知'

  if (role.permission_count >= 30) return '高级'
  if (role.permission_count >= 15) return '中级'
  return '基础'
}

const getRoleLevelType = (roleCode) => {
  const level = getRoleLevel(roleCode)
  switch (level) {
    case '高级': return 'danger'
    case '中级': return 'warning'
    case '基础': return 'success'
    default: return 'info'
  }
}

const getEmployeeCountByRole = (roleCode) => {
  return employees.value.filter(emp => emp.role === roleCode && emp.is_active).length
}

const hasPermission = (roleCode, permissionCode) => {
  const role = businessRoles.value.find(r => r.code === roleCode)
  return role ? role.permissions.includes(permissionCode) : false
}

// 初始化数据
const initData = () => {
  businessRoles.value = mockRoles
  employees.value = mockEmployees
  
  // 模拟权限数据（添加ID字段）
  let permissionId = 1
  allPermissions.value = {
    '门店管理': [
      { id: permissionId++, code: 'store:read', name: '查看门店', is_active: true, is_system: true, category: '门店管理' },
      { id: permissionId++, code: 'store:update', name: '编辑门店', is_active: true, is_system: true, category: '门店管理' }
    ],
    '员工管理': [
      { id: permissionId++, code: 'employee:create', name: '创建员工', is_active: true, is_system: true, category: '员工管理' },
      { id: permissionId++, code: 'employee:read', name: '查看员工', is_active: true, is_system: true, category: '员工管理' },
      { id: permissionId++, code: 'employee:update', name: '编辑员工', is_active: true, is_system: true, category: '员工管理' }
    ],
    '客户管理': [
      { id: permissionId++, code: 'customer:create', name: '创建客户', is_active: true, is_system: true, category: '客户管理' },
      { id: permissionId++, code: 'customer:read', name: '查看客户', is_active: true, is_system: true, category: '客户管理' },
      { id: permissionId++, code: 'customer:update', name: '编辑客户', is_active: true, is_system: true, category: '客户管理' }
    ],
    '订单管理': [
      { id: permissionId++, code: 'order:create', name: '创建订单', is_active: true, is_system: true, category: '订单管理' },
      { id: permissionId++, code: 'order:read', name: '查看订单', is_active: true, is_system: true, category: '订单管理' },
      { id: permissionId++, code: 'order:update', name: '编辑订单', is_active: true, is_system: true, category: '订单管理' },
      { id: permissionId++, code: 'order:refund', name: '订单退款', is_active: true, is_system: true, category: '订单管理' }
    ],
    '收银管理': [
      { id: permissionId++, code: 'cashier:checkout', name: '收银结账', is_active: true, is_system: true, category: '收银管理' },
      { id: permissionId++, code: 'cashier:refund', name: '退款操作', is_active: true, is_system: true, category: '收银管理' },
      { id: permissionId++, code: 'cashier:discount', name: '折扣操作', is_active: true, is_system: true, category: '收银管理' },
      { id: permissionId++, code: 'cashier:shift_start', name: '开班', is_active: true, is_system: true, category: '收银管理' },
      { id: permissionId++, code: 'cashier:shift_end', name: '交班', is_active: true, is_system: true, category: '收银管理' }
    ],
    '技师服务': [
      { id: permissionId++, code: 'technician:service_start', name: '开始服务', is_active: true, is_system: true, category: '技师服务' },
      { id: permissionId++, code: 'technician:service_end', name: '结束服务', is_active: true, is_system: true, category: '技师服务' },
      { id: permissionId++, code: 'technician:customer_record', name: '客户记录', is_active: true, is_system: true, category: '技师服务' }
    ],
    '预约管理': [
      { id: permissionId++, code: 'appointment:create', name: '创建预约', is_active: true, is_system: true, category: '预约管理' },
      { id: permissionId++, code: 'appointment:read', name: '查看预约', is_active: true, is_system: true, category: '预约管理' },
      { id: permissionId++, code: 'appointment:update', name: '编辑预约', is_active: true, is_system: true, category: '预约管理' },
      { id: permissionId++, code: 'appointment:confirm', name: '确认预约', is_active: true, is_system: true, category: '预约管理' }
    ],
    '会员管理': [
      { id: permissionId++, code: 'member:create', name: '创建会员', is_active: true, is_system: true, category: '会员管理' },
      { id: permissionId++, code: 'member:read', name: '查看会员', is_active: true, is_system: true, category: '会员管理' }
    ],
    '财务管理': [
      { id: permissionId++, code: 'finance:read', name: '查看财务', is_active: true, is_system: true, category: '财务管理' },
      { id: permissionId++, code: 'finance:report', name: '财务报表', is_active: true, is_system: true, category: '财务管理' }
    ]
  }
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    initData()
    loading.value = false
    ElMessage.success('数据刷新成功')
  }, 1000)
}

const viewRolePermissions = (role) => {
  // 模拟获取角色权限详情
  selectedRolePermissions.value = {
    role_code: role.code,
    role_name: role.name,
    role_description: role.description,
    total_permissions: role.permission_count,
    permissions: {}
  }

  // 按分类组织权限
  Object.entries(allPermissions.value).forEach(([category, permissions]) => {
    const rolePermissions = permissions.filter(perm => role.permissions.includes(perm.code))
    if (rolePermissions.length > 0) {
      selectedRolePermissions.value.permissions[category] = rolePermissions
    }
  })

  rolePermissionDialogVisible.value = true
}

const assignRole = (role) => {
  selectedRole.value = role
  assignDialogVisible.value = true
}

const confirmAssignRole = async (employee) => {
  try {
    await ElMessageBox.confirm(
      `确定要将角色 "${selectedRole.value.name}" 分配给员工 "${employee.name}" 吗？`,
      '确认分配',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 模拟API调用
    const emp = employees.value.find(e => e.id === employee.id)
    if (emp) {
      emp.role = selectedRole.value.code
    }
    
    ElMessage.success('角色分配成功')
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('分配角色失败')
    }
  }
}

const viewRoleEmployees = (role) => {
  // 获取该角色的员工列表
  const roleEmployees = employees.value.filter(emp => emp.role === role.code && emp.is_active)

  // 设置员工名单数据
  selectedRoleEmployees.value = {
    role_code: role.code,
    role_name: role.name,
    role_description: role.description,
    employees: roleEmployees.map(emp => ({
      name: emp.name,
      employee_id: emp.employee_id,
      department: emp.department || '未分配',
      position: emp.position || '未设置',
      phone: emp.phone || '未填写',
      status: emp.is_active ? '在职' : '离职',
      hire_date: emp.hire_date || '未知'
    }))
  }

  // 显示员工名单对话框
  employeeDialogVisible.value = true
}

const showPermissionMatrix = () => {
  matrixDialogVisible.value = true
}

const showRoleStatistics = () => {
  statisticsDialogVisible.value = true
}

const searchEmployees = () => {
  // 搜索逻辑在计算属性中处理
}

// ==================== 新增的方法 ====================

const showCreateRoleDialog = () => {
  isEditMode.value = false
  currentRole.value = null
  resetRoleForm()
  roleDialogVisible.value = true
}

const editRole = (role) => {
  isEditMode.value = true
  currentRole.value = role

  // 获取角色级别数字值
  const roleLevel = getRoleLevel(role.code)
  let levelValue = 1
  switch (roleLevel) {
    case '高级': levelValue = 3; break
    case '中级': levelValue = 2; break
    case '基础': levelValue = 1; break
    default: levelValue = 1
  }

  // 填充表单数据
  roleForm.value = {
    code: role.code,
    name: role.name,
    description: role.description || '',
    level: levelValue,
    is_active: role.is_active !== false,
    sort_order: role.sort_order || 0,
    permission_ids: role.permissions ? role.permissions.map(p => getPermissionIdByCode(p)).filter(id => id !== null) : []
  }

  // 更新权限选择状态
  nextTick(() => {
    Object.keys(allPermissions.value).forEach(category => {
      updateCheckAllStatus(category)
    })
  })

  roleDialogVisible.value = true
}

const resetRoleForm = () => {
  roleForm.value = {
    code: '',
    name: '',
    description: '',
    level: 1,
    is_active: true,
    sort_order: 0,
    permission_ids: []
  }

  // 重置权限选择状态
  checkAll.value = {}
  Object.keys(allPermissions.value).forEach(category => {
    checkAll.value[category] = false
  })
}

const saveRole = async () => {
  if (!roleFormRef.value) return

  try {
    await roleFormRef.value.validate()
    loading.value = true

    if (isEditMode.value) {
      // 更新角色
      await updateRole()
    } else {
      // 创建角色
      await createRole()
    }

    roleDialogVisible.value = false
    await refreshData()
    ElMessage.success(isEditMode.value ? '角色更新成功' : '角色创建成功')

  } catch (error) {
    console.error('保存角色失败:', error)
    ElMessage.error('保存角色失败')
  } finally {
    loading.value = false
  }
}

const createRole = async () => {
  // 这里应该调用API创建角色
  console.log('创建角色:', roleForm.value)
  // 模拟API调用
  await new Promise(resolve => setTimeout(resolve, 1000))
}

const updateRole = async () => {
  // 这里应该调用API更新角色
  console.log('更新角色:', roleForm.value)
  // 模拟API调用
  await new Promise(resolve => setTimeout(resolve, 1000))
}

const deleteRole = async (role) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色 "${role.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    loading.value = true

    // 这里应该调用API删除角色
    console.log('删除角色:', role)
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    await refreshData()
    ElMessage.success('角色删除成功')

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除角色失败:', error)
      ElMessage.error('删除角色失败')
    }
  } finally {
    loading.value = false
  }
}

const showPermissionManagement = () => {
  permissionManagementDialogVisible.value = true
}

const showCreatePermissionDialog = () => {
  isEditPermissionMode.value = false
  currentPermission.value = null
  resetPermissionForm()
  permissionEditDialogVisible.value = true
}

const editPermission = (permission) => {
  isEditPermissionMode.value = true
  currentPermission.value = permission

  // 填充表单数据
  permissionForm.value = {
    code: permission.code,
    name: permission.name,
    description: permission.description || '',
    category: permission.category,
    resource: permission.resource || '',
    action: permission.action || '',
    is_active: permission.is_active !== false,
    sort_order: permission.sort_order || 0
  }

  permissionEditDialogVisible.value = true
}

const deletePermission = async (permission) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除权限 "${permission.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    loading.value = true

    // 这里应该调用API删除权限
    console.log('删除权限:', permission)

    // 模拟API调用 - 从本地数据中删除
    for (const category in allPermissions.value) {
      const permissions = allPermissions.value[category]
      const index = permissions.findIndex(p => p.id === permission.id)
      if (index !== -1) {
        permissions.splice(index, 1)
        break
      }
    }

    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('权限删除成功')

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除权限失败:', error)
      ElMessage.error('删除权限失败')
    }
  } finally {
    loading.value = false
  }
}

// 权限选择相关方法
const isIndeterminate = (category) => {
  const permissions = allPermissions.value[category] || []
  const selectedCount = permissions.filter(p => roleForm.value.permission_ids.includes(p.id)).length
  return selectedCount > 0 && selectedCount < permissions.length
}

const handleCheckAllChange = (category, checked) => {
  const permissions = allPermissions.value[category] || []

  if (checked) {
    // 添加该分类下的所有权限
    permissions.forEach(permission => {
      if (!roleForm.value.permission_ids.includes(permission.id)) {
        roleForm.value.permission_ids.push(permission.id)
      }
    })
  } else {
    // 移除该分类下的所有权限
    const permissionIds = permissions.map(p => p.id)
    roleForm.value.permission_ids = roleForm.value.permission_ids.filter(
      id => !permissionIds.includes(id)
    )
  }
}

const handlePermissionChange = (category) => {
  const permissions = allPermissions.value[category] || []
  const selectedCount = permissions.filter(p => roleForm.value.permission_ids.includes(p.id)).length
  checkAll.value[category] = selectedCount === permissions.length
}

const getPermissionIdByCode = (code) => {
  for (const permissions of Object.values(allPermissions.value)) {
    const permission = permissions.find(p => p.code === code)
    if (permission) return permission.id
  }
  return null
}

// 权限管理相关方法
const resetPermissionForm = () => {
  permissionForm.value = {
    code: '',
    name: '',
    description: '',
    category: '',
    resource: '',
    action: '',
    is_active: true,
    sort_order: 0
  }
}

const savePermission = async () => {
  if (!permissionFormRef.value) return

  try {
    await permissionFormRef.value.validate()
    loading.value = true

    if (isEditPermissionMode.value) {
      // 更新权限
      await updatePermission()
    } else {
      // 创建权限
      await createPermission()
    }

    permissionEditDialogVisible.value = false
    await refreshData()
    ElMessage.success(isEditPermissionMode.value ? '权限更新成功' : '权限创建成功')

  } catch (error) {
    console.error('保存权限失败:', error)
    ElMessage.error('保存权限失败')
  } finally {
    loading.value = false
  }
}

const createPermission = async () => {
  // 这里应该调用API创建权限
  console.log('创建权限:', permissionForm.value)

  // 模拟API调用 - 添加到本地数据
  const newPermission = {
    id: Date.now(), // 临时ID
    ...permissionForm.value,
    is_system: false
  }

  if (!allPermissions.value[permissionForm.value.category]) {
    allPermissions.value[permissionForm.value.category] = []
  }
  allPermissions.value[permissionForm.value.category].push(newPermission)

  await new Promise(resolve => setTimeout(resolve, 1000))
}

const updatePermission = async () => {
  // 这里应该调用API更新权限
  console.log('更新权限:', permissionForm.value)

  // 模拟API调用 - 更新本地数据
  for (const category in allPermissions.value) {
    const permissions = allPermissions.value[category]
    const index = permissions.findIndex(p => p.id === currentPermission.value.id)
    if (index !== -1) {
      permissions[index] = {
        ...permissions[index],
        ...permissionForm.value
      }
      break
    }
  }

  await new Promise(resolve => setTimeout(resolve, 1000))
}

// 初始化
onMounted(() => {
  initData()
})
</script>

<style scoped>
.business-role-management {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 24px;
}

.stats-card {
  height: 100px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  margin-right: 16px;
  padding: 12px;
  border-radius: 8px;
  background-color: #f5f7fa;
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.toolbar {
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
}

.roles-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.role-info-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.role-icon-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.role-name {
  font-weight: 600;
  color: #303133;
}

.role-code {
  font-size: 12px;
  color: #909399;
}

.employee-count {
  font-weight: 600;
  color: #303133;
}

.action-buttons {
  display: flex;
  gap: 6px;
  justify-content: center;
  flex-wrap: wrap;
  align-items: center;
}

.action-buttons .el-button {
  min-width: 70px;
}

.employee-count-btn {
  color: #409eff;
  font-weight: 500;
  padding: 0;
  min-height: auto;
}

.employee-count-btn:hover:not(:disabled) {
  color: #66b1ff;
}

.employee-count-btn:disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}

.employee-dialog-content .role-info {
  margin-bottom: 16px;
}

.employee-dialog-content .role-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.employee-dialog-content .role-icon {
  font-size: 24px;
  color: #409eff;
}

.employee-dialog-content .role-details h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #303133;
}

.employee-dialog-content .role-details p {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.employee-dialog-content .employee-count {
  font-size: 14px;
  color: #909399;
}

.employee-dialog-content .empty-state {
  text-align: center;
  padding: 40px 0;
}

.permission-detail {
  max-height: 600px;
  overflow-y: auto;
}

.role-info-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.role-info h3 {
  margin: 0 0 4px 0;
  color: #303133;
}

.role-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.permission-categories {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.permission-category {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 16px;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.category-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.permission-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.permission-tag {
  margin: 0;
}

.assign-role-content {
  max-height: 600px;
  overflow-y: auto;
}

.employee-search {
  margin-bottom: 16px;
}

.employee-list {
  max-height: 400px;
  overflow-y: auto;
}

.permission-matrix {
  height: 80vh;
  display: flex;
  flex-direction: column;
}

.matrix-toolbar {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.matrix-legend {
  display: flex;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #606266;
}

.matrix-table {
  flex: 1;
  overflow: auto;
}

.role-statistics {
  padding: 16px 0;
}

.chart-container {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
}

.chart-container h4 {
  margin: 0 0 16px 0;
  color: #303133;
}

.role-distribution {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.distribution-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.distribution-info {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.role-name {
  color: #303133;
  font-weight: 500;
}

.employee-count {
  color: #409EFF;
  font-weight: 600;
}

.distribution-bar {
  height: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background-color: #409EFF;
  transition: width 0.3s ease;
}

.permission-levels {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.level-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.level-count {
  font-size: 14px;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 新增样式 */
.permission-selection {
  max-height: 450px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafbfc;
}

.permission-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 12px;
  align-items: start;
}

.permission-category {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 10px;
  background-color: #ffffff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
  min-height: 120px;
}

.permission-category:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
  border-color: #c6e2ff;
}

.category-header {
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #e4e7ed;
}

.category-header .el-checkbox {
  font-weight: 600;
  color: #303133;
}

.permission-list {
  padding-left: 0;
}

.permission-list .el-checkbox {
  display: block;
  margin-bottom: 4px;
  margin-right: 0;
  font-size: 12px;
  line-height: 1.4;
}

.permission-list .el-checkbox:last-child {
  margin-bottom: 0;
}

.permission-list .el-checkbox .el-checkbox__label {
  padding-left: 6px;
}

.permission-management {
  height: 80vh;
  display: flex;
  flex-direction: column;
}

.permission-toolbar {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.permission-toolbar .el-input {
  margin-left: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .role-card {
    height: auto;
  }
  
  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .role-actions {
    flex-direction: column;
  }
  
  .stats-row .el-col {
    margin-bottom: 12px;
  }
}
</style>
