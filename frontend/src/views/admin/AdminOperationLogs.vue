<template>
  <div class="admin-operation-logs">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>操作日志</h1>
      <p>查看系统操作记录和审计日志</p>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filters">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索操作内容、用户名"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.action_type" placeholder="操作类型" clearable @change="handleSearch">
            <el-option label="全部" value="" />
            <el-option label="登录" value="login" />
            <el-option label="登出" value="logout" />
            <el-option label="创建" value="create" />
            <el-option label="更新" value="update" />
            <el-option label="删除" value="delete" />
            <el-option label="查看" value="view" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.user_type" placeholder="用户类型" clearable @change="handleSearch">
            <el-option label="全部" value="" />
            <el-option label="管理员" value="admin" />
            <el-option label="员工" value="employee" />
            <el-option label="客户" value="customer" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="searchForm.date_range"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="handleSearch"
          />
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮 -->
    <div class="toolbar">
      <el-button @click="refreshLogs">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
      <el-button type="success" @click="exportLogs">
        <el-icon><Download /></el-icon>
        导出日志
      </el-button>
      <el-button type="danger" @click="clearLogs">
        <el-icon><Delete /></el-icon>
        清理日志
      </el-button>
    </div>

    <!-- 日志列表 -->
    <div class="logs-table">
      <el-table :data="logs" v-loading="loading" stripe border>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="action_type" label="操作类型" width="100">
          <template #default="scope">
            <el-tag :type="getActionTypeTag(scope.row.action_type)">
              {{ getActionTypeName(scope.row.action_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="操作描述" min-width="200" />
        <el-table-column prop="user_info" label="操作用户" width="150">
          <template #default="scope">
            <div class="user-info">
              <div class="user-name">{{ scope.row.user_name || '未知用户' }}</div>
              <div class="user-type">
                <el-tag size="small" :type="getUserTypeTag(scope.row.user_type)">
                  {{ getUserTypeName(scope.row.user_type) }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="ip_address" label="IP地址" width="130" />
        <el-table-column prop="user_agent" label="用户代理" width="200" show-overflow-tooltip />
        <el-table-column prop="created_at" label="操作时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'" size="small">
              {{ scope.row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="viewLogDetail(scope.row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 日志详情对话框 -->
    <el-dialog
      title="操作日志详情"
      v-model="detailDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedLog" class="log-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="操作ID">{{ selectedLog.id }}</el-descriptions-item>
          <el-descriptions-item label="操作类型">
            <el-tag :type="getActionTypeTag(selectedLog.action_type)">
              {{ getActionTypeName(selectedLog.action_type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="操作描述" :span="2">{{ selectedLog.description }}</el-descriptions-item>
          <el-descriptions-item label="操作用户">{{ selectedLog.user_name || '未知用户' }}</el-descriptions-item>
          <el-descriptions-item label="用户类型">
            <el-tag :type="getUserTypeTag(selectedLog.user_type)">
              {{ getUserTypeName(selectedLog.user_type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="IP地址">{{ selectedLog.ip_address }}</el-descriptions-item>
          <el-descriptions-item label="操作时间">{{ formatDateTime(selectedLog.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedLog.status === 'success' ? 'success' : 'danger'">
              {{ selectedLog.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="用户代理" :span="2">{{ selectedLog.user_agent }}</el-descriptions-item>
        </el-descriptions>

        <div v-if="selectedLog.details" class="log-details-section">
          <h4>详细信息</h4>
          <el-input
            type="textarea"
            :rows="8"
            :value="JSON.stringify(selectedLog.details, null, 2)"
            readonly
          />
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Download, Delete } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const detailDialogVisible = ref(false)
const selectedLog = ref(null)

const searchForm = reactive({
  keyword: '',
  action_type: '',
  user_type: '',
  date_range: null
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const logs = ref([])

// 模拟数据
const mockLogs = [
  {
    id: 1,
    action_type: 'login',
    description: '管理员登录系统',
    user_name: 'admin',
    user_type: 'admin',
    ip_address: '************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    created_at: '2025-07-21 14:30:15',
    status: 'success',
    details: { login_method: 'password', session_id: 'sess_123456' }
  },
  {
    id: 2,
    action_type: 'view',
    description: '查看业务角色管理页面',
    user_name: 'admin',
    user_type: 'admin',
    ip_address: '************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    created_at: '2025-07-21 14:32:20',
    status: 'success',
    details: { page: '/admin/business-roles', action: 'page_view' }
  },
  {
    id: 3,
    action_type: 'view',
    description: '查看权限矩阵',
    user_name: 'admin',
    user_type: 'admin',
    ip_address: '************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    created_at: '2025-07-21 14:33:45',
    status: 'success',
    details: { action: 'view_permission_matrix', roles_count: 8 }
  },
  {
    id: 4,
    action_type: 'view',
    description: '查看系统配置',
    user_name: 'admin',
    user_type: 'admin',
    ip_address: '************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    created_at: '2025-07-21 14:35:10',
    status: 'success',
    details: { page: '/admin/configs', configs_count: 15 }
  },
  {
    id: 5,
    action_type: 'create',
    description: '创建新的系统配置',
    user_name: 'admin',
    user_type: 'admin',
    ip_address: '************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    created_at: '2025-07-21 14:20:30',
    status: 'success',
    details: { config_key: 'new_feature.enabled', config_value: 'true' }
  }
]

// 方法
const getActionTypeTag = (type) => {
  const tagMap = {
    login: 'success',
    logout: 'info',
    create: 'success',
    update: 'warning',
    delete: 'danger',
    view: 'info'
  }
  return tagMap[type] || 'info'
}

const getActionTypeName = (type) => {
  const nameMap = {
    login: '登录',
    logout: '登出',
    create: '创建',
    update: '更新',
    delete: '删除',
    view: '查看'
  }
  return nameMap[type] || type
}

const getUserTypeTag = (type) => {
  const tagMap = {
    admin: 'danger',
    employee: 'warning',
    customer: 'success'
  }
  return tagMap[type] || 'info'
}

const getUserTypeName = (type) => {
  const nameMap = {
    admin: '管理员',
    employee: '员工',
    customer: '客户'
  }
  return nameMap[type] || type
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const fetchLogs = () => {
  loading.value = true
  
  // 模拟API调用
  setTimeout(() => {
    logs.value = mockLogs
    pagination.total = mockLogs.length
    loading.value = false
  }, 1000)
}

const handleSearch = () => {
  pagination.page = 1
  fetchLogs()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    action_type: '',
    user_type: '',
    date_range: null
  })
  handleSearch()
}

const refreshLogs = () => {
  fetchLogs()
  ElMessage.success('日志已刷新')
}

const exportLogs = async () => {
  try {
    // 构建导出参数
    const params = {
      ...searchForm,
      page: 1,
      size: 10000 // 导出所有数据
    }

    // 处理日期范围
    if (searchForm.date_range && searchForm.date_range.length === 2) {
      params.start_date = searchForm.date_range[0]
      params.end_date = searchForm.date_range[1]
      delete params.date_range
    }

    // 调用导出API
    const response = await fetch('/api/v1/admin/logs/operations/export', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params)
    })

    if (response.ok) {
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `操作日志_${new Date().toISOString().slice(0, 10)}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      ElMessage.success('日志导出成功')
    } else {
      throw new Error('导出失败')
    }
  } catch (error) {
    console.error('导出日志失败:', error)
    ElMessage.error('导出日志失败')
  }
}

const clearLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清理历史日志吗？此操作不可恢复！',
      '确认清理',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    ElMessage.success('日志清理成功')
  } catch {
    // 用户取消
  }
}

const viewLogDetail = (log) => {
  selectedLog.value = log
  detailDialogVisible.value = true
}

const handleSizeChange = (size) => {
  pagination.size = size
  fetchLogs()
}

const handlePageChange = (page) => {
  pagination.page = page
  fetchLogs()
}

// 初始化
onMounted(() => {
  fetchLogs()
})
</script>

<style scoped>
.admin-operation-logs {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-filters {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.toolbar {
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
}

.logs-table {
  background: white;
  border-radius: 6px;
  overflow: hidden;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-name {
  font-weight: 500;
  color: #303133;
}

.pagination {
  padding: 20px;
  display: flex;
  justify-content: center;
}

.log-detail {
  max-height: 600px;
  overflow-y: auto;
}

.log-details-section {
  margin-top: 20px;
}

.log-details-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-filters .el-row {
    flex-direction: column;
  }
  
  .search-filters .el-col {
    margin-bottom: 12px;
  }
  
  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
