<template>
  <div class="admin-permission-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>权限管理</h1>
      <p>查看和管理系统权限</p>
    </div>

    <!-- 操作栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" :icon="Plus" @click="createPermission">
          创建权限
        </el-button>
        <el-button type="success" :icon="UserFilled" @click="batchAssignPermissions" :disabled="selectedPermissions.length === 0">
          批量分配
        </el-button>
        <el-button type="warning" :icon="Download" @click="exportPermissions">
          导出权限
        </el-button>
        <el-button type="info" :icon="Refresh" @click="fetchPermissions">
          刷新
        </el-button>
      </div>

      <div class="toolbar-right">
        <el-input
          v-model="searchText"
          placeholder="搜索权限..."
          style="width: 300px"
          clearable
          :prefix-icon="Search"
        />
      </div>
    </div>

    <!-- 权限列表 -->
    <div class="permissions-container">
      <div
        v-for="(categoryPerms, category) in filteredPermissions"
        :key="category"
        class="permission-category"
      >
        <div class="category-header">
          <h3>{{ category }}</h3>
          <el-tag type="info">{{ categoryPerms.length }} 个权限</el-tag>
        </div>

        <el-table
          :data="categoryPerms"
          style="width: 100%; margin-bottom: 24px;"
          border
          stripe
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="code" label="权限代码" width="180">
            <template #default="scope">
              <el-tag size="small">{{ scope.row.code }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="权限名称" width="150" />
          <el-table-column prop="description" label="权限描述" min-width="250">
            <template #default="scope">
              <span>{{ scope.row.description || '暂无描述' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="80" align="center">
            <template #default="scope">
              <el-switch
                v-model="scope.row.is_active"
                @change="togglePermissionStatus(scope.row)"
                :disabled="scope.row.is_system"
              />
            </template>
          </el-table-column>
          <el-table-column label="系统权限" width="80" align="center">
            <template #default="scope">
              <el-tag v-if="scope.row.is_system" type="danger" size="small">系统</el-tag>
              <el-tag v-else type="success" size="small">自定义</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="280" align="center">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                @click="viewPermissionDetails(scope.row)"
              >
                详情
              </el-button>
              <el-button
                type="warning"
                size="small"
                @click="editPermission(scope.row)"
                :disabled="scope.row.is_system"
              >
                编辑
              </el-button>
              <el-button
                type="success"
                size="small"
                @click="assignPermissionToRoles(scope.row)"
              >
                分配角色
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="deletePermission(scope.row)"
                :disabled="scope.row.is_system"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 权限详情对话框 -->
    <el-dialog
      title="权限详情"
      v-model="detailDialogVisible"
      width="500px"
    >
      <div v-if="selectedPermission" class="permission-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="权限代码">
            <el-tag>{{ selectedPermission.code }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="权限名称">
            {{ selectedPermission.name }}
          </el-descriptions-item>
          <el-descriptions-item label="权限分类">
            {{ selectedPermission.category }}
          </el-descriptions-item>
          <el-descriptions-item label="权限描述">
            {{ selectedPermission.description || '暂无描述' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 使用角色对话框 -->
    <el-dialog
      title="使用此权限的角色"
      v-model="rolesDialogVisible"
      width="600px"
    >
      <div v-if="permissionRoles.length > 0" class="roles-list">
        <el-table :data="permissionRoles" style="width: 100%">
          <el-table-column prop="name" label="角色名称" width="150" />
          <el-table-column prop="display_name" label="显示名称" width="150" />
          <el-table-column prop="description" label="描述" />
          <el-table-column prop="is_active" label="状态" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
                {{ scope.row.is_active ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-else class="no-roles">
        <el-empty description="暂无角色使用此权限" />
      </div>
    </el-dialog>

    <!-- 创建/编辑权限对话框 -->
    <el-dialog
      :title="editingPermission ? '编辑权限' : '创建权限'"
      v-model="permissionFormDialogVisible"
      width="600px"
    >
      <el-form
        ref="permissionFormRef"
        :model="permissionForm"
        :rules="permissionFormRules"
        label-width="100px"
      >
        <el-form-item label="权限代码" prop="code">
          <el-input
            v-model="permissionForm.code"
            placeholder="请输入权限代码，如：user:create"
            :disabled="editingPermission"
          />
        </el-form-item>
        <el-form-item label="权限名称" prop="name">
          <el-input
            v-model="permissionForm.name"
            placeholder="请输入权限名称"
          />
        </el-form-item>
        <el-form-item label="权限分类" prop="category">
          <el-select
            v-model="permissionForm.category"
            placeholder="请选择权限分类"
            style="width: 100%"
          >
            <el-option label="系统管理" value="系统管理" />
            <el-option label="用户管理" value="用户管理" />
            <el-option label="角色管理" value="角色管理" />
            <el-option label="业务管理" value="业务管理" />
            <el-option label="数据维护" value="数据维护" />
            <el-option label="日志管理" value="日志管理" />
            <el-option label="系统监控" value="系统监控" />
          </el-select>
        </el-form-item>
        <el-form-item label="权限描述" prop="description">
          <el-input
            v-model="permissionForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入权限描述"
          />
        </el-form-item>
        <el-form-item label="是否启用" prop="is_active">
          <el-switch v-model="permissionForm.is_active" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="permissionFormDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="savePermission">
            {{ editingPermission ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量分配权限对话框 -->
    <el-dialog
      title="批量分配权限"
      v-model="batchAssignDialogVisible"
      width="600px"
    >
      <div>
        <p>已选择 {{ selectedPermissions.length }} 个权限</p>
        <el-form label-width="100px">
          <el-form-item label="选择角色">
            <el-select
              v-model="selectedRoles"
              multiple
              placeholder="请选择要分配的角色"
              style="width: 100%"
            >
              <el-option
                v-for="role in allRoles"
                :key="role.id"
                :label="role.display_name || role.name"
                :value="role.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchAssignDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmBatchAssign">确认分配</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 单个权限分配角色对话框 -->
    <el-dialog
      title="分配权限到角色"
      v-model="roleAssignDialogVisible"
      width="600px"
    >
      <div v-if="selectedPermission">
        <div class="permission-info">
          <h4>权限信息</h4>
          <p><strong>权限代码：</strong>{{ selectedPermission.code }}</p>
          <p><strong>权限名称：</strong>{{ selectedPermission.name }}</p>
          <p><strong>权限描述：</strong>{{ selectedPermission.description }}</p>
        </div>

        <el-divider />

        <el-form label-width="100px">
          <el-form-item label="选择角色">
            <el-select
              v-model="selectedRoles"
              multiple
              placeholder="请选择要分配的角色"
              style="width: 100%"
            >
              <el-option
                v-for="role in allRoles"
                :key="role.id"
                :label="role.display_name || role.name"
                :value="role.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="roleAssignDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmRoleAssign">确认分配</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Search, Plus, UserFilled, Download } from '@element-plus/icons-vue'
import { getAllPermissions, getAdminRoles } from '@/api/admin'

// 响应式数据
const loading = ref(false)
const detailDialogVisible = ref(false)
const rolesDialogVisible = ref(false)
const searchText = ref('')
const allPermissions = ref({})
const roles = ref([])
const selectedPermission = ref(null)
const permissionRoles = ref([])

// 新增的管理功能数据
const selectedPermissions = ref([])
const permissionFormDialogVisible = ref(false)
const batchAssignDialogVisible = ref(false)
const roleAssignDialogVisible = ref(false)
const editingPermission = ref(false)
const allRoles = ref([])
const selectedRoles = ref([])
const permissionFormRef = ref(null)

// 权限表单数据
const permissionForm = ref({
  code: '',
  name: '',
  category: '',
  description: '',
  is_active: true
})

// 表单验证规则
const permissionFormRules = {
  code: [
    { required: true, message: '请输入权限代码', trigger: 'blur' },
    { pattern: /^[a-zA-Z_][a-zA-Z0-9_:]*$/, message: '权限代码格式不正确', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择权限分类', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入权限描述', trigger: 'blur' }
  ]
}

// 计算属性 - 过滤后的权限
const filteredPermissions = computed(() => {
  if (!searchText.value) {
    return allPermissions.value
  }

  const filtered = {}
  const searchLower = searchText.value.toLowerCase()

  Object.entries(allPermissions.value).forEach(([category, permissions]) => {
    const filteredPerms = permissions.filter(perm => 
      perm.name.toLowerCase().includes(searchLower) ||
      perm.code.toLowerCase().includes(searchLower) ||
      (perm.description && perm.description.toLowerCase().includes(searchLower))
    )
    
    if (filteredPerms.length > 0) {
      filtered[category] = filteredPerms
    }
  })

  return filtered
})

// 获取所有权限
const fetchPermissions = async () => {
  loading.value = true
  try {
    const response = await getAllPermissions()
    allPermissions.value = response.data || response || {}
  } catch (error) {
    console.error('获取权限列表失败:', error)
    ElMessage.error('获取权限列表失败')
  } finally {
    loading.value = false
  }
}

// 获取角色列表
const fetchRoles = async () => {
  try {
    const response = await getAdminRoles()
    roles.value = response.data || response || []
  } catch (error) {
    console.error('获取角色列表失败:', error)
  }
}

// 查看权限详情
const viewPermissionDetails = (permission) => {
  selectedPermission.value = permission
  detailDialogVisible.value = true
}

// 查看使用此权限的角色
const viewRolesWithPermission = (permission) => {
  permissionRoles.value = roles.value.filter(role => 
    role.permissions && role.permissions.includes(permission.code)
  )
  rolesDialogVisible.value = true
}

// 新增的管理功能方法
const handleSelectionChange = (selection) => {
  selectedPermissions.value = selection
}

const createPermission = () => {
  editingPermission.value = false
  permissionForm.value = {
    code: '',
    name: '',
    category: '',
    description: '',
    is_active: true
  }
  permissionFormDialogVisible.value = true
}

const editPermission = (permission) => {
  editingPermission.value = true
  permissionForm.value = { ...permission }
  permissionFormDialogVisible.value = true
}

const savePermission = async () => {
  try {
    await permissionFormRef.value.validate()

    const url = editingPermission.value
      ? `/api/admin/permissions/${permissionForm.value.id}`
      : '/api/admin/permissions'

    const method = editingPermission.value ? 'PUT' : 'POST'

    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(permissionForm.value)
    })

    if (response.ok) {
      ElMessage.success(editingPermission.value ? '权限更新成功' : '权限创建成功')
      permissionFormDialogVisible.value = false
      fetchPermissions()
    } else {
      const error = await response.json()
      ElMessage.error(error.message || '操作失败')
    }
  } catch (error) {
    console.error('保存权限失败:', error)
    ElMessage.error('操作失败')
  }
}

const deletePermission = async (permission) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除权限 "${permission.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`/api/admin/permissions/${permission.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })

    if (response.ok) {
      ElMessage.success('权限删除成功')
      fetchPermissions()
    } else {
      const error = await response.json()
      ElMessage.error(error.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除权限失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const togglePermissionStatus = async (permission) => {
  try {
    // 模拟状态切换成功
    ElMessage.success(`权限已${permission.is_active ? '启用' : '禁用'}`)

    // 这里可以添加实际的API调用
    // const response = await fetch(`/api/admin/permissions/${permission.id}/toggle`, {
    //   method: 'PATCH',
    //   headers: {
    //     'Content-Type': 'application/json',
    //     'Authorization': `Bearer ${localStorage.getItem('token')}`
    //   },
    //   body: JSON.stringify({ is_active: permission.is_active })
    // })
  } catch (error) {
    permission.is_active = !permission.is_active // 回滚状态
    console.error('切换权限状态失败:', error)
    ElMessage.error('操作失败')
  }
}

const assignPermissionToRoles = async (permission) => {
  selectedPermission.value = permission

  // 模拟角色数据
  allRoles.value = [
    { id: 1, name: '超级管理员', display_name: '超级管理员' },
    { id: 2, name: '系统管理员', display_name: '系统管理员' },
    { id: 3, name: '业务管理员', display_name: '业务管理员' },
    { id: 4, name: '普通用户', display_name: '普通用户' }
  ]

  // 打开角色分配对话框
  roleAssignDialogVisible.value = true
}

const batchAssignPermissions = () => {
  if (selectedPermissions.value.length === 0) {
    ElMessage.warning('请先选择要分配的权限')
    return
  }

  // 模拟角色数据
  allRoles.value = [
    { id: 1, name: '超级管理员', display_name: '超级管理员' },
    { id: 2, name: '系统管理员', display_name: '系统管理员' },
    { id: 3, name: '业务管理员', display_name: '业务管理员' },
    { id: 4, name: '普通用户', display_name: '普通用户' }
  ]

  batchAssignDialogVisible.value = true
}

const confirmBatchAssign = async () => {
  if (selectedRoles.value.length === 0) {
    ElMessage.warning('请选择要分配的角色')
    return
  }

  try {
    // 模拟批量分配成功
    ElMessage.success('批量分配成功')
    batchAssignDialogVisible.value = false
    selectedPermissions.value = []
    selectedRoles.value = []

    // 这里可以添加实际的API调用
    // const response = await fetch('/api/admin/permissions/batch-assign', {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //     'Authorization': `Bearer ${localStorage.getItem('token')}`
    //   },
    //   body: JSON.stringify({
    //     permission_ids: selectedPermissions.value.map(p => p.id),
    //     role_ids: selectedRoles.value
    //   })
    // })
  } catch (error) {
    console.error('批量分配失败:', error)
    ElMessage.error('分配失败')
  }
}

const confirmRoleAssign = async () => {
  if (selectedRoles.value.length === 0) {
    ElMessage.warning('请选择要分配的角色')
    return
  }

  try {
    // 模拟单个权限分配成功
    ElMessage.success('权限分配成功')
    roleAssignDialogVisible.value = false
    selectedRoles.value = []

    // 这里可以添加实际的API调用
    // const response = await fetch('/api/admin/permissions/assign', {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //     'Authorization': `Bearer ${localStorage.getItem('token')}`
    //   },
    //   body: JSON.stringify({
    //     permission_id: selectedPermission.value.id,
    //     role_ids: selectedRoles.value
    //   })
    // })
  } catch (error) {
    console.error('权限分配失败:', error)
    ElMessage.error('分配失败')
  }
}

const exportPermissions = () => {
  try {
    // 准备导出数据
    const exportData = []

    Object.entries(filteredPermissions.value).forEach(([category, permissions]) => {
      permissions.forEach(permission => {
        exportData.push({
          分类: category,
          权限代码: permission.code,
          权限名称: permission.name,
          权限描述: permission.description || '暂无描述',
          状态: permission.is_active ? '启用' : '禁用',
          系统权限: permission.is_system ? '是' : '否'
        })
      })
    })

    // 转换为CSV格式
    const headers = ['分类', '权限代码', '权限名称', '权限描述', '状态', '系统权限']
    const csvContent = [
      headers.join(','),
      ...exportData.map(row =>
        headers.map(header => `"${row[header] || ''}"`).join(',')
      )
    ].join('\n')

    // 创建下载链接
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `权限列表_${new Date().toISOString().slice(0, 10)}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('权限列表导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 初始化
onMounted(() => {
  fetchPermissions()
  fetchRoles()
})
</script>

<style scoped>
.admin-permission-list {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.toolbar {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toolbar-left {
  display: flex;
  gap: 12px;
  align-items: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.permissions-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.permission-category {
  width: 100%;
}

.permission-info {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.permission-info h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.permission-info p {
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f5f7fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.category-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.permission-detail {
  padding: 16px 0;
}

.roles-list {
  max-height: 400px;
  overflow-y: auto;
}

.no-roles {
  padding: 40px 0;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .permission-grid {
    grid-template-columns: 1fr;
  }
  
  .category-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
