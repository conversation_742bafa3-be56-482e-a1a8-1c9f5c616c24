<template>
  <div class="data-maintenance">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>数据维护</h1>
      <p>数据备份、恢复和清理</p>
    </div>

    <!-- 数据备份 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>数据备份</span>
              <el-button type="primary" :icon="Download" @click="showBackupDialog">
                创建备份
              </el-button>
            </div>
          </template>
          
          <div class="backup-section">
            <el-table :data="backups" v-loading="backupLoading" style="width: 100%">
              <el-table-column prop="backup_name" label="备份名称" />
              <el-table-column prop="backup_type" label="类型" width="80">
                <template #default="scope">
                  <el-tag :type="scope.row.backup_type === 'manual' ? 'primary' : 'success'">
                    {{ scope.row.backup_type === 'manual' ? '手动' : '自动' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="file_size" label="大小" width="100">
                <template #default="scope">
                  {{ formatFileSize(scope.row.file_size) }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.status)">
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="创建时间" width="160">
                <template #default="scope">
                  {{ formatTime(scope.row.created_at) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="scope">
                  <el-button 
                    type="text" 
                    size="small"
                    :disabled="scope.row.status !== 'completed'"
                    @click="downloadBackup(scope.row)"
                  >
                    下载
                  </el-button>
                  <el-button 
                    type="text" 
                    size="small" 
                    style="color: #f56c6c"
                    @click="deleteBackup(scope.row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>数据清理</span>
              <el-button type="warning" :icon="Delete" @click="showCleanupDialog">
                清理数据
              </el-button>
            </div>
          </template>
          
          <div class="cleanup-section">
            <div class="cleanup-options">
              <div class="cleanup-item">
                <h4>操作日志清理</h4>
                <p>清理指定天数前的操作日志</p>
                <el-input-number 
                  v-model="cleanupDays.logs" 
                  :min="1" 
                  :max="365"
                  style="width: 120px"
                />
                <span class="unit">天前</span>
              </div>
              
              <div class="cleanup-item">
                <h4>临时文件清理</h4>
                <p>清理系统临时文件和缓存</p>
                <el-button type="warning" size="small" @click="cleanupTempFiles">
                  立即清理
                </el-button>
              </div>
              
              <div class="cleanup-item">
                <h4>过期会话清理</h4>
                <p>清理过期的用户会话数据</p>
                <el-button type="warning" size="small" @click="cleanupSessions">
                  立即清理
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据统计 -->
    <el-row style="margin-top: 20px">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>数据统计</span>
              <el-button :icon="Refresh" @click="fetchDataStats">
                刷新
              </el-button>
            </div>
          </template>
          
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-label">总用户数</div>
              <div class="stat-value">{{ dataStats.total_users }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">总门店数</div>
              <div class="stat-value">{{ dataStats.total_stores }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">总员工数</div>
              <div class="stat-value">{{ dataStats.total_employees }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">总订单数</div>
              <div class="stat-value">{{ dataStats.total_orders }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">操作日志数</div>
              <div class="stat-value">{{ dataStats.total_logs }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">数据库大小</div>
              <div class="stat-value">{{ formatFileSize(dataStats.database_size) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 创建备份对话框 -->
    <el-dialog
      title="创建数据备份"
      v-model="backupDialogVisible"
      width="500px"
    >
      <el-form :model="backupForm" label-width="100px">
        <el-form-item label="备份名称">
          <el-input v-model="backupForm.backup_name" placeholder="留空自动生成" />
        </el-form-item>
        <el-form-item label="备份类型">
          <el-radio-group v-model="backupForm.backup_type">
            <el-radio label="full">完整备份</el-radio>
            <el-radio label="incremental">增量备份</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="backupDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="createBackup" :loading="creating">
            创建备份
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 数据清理对话框 -->
    <el-dialog
      title="数据清理确认"
      v-model="cleanupDialogVisible"
      width="500px"
    >
      <div class="cleanup-confirm">
        <el-alert
          title="警告"
          type="warning"
          description="数据清理操作不可逆，请确认要清理的数据类型和时间范围。"
          show-icon
          :closable="false"
        />
        
        <div style="margin-top: 20px;">
          <p>将清理 <strong>{{ cleanupDays.logs }}</strong> 天前的操作日志</p>
          <p>预计清理数据量：<strong>{{ estimatedCleanupSize }}</strong></p>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cleanupDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="executeCleanup" :loading="cleaning">
            确认清理
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, Delete, Refresh } from '@element-plus/icons-vue'
import { 
  createDataBackup, 
  getDataBackups, 
  cleanupData,
  getDashboardStats 
} from '@/api/admin'

// 响应式数据
const backupLoading = ref(false)
const creating = ref(false)
const cleaning = ref(false)
const backupDialogVisible = ref(false)
const cleanupDialogVisible = ref(false)
const backups = ref([])
const estimatedCleanupSize = ref('未知')

// 表单数据
const backupForm = reactive({
  backup_name: '',
  backup_type: 'full'
})

const cleanupDays = reactive({
  logs: 30
})

const dataStats = reactive({
  total_users: 0,
  total_stores: 0,
  total_employees: 0,
  total_orders: 0,
  total_logs: 0,
  database_size: 0
})

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化时间 - 使用浏览器本地时间
const formatTime = (time) => {
  if (!time) return '-'

  try {
    // 创建Date对象，让浏览器自动处理时区
    const date = new Date(time)

    // 使用浏览器本地时间格式化，强制使用北京时区
    return date.toLocaleString('zh-CN', {
      timeZone: 'Asia/Shanghai', // 强制使用北京时间
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false // 使用24小时制
    })

  } catch (error) {
    console.error('时间格式化错误:', error, time)
    return time
  }
}

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    running: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    running: '进行中',
    completed: '已完成',
    failed: '失败'
  }
  return texts[status] || status
}

// 获取备份列表
const fetchBackups = async () => {
  backupLoading.value = true
  try {
    const response = await getDataBackups()
    backups.value = response.data || response || []
  } catch (error) {
    console.error('获取备份列表失败:', error)
    ElMessage.error('获取备份列表失败')
  } finally {
    backupLoading.value = false
  }
}

// 获取数据统计
const fetchDataStats = async () => {
  try {
    const response = await getDashboardStats()
    const stats = response.data || response || {}
    Object.assign(dataStats, {
      total_users: stats.total_users || 0,
      total_stores: stats.total_stores || 0,
      total_employees: stats.total_employees || 0,
      total_orders: stats.total_orders || 0,
      total_logs: stats.total_logs || 0,
      database_size: stats.database_size || 0
    })
  } catch (error) {
    console.error('获取数据统计失败:', error)
  }
}

// 显示备份对话框
const showBackupDialog = () => {
  backupForm.backup_name = ''
  backupForm.backup_type = 'full'
  backupDialogVisible.value = true
}

// 创建备份
const createBackup = async () => {
  creating.value = true
  try {
    await createDataBackup(backupForm)
    ElMessage.success('备份创建成功')
    backupDialogVisible.value = false
    fetchBackups()
  } catch (error) {
    console.error('创建备份失败:', error)
    ElMessage.error('创建备份失败')
  } finally {
    creating.value = false
  }
}

// 下载备份
const downloadBackup = async (backup) => {
  try {
    // 创建下载链接
    const downloadUrl = `/api/v1/admin/data/backups/${backup.id}/download`
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = backup.backup_name + '.sql'
    link.target = '_blank'

    // 添加认证头
    const token = localStorage.getItem('admin_token')
    if (token) {
      // 使用fetch下载文件
      const response = await fetch(downloadUrl, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        link.href = url
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        ElMessage.success('备份文件下载成功')
      } else {
        throw new Error('下载失败')
      }
    } else {
      throw new Error('未找到认证信息')
    }
  } catch (error) {
    console.error('下载备份失败:', error)
    ElMessage.error('下载备份失败')
  }
}

// 删除备份
const deleteBackup = async (backup) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除备份 "${backup.backup_name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里应该调用删除备份的API
    ElMessage.success('删除成功')
    fetchBackups()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除备份失败:', error)
      ElMessage.error('删除备份失败')
    }
  }
}

// 显示清理对话框
const showCleanupDialog = () => {
  // 估算清理大小
  estimatedCleanupSize.value = '约 ' + Math.floor(dataStats.total_logs * 0.1) + ' 条记录'
  cleanupDialogVisible.value = true
}

// 执行数据清理
const executeCleanup = async () => {
  cleaning.value = true
  try {
    await cleanupData({ days: cleanupDays.logs })
    ElMessage.success('数据清理完成')
    cleanupDialogVisible.value = false
    fetchDataStats()
  } catch (error) {
    console.error('数据清理失败:', error)
    ElMessage.error('数据清理失败')
  } finally {
    cleaning.value = false
  }
}

// 清理临时文件
const cleanupTempFiles = async () => {
  try {
    const response = await fetch('/api/v1/admin/data/cleanup/temp', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const result = await response.json()
      ElMessage.success(`临时文件清理完成，清理了 ${result.cleaned_files || 0} 个文件`)
    } else {
      throw new Error('清理失败')
    }
  } catch (error) {
    console.error('清理临时文件失败:', error)
    ElMessage.error('清理临时文件失败')
  }
}

// 清理过期会话
const cleanupSessions = async () => {
  try {
    const response = await fetch('/api/v1/admin/data/cleanup/sessions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const result = await response.json()
      ElMessage.success(`过期会话清理完成，清理了 ${result.cleaned_sessions || 0} 个会话`)
    } else {
      throw new Error('清理失败')
    }
  } catch (error) {
    console.error('清理会话失败:', error)
    ElMessage.error('清理会话失败')
  }
}

// 初始化
onMounted(() => {
  fetchBackups()
  fetchDataStats()
})
</script>

<style scoped>
.data-maintenance {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.backup-section {
  min-height: 300px;
}

.cleanup-section {
  min-height: 300px;
}

.cleanup-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.cleanup-item {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #fafafa;
}

.cleanup-item h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #303133;
}

.cleanup-item p {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
}

.unit {
  margin-left: 8px;
  color: #909399;
  font-size: 14px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #fafafa;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.cleanup-confirm {
  padding: 16px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
