<template>
  <div class="system-monitor">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>系统监控</h1>
      <p>实时监控系统运行状态</p>
    </div>

    <!-- 系统状态概览 -->
    <el-row :gutter="20" class="status-overview">
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon cpu">
              <el-icon><Cpu /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-value">{{ systemHealth.cpu_usage }}%</div>
              <div class="status-label">CPU使用率</div>
            </div>
          </div>
          <el-progress 
            :percentage="systemHealth.cpu_usage" 
            :color="getProgressColor(systemHealth.cpu_usage)"
            :show-text="false"
          />
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon memory">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-value">{{ systemHealth.memory_usage }}%</div>
              <div class="status-label">内存使用率</div>
            </div>
          </div>
          <el-progress 
            :percentage="systemHealth.memory_usage" 
            :color="getProgressColor(systemHealth.memory_usage)"
            :show-text="false"
          />
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon disk">
              <el-icon><FolderOpened /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-value">{{ systemHealth.disk_usage }}%</div>
              <div class="status-label">磁盘使用率</div>
            </div>
          </div>
          <el-progress 
            :percentage="systemHealth.disk_usage" 
            :color="getProgressColor(systemHealth.disk_usage)"
            :show-text="false"
          />
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon uptime">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-value">{{ systemHealth.uptime }}</div>
              <div class="status-label">运行时间</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 服务状态 -->
    <el-row :gutter="20" class="service-status">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>服务状态</span>
              <el-button type="text" :icon="Refresh" @click="fetchServiceStatus">
                刷新
              </el-button>
            </div>
          </template>
          
          <div class="service-list">
            <div class="service-item">
              <div class="service-info">
                <h4>数据库服务</h4>
                <p>PostgreSQL</p>
              </div>
              <el-tag :type="getStatusType(systemHealth.database_status)">
                {{ getStatusText(systemHealth.database_status) }}
              </el-tag>
            </div>
            
            <div class="service-item">
              <div class="service-info">
                <h4>缓存服务</h4>
                <p>Redis</p>
              </div>
              <el-tag :type="getStatusType(systemHealth.redis_status)">
                {{ getStatusText(systemHealth.redis_status) }}
              </el-tag>
            </div>
            
            <div class="service-item">
              <div class="service-info">
                <h4>Web服务</h4>
                <p>FastAPI</p>
              </div>
              <el-tag type="success">正常</el-tag>
            </div>
            
            <div class="service-item">
              <div class="service-info">
                <h4>前端服务</h4>
                <p>Vue.js</p>
              </div>
              <el-tag type="success">正常</el-tag>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>实时指标</span>
              <el-switch 
                v-model="autoRefresh" 
                active-text="自动刷新"
                @change="toggleAutoRefresh"
              />
            </div>
          </template>
          
          <div class="metrics-list">
            <div class="metric-item">
              <span class="metric-label">当前在线用户</span>
              <span class="metric-value">{{ metrics.online_users }}</span>
            </div>
            
            <div class="metric-item">
              <span class="metric-label">今日访问量</span>
              <span class="metric-value">{{ metrics.daily_visits }}</span>
            </div>
            
            <div class="metric-item">
              <span class="metric-label">API请求数/分钟</span>
              <span class="metric-value">{{ metrics.api_requests_per_minute }}</span>
            </div>
            
            <div class="metric-item">
              <span class="metric-label">平均响应时间</span>
              <span class="metric-value">{{ metrics.avg_response_time }}ms</span>
            </div>
            
            <div class="metric-item">
              <span class="metric-label">错误率</span>
              <span class="metric-value">{{ metrics.error_rate }}%</span>
            </div>
            
            <div class="metric-item">
              <span class="metric-label">数据库连接数</span>
              <span class="metric-value">{{ metrics.db_connections }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统日志 -->
    <el-row>
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>系统日志</span>
              <div class="header-actions">
                <el-select v-model="logLevel" placeholder="日志级别" style="width: 120px; margin-right: 12px">
                  <el-option label="全部" value="" />
                  <el-option label="错误" value="error" />
                  <el-option label="警告" value="warning" />
                  <el-option label="信息" value="info" />
                </el-select>
                <el-button type="text" :icon="Refresh" @click="fetchSystemLogs">
                  刷新
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="log-container">
            <div 
              v-for="log in systemLogs" 
              :key="log.id"
              :class="['log-item', `log-${log.level}`]"
            >
              <span class="log-time">{{ formatTime(log.timestamp) }}</span>
              <span :class="['log-level', `level-${log.level}`]">{{ log.level.toUpperCase() }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
            
            <div v-if="systemLogs.length === 0" class="no-logs">
              <el-empty description="暂无日志数据" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Cpu,
  Monitor,
  FolderOpened,
  Timer,
  Refresh
} from '@element-plus/icons-vue'
import { getSystemHealth } from '@/api/admin'

// 响应式数据
const autoRefresh = ref(false)
const logLevel = ref('')
const refreshTimer = ref(null)

const systemHealth = reactive({
  cpu_usage: 0,
  memory_usage: 0,
  disk_usage: 0,
  uptime: '',
  database_status: 'normal',
  redis_status: 'normal'
})

const metrics = reactive({
  online_users: 0,
  daily_visits: 0,
  api_requests_per_minute: 0,
  avg_response_time: 0,
  error_rate: 0,
  db_connections: 0
})

const systemLogs = ref([])

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage < 60) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'normal':
      return 'success'
    case 'warning':
      return 'warning'
    case 'error':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'normal':
      return '正常'
    case 'warning':
      return '警告'
    case 'error':
      return '错误'
    default:
      return '未知'
  }
}

// 格式化时间
const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

// 获取系统健康状态
const fetchSystemHealth = async () => {
  try {
    const response = await getSystemHealth()
    const health = response.data || response
    Object.assign(systemHealth, health)
  } catch (error) {
    console.error('获取系统健康状态失败:', error)
  }
}

// 获取服务状态
const fetchServiceStatus = async () => {
  try {
    // 这里应该调用获取服务状态的API
    await fetchSystemHealth()
  } catch (error) {
    console.error('获取服务状态失败:', error)
  }
}

// 获取实时指标
const fetchMetrics = async () => {
  try {
    const response = await fetch('/api/v1/admin/dashboard/metrics', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      }
    })

    if (response.ok) {
      const data = await response.json()
      Object.assign(metrics, data)
    } else {
      // 如果API不存在，使用模拟数据
      Object.assign(metrics, {
        online_users: Math.floor(Math.random() * 100) + 50,
        daily_visits: Math.floor(Math.random() * 1000) + 500,
        api_requests_per_minute: Math.floor(Math.random() * 50) + 20,
        avg_response_time: Math.floor(Math.random() * 100) + 50,
        error_rate: (Math.random() * 2).toFixed(2),
        db_connections: Math.floor(Math.random() * 20) + 10
      })
    }
  } catch (error) {
    console.error('获取实时指标失败:', error)
    // 使用模拟数据作为后备
    Object.assign(metrics, {
      online_users: Math.floor(Math.random() * 100) + 50,
      daily_visits: Math.floor(Math.random() * 1000) + 500,
      api_requests_per_minute: Math.floor(Math.random() * 50) + 20,
      avg_response_time: Math.floor(Math.random() * 100) + 50,
      error_rate: (Math.random() * 2).toFixed(2),
      db_connections: Math.floor(Math.random() * 20) + 10
    })
  }
}

// 获取系统日志
const fetchSystemLogs = async () => {
  try {
    const params = new URLSearchParams()
    if (logLevel.value) {
      params.append('level', logLevel.value)
    }
    params.append('limit', '50') // 限制日志数量

    const response = await fetch(`/api/v1/admin/logs/system?${params}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      }
    })

    if (response.ok) {
      const data = await response.json()
      systemLogs.value = data.data || data || []
    } else {
      // 如果API不存在，使用模拟数据
      const mockLogs = [
        {
          id: 1,
          timestamp: new Date(),
          level: 'info',
          message: '系统启动完成'
        },
        {
          id: 2,
          timestamp: new Date(Date.now() - 60000),
          level: 'warning',
          message: 'CPU使用率较高，当前为85%'
        },
        {
          id: 3,
          timestamp: new Date(Date.now() - 120000),
          level: 'error',
          message: '数据库连接超时'
        },
        {
          id: 4,
          timestamp: new Date(Date.now() - 180000),
          level: 'info',
          message: '用户登录成功'
        }
      ]

      systemLogs.value = logLevel.value
        ? mockLogs.filter(log => log.level === logLevel.value)
        : mockLogs
    }
  } catch (error) {
    console.error('获取系统日志失败:', error)
    // 使用模拟数据作为后备
    const mockLogs = [
      {
        id: 1,
        timestamp: new Date(),
        level: 'info',
        message: '系统启动完成'
      },
      {
        id: 2,
        timestamp: new Date(Date.now() - 60000),
        level: 'warning',
        message: 'CPU使用率较高，当前为85%'
      },
      {
        id: 3,
        timestamp: new Date(Date.now() - 120000),
        level: 'error',
        message: '数据库连接超时'
      },
      {
        id: 4,
        timestamp: new Date(Date.now() - 180000),
        level: 'info',
        message: '用户登录成功'
      }
    ]

    systemLogs.value = logLevel.value
      ? mockLogs.filter(log => log.level === logLevel.value)
      : mockLogs
  }
}

// 切换自动刷新
const toggleAutoRefresh = (enabled) => {
  if (enabled) {
    refreshTimer.value = setInterval(() => {
      fetchSystemHealth()
      fetchMetrics()
    }, 5000) // 每5秒刷新一次
    ElMessage.success('已开启自动刷新')
  } else {
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value)
      refreshTimer.value = null
    }
    ElMessage.info('已关闭自动刷新')
  }
}

// 初始化
onMounted(() => {
  fetchSystemHealth()
  fetchMetrics()
  fetchSystemLogs()
})

// 清理定时器
onUnmounted(() => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
  }
})
</script>

<style scoped>
.system-monitor {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.status-overview {
  margin-bottom: 24px;
}

.status-card {
  height: 120px;
}

.status-content {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.status-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: #fff;
}

.status-icon.cpu {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.status-icon.memory {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.status-icon.disk {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.status-icon.uptime {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.status-info {
  flex: 1;
}

.status-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.status-label {
  font-size: 14px;
  color: #909399;
}

.service-status {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

.service-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #fafafa;
}

.service-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #303133;
}

.service-info p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.metrics-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.metric-label {
  color: #606266;
  font-size: 14px;
}

.metric-value {
  color: #303133;
  font-weight: 600;
  font-size: 16px;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.log-time {
  color: #909399;
  margin-right: 12px;
  min-width: 160px;
}

.log-level {
  margin-right: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  min-width: 50px;
  text-align: center;
}

.level-error {
  background: #fef0f0;
  color: #f56c6c;
}

.level-warning {
  background: #fdf6ec;
  color: #e6a23c;
}

.level-info {
  background: #f0f9ff;
  color: #409eff;
}

.log-message {
  color: #303133;
  flex: 1;
}

.no-logs {
  padding: 40px 0;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-overview {
    margin-bottom: 16px;
  }
  
  .service-status {
    margin-bottom: 16px;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
