<template>
  <div class="site-maintenance">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>网站维护</h1>
      <p>网站维护模式和公告管理</p>
    </div>

    <!-- 维护模式控制 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>维护模式</span>
              <el-switch
                v-model="maintenanceMode"
                @change="toggleMaintenanceMode"
                active-text="开启"
                inactive-text="关闭"
                :loading="switching"
              />
            </div>
          </template>
          
          <div class="maintenance-section">
            <el-alert
              :title="maintenanceMode ? '网站当前处于维护模式' : '网站正常运行中'"
              :type="maintenanceMode ? 'warning' : 'success'"
              :description="maintenanceMode ? '用户无法访问前台页面，只显示维护公告' : '用户可以正常访问所有功能'"
              show-icon
              :closable="false"
            />
            
            <div class="maintenance-form" v-if="maintenanceMode">
              <el-form :model="maintenanceForm" label-width="120px" style="margin-top: 20px;">
                <el-form-item label="维护标题">
                  <el-input v-model="maintenanceForm.title" placeholder="请输入维护标题" />
                </el-form-item>
                
                <el-form-item label="维护说明">
                  <el-input
                    v-model="maintenanceForm.message"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入维护说明"
                  />
                </el-form-item>
                
                <el-form-item label="预计恢复时间">
                  <el-date-picker
                    v-model="maintenanceForm.estimatedTime"
                    type="datetime"
                    placeholder="选择预计恢复时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                  />
                </el-form-item>
                
                <el-form-item label="联系方式">
                  <el-input v-model="maintenanceForm.contact" placeholder="请输入联系方式" />
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="saveMaintenanceInfo" :loading="saving">
                    保存维护信息
                  </el-button>
                  <el-button @click="previewMaintenance">
                    预览维护页面
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>系统公告</span>
              <el-button type="primary" size="small" @click="showAnnouncementDialog">
                新增公告
              </el-button>
            </div>
          </template>
          
          <div class="announcement-section">
            <el-table :data="announcements" style="width: 100%" v-loading="announcementLoading">
              <el-table-column prop="title" label="标题" />
              <el-table-column prop="type" label="类型" width="80">
                <template #default="scope">
                  <el-tag :type="getAnnouncementTypeColor(scope.row.type)">
                    {{ getAnnouncementTypeText(scope.row.type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template #default="scope">
                  <el-tag :type="scope.row.status === 'active' ? 'success' : 'info'">
                    {{ scope.row.status === 'active' ? '启用' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="创建时间" width="160" />
              <el-table-column label="操作" width="120">
                <template #default="scope">
                  <el-button size="small" @click="editAnnouncement(scope.row)">编辑</el-button>
                  <el-button size="small" type="danger" @click="deleteAnnouncement(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 维护历史 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>维护历史</span>
          <el-button size="small" @click="fetchMaintenanceHistory">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      
      <el-table :data="maintenanceHistory" style="width: 100%" v-loading="historyLoading">
        <el-table-column prop="start_time" label="开始时间" width="160" />
        <el-table-column prop="end_time" label="结束时间" width="160" />
        <el-table-column prop="duration" label="持续时间" width="120" />
        <el-table-column prop="title" label="维护标题" />
        <el-table-column prop="operator" label="操作人" width="100" />
        <el-table-column prop="reason" label="维护原因" />
      </el-table>
    </el-card>

    <!-- 公告编辑对话框 -->
    <el-dialog
      :title="announcementDialogTitle"
      v-model="announcementDialogVisible"
      width="600px"
    >
      <el-form :model="announcementForm" label-width="100px">
        <el-form-item label="公告标题" required>
          <el-input v-model="announcementForm.title" placeholder="请输入公告标题" />
        </el-form-item>
        
        <el-form-item label="公告类型" required>
          <el-select v-model="announcementForm.type" placeholder="请选择公告类型">
            <el-option label="系统通知" value="system" />
            <el-option label="维护通知" value="maintenance" />
            <el-option label="功能更新" value="update" />
            <el-option label="重要公告" value="important" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="公告内容" required>
          <el-input
            v-model="announcementForm.content"
            type="textarea"
            :rows="6"
            placeholder="请输入公告内容"
          />
        </el-form-item>
        
        <el-form-item label="生效时间">
          <el-date-picker
            v-model="announcementForm.start_time"
            type="datetime"
            placeholder="选择生效时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        
        <el-form-item label="失效时间">
          <el-date-picker
            v-model="announcementForm.end_time"
            type="datetime"
            placeholder="选择失效时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-switch
            v-model="announcementForm.status"
            active-value="active"
            inactive-value="inactive"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="announcementDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveAnnouncement" :loading="saving">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'

// 响应式数据
const maintenanceMode = ref(false)
const switching = ref(false)
const saving = ref(false)
const announcementLoading = ref(false)
const historyLoading = ref(false)
const announcementDialogVisible = ref(false)
const editingAnnouncement = ref(null)

// 表单数据
const maintenanceForm = reactive({
  title: '系统维护中',
  message: '系统正在进行维护升级，预计将在指定时间恢复正常服务。给您带来的不便，敬请谅解。',
  estimatedTime: '',
  contact: '技术支持：400-123-4567'
})

const announcementForm = reactive({
  title: '',
  type: 'system',
  content: '',
  start_time: '',
  end_time: '',
  status: 'active'
})

// 模拟数据
const announcements = ref([
  {
    id: 1,
    title: '系统维护通知',
    type: 'maintenance',
    status: 'active',
    created_at: '2025-07-22 14:30:00'
  },
  {
    id: 2,
    title: '新功能上线',
    type: 'update',
    status: 'active',
    created_at: '2025-07-20 10:00:00'
  }
])

const maintenanceHistory = ref([
  {
    start_time: '2025-07-20 02:00:00',
    end_time: '2025-07-20 04:30:00',
    duration: '2小时30分钟',
    title: '数据库升级维护',
    operator: '系统管理员',
    reason: '数据库版本升级'
  }
])

// 计算属性
const announcementDialogTitle = computed(() => {
  return editingAnnouncement.value ? '编辑公告' : '新增公告'
})

// 方法
const toggleMaintenanceMode = async (value) => {
  switching.value = true
  try {
    // 这里应该调用API切换维护模式
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
    ElMessage.success(value ? '维护模式已开启' : '维护模式已关闭')
  } catch (error) {
    console.error('切换维护模式失败:', error)
    ElMessage.error('操作失败')
    maintenanceMode.value = !value // 回滚状态
  } finally {
    switching.value = false
  }
}

const saveMaintenanceInfo = async () => {
  saving.value = true
  try {
    // 这里应该调用API保存维护信息
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
    ElMessage.success('维护信息保存成功')
  } catch (error) {
    console.error('保存维护信息失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const previewMaintenance = () => {
  // 这里应该打开维护页面预览
  ElMessage.info('维护页面预览功能开发中')
}

const showAnnouncementDialog = () => {
  editingAnnouncement.value = null
  Object.assign(announcementForm, {
    title: '',
    type: 'system',
    content: '',
    start_time: '',
    end_time: '',
    status: 'active'
  })
  announcementDialogVisible.value = true
}

const editAnnouncement = (announcement) => {
  editingAnnouncement.value = announcement
  Object.assign(announcementForm, announcement)
  announcementDialogVisible.value = true
}

const saveAnnouncement = async () => {
  saving.value = true
  try {
    // 这里应该调用API保存公告
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
    ElMessage.success('公告保存成功')
    announcementDialogVisible.value = false
    // 刷新公告列表
  } catch (error) {
    console.error('保存公告失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const deleteAnnouncement = async (announcement) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除公告 "${announcement.title}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里应该调用API删除公告
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除公告失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const getAnnouncementTypeColor = (type) => {
  const colors = {
    system: '',
    maintenance: 'warning',
    update: 'success',
    important: 'danger'
  }
  return colors[type] || ''
}

const getAnnouncementTypeText = (type) => {
  const texts = {
    system: '系统',
    maintenance: '维护',
    update: '更新',
    important: '重要'
  }
  return texts[type] || type
}

const fetchMaintenanceHistory = async () => {
  historyLoading.value = true
  try {
    // 这里应该调用API获取维护历史
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
  } catch (error) {
    console.error('获取维护历史失败:', error)
    ElMessage.error('获取维护历史失败')
  } finally {
    historyLoading.value = false
  }
}

// 初始化
onMounted(() => {
  // 获取当前维护模式状态
  // 这里应该调用API获取维护模式状态
})
</script>

<style scoped>
.site-maintenance {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.maintenance-section {
  min-height: 200px;
}

.maintenance-form {
  margin-top: 20px;
}

.announcement-section {
  min-height: 300px;
}
</style>
