<template>
  <div class="customer-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>客户管理</h1>
      <p>管理所有客户信息，包括基本信息、消费记录和会员状态</p>
    </div>

    <!-- 搜索和操作栏 -->
    <div class="toolbar">
      <div class="search-section">
        <el-input
          v-model="searchForm.keyword"
          placeholder="搜索客户姓名、手机号"
          style="width: 300px"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="searchForm.memberLevel"
          placeholder="会员等级"
          style="width: 150px; margin-left: 10px"
          clearable
        >
          <el-option label="普通客户" value="normal" />
          <el-option label="银卡会员" value="silver" />
          <el-option label="金卡会员" value="gold" />
          <el-option label="钻石会员" value="diamond" />
        </el-select>
        
        <el-select
          v-model="searchForm.status"
          placeholder="客户状态"
          style="width: 120px; margin-left: 10px"
          clearable
        >
          <el-option label="正常" value="active" />
          <el-option label="禁用" value="inactive" />
        </el-select>
        
        <el-button type="primary" @click="handleSearch" style="margin-left: 10px">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        
        <el-button @click="resetSearch">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </div>
      
      <div class="action-section">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          新增客户
        </el-button>
        
        <el-button @click="exportCustomers">
          <el-icon><Download /></el-icon>
          导出
        </el-button>

        <el-button type="warning" @click="debugData">
          调试数据
        </el-button>
      </div>
    </div>

    <!-- 客户列表表格 -->
    <el-table
      :data="customerList"
      v-loading="loading"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      
      <el-table-column prop="avatar" label="头像" width="80" align="center">
        <template #default="scope">
          <el-avatar :src="scope.row.avatar" :size="40">
            {{ scope.row.name?.charAt(0) }}
          </el-avatar>
        </template>
      </el-table-column>
      
      <el-table-column prop="name" label="姓名" width="120" />
      
      <el-table-column prop="phone" label="手机号" width="130" />
      
      <el-table-column prop="gender" label="性别" width="80" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.gender === 'male' ? 'primary' : 'danger'" size="small">
            {{ scope.row.gender === 'male' ? '男' : '女' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="member_level" label="会员等级" width="120" align="center">
        <template #default="scope">
          <el-tag :type="getMemberLevelType(scope.row.member_level)" size="small">
            {{ getMemberLevelText(scope.row.member_level) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="total_amount" label="累计消费" width="120" align="right">
        <template #default="scope">
          <span class="amount">¥{{ scope.row.total_amount?.toFixed ? scope.row.total_amount.toFixed(2) : '0.00' }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="last_visit_time" label="最后到店" width="120">
        <template #default="scope">
          {{ formatDate(scope.row.last_visit_time) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="80" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'" size="small">
            {{ scope.row.status === 'active' ? '正常' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="createdAt" label="注册时间" width="120">
        <template #default="scope">
          {{ formatDate(scope.row.createdAt) }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button type="primary" size="small" @click="viewCustomer(scope.row)">
            查看
          </el-button>
          <el-button type="warning" size="small" @click="editCustomer(scope.row)">
            编辑
          </el-button>
          <el-button 
            type="danger" 
            size="small" 
            @click="deleteCustomer(scope.row)"
            :disabled="scope.row.status === 'inactive'"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 客户编辑对话框 -->
    <el-dialog
      :title="isEditMode ? '编辑客户' : '新增客户'"
      v-model="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="customerForm" :rules="customerRules" ref="customerFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户姓名" prop="name">
              <el-input v-model="customerForm.name" placeholder="请输入客户姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="customerForm.phone" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-radio-group v-model="customerForm.gender">
                <el-radio label="male">男</el-radio>
                <el-radio label="female">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生日" prop="birthday">
              <el-date-picker
                v-model="customerForm.birthday"
                type="date"
                placeholder="选择生日"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="会员等级" prop="memberLevel">
          <el-select v-model="customerForm.memberLevel" placeholder="请选择会员等级" style="width: 100%">
            <el-option label="普通客户" value="normal" />
            <el-option label="银卡会员" value="silver" />
            <el-option label="金卡会员" value="gold" />
            <el-option label="钻石会员" value="diamond" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="地址" prop="address">
          <el-input v-model="customerForm.address" placeholder="请输入地址" />
        </el-form-item>
        
        <el-form-item label="备注" prop="notes">
          <el-input 
            v-model="customerForm.notes" 
            type="textarea" 
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveCustomer" :loading="saving">
            {{ isEditMode ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导出对话框 -->
    <el-dialog
      v-model="showExportDialog"
      title="导出客户数据"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form :model="exportForm" label-width="80px">
        <el-form-item label="导出格式">
          <el-radio-group v-model="exportForm.format">
            <el-radio label="excel">Excel (.csv)</el-radio>
            <el-radio label="json">JSON</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="导出字段">
          <el-checkbox-group v-model="exportForm.fields">
            <el-checkbox label="name">姓名</el-checkbox>
            <el-checkbox label="phone">手机号</el-checkbox>
            <el-checkbox label="gender">性别</el-checkbox>
            <el-checkbox label="memberLevel">会员等级</el-checkbox>
            <el-checkbox label="totalConsumption">累计消费</el-checkbox>
            <el-checkbox label="lastVisit">最后到店</el-checkbox>
            <el-checkbox label="status">状态</el-checkbox>
            <el-checkbox label="createdAt">注册时间</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showExportDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmExport" :loading="exportLoading">
            导出
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Refresh, Plus, Download, User, Edit, Delete
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { getCustomerList, createCustomer, updateCustomer, deleteCustomer as deleteCustomerApi } from '@/api/customer'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const dialogVisible = ref(false)
const isEditMode = ref(false)
const customerList = ref([])
const selectedCustomers = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  memberLevel: '',
  status: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 客户表单
const customerForm = reactive({
  id: null,
  name: '',
  phone: '',
  gender: 'male',
  birthday: null,
  memberLevel: 'normal',
  address: '',
  notes: ''
})

const customerFormRef = ref(null)

// 表单验证规则
const customerRules = {
  name: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ]
}

// 计算属性
const getMemberLevelType = (level) => {
  const types = {
    normal: 'info',
    silver: '',
    gold: 'warning',
    diamond: 'danger'
  }
  return types[level] || 'info'
}

const getMemberLevelText = (level) => {
  const texts = {
    normal: '普通客户',
    silver: '银卡会员',
    gold: '金卡会员',
    diamond: '钻石会员'
  }
  return texts[level] || '普通客户'
}

// 方法
const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString()
}

const loadCustomers = async () => {
  loading.value = true
  try {
    const params = {
      skip: (pagination.page - 1) * pagination.size,
      limit: pagination.size
    }

    // 添加搜索条件
    if (searchForm.keyword) {
      params.keyword = searchForm.keyword
    }
    if (searchForm.memberLevel) {
      params.member_level = searchForm.memberLevel
    }
    if (searchForm.status) {
      params.status = searchForm.status
    }

    console.log('调用客户API，参数:', params)
    const response = await getCustomerList(params)
    console.log('客户API响应:', response)

    // 处理响应数据
    console.log('处理响应数据，response类型:', typeof response)
    console.log('response.items存在:', !!response?.items)
    console.log('response.items长度:', response?.items?.length)

    if (response && response.items) {
      customerList.value = response.items
      pagination.total = response.total || 0
      console.log('设置customerList.value:', customerList.value.length, '条记录')
      console.log('设置pagination.total:', pagination.total)
    } else if (Array.isArray(response)) {
      customerList.value = response
      pagination.total = response.length
      console.log('直接设置数组，长度:', response.length)
    } else {
      customerList.value = []
      pagination.total = 0
      console.log('设置为空数组')
    }

  } catch (error) {
    console.error('加载客户列表失败:', error)
    ElMessage.error('加载客户列表失败: ' + (error.message || '未知错误'))
    customerList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadCustomers()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    memberLevel: '',
    status: ''
  })
  handleSearch()
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadCustomers()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadCustomers()
}

const handleSelectionChange = (selection) => {
  selectedCustomers.value = selection
}

const showCreateDialog = () => {
  isEditMode.value = false
  resetCustomerForm()
  dialogVisible.value = true
}

const viewCustomer = (customer) => {
  router.push(`/customers/${customer.id}`)
}

const editCustomer = (customer) => {
  isEditMode.value = true
  Object.assign(customerForm, {
    id: customer.id,
    name: customer.name,
    phone: customer.phone,
    gender: customer.gender,
    birthday: customer.birthday ? new Date(customer.birthday) : null,
    memberLevel: customer.memberLevel,
    address: customer.address || '',
    notes: customer.notes || ''
  })
  dialogVisible.value = true
}

const deleteCustomer = async (customer) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除客户 "${customer.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 调用API删除客户
    console.log('删除客户:', customer)
    await deleteCustomerApi(customer.id)
    ElMessage.success('客户删除成功')
    loadCustomers()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除客户失败:', error)
      ElMessage.error('删除客户失败')
    }
  }
}

const saveCustomer = async () => {
  if (!customerFormRef.value) return

  try {
    await customerFormRef.value.validate()
    saving.value = true

    // 准备提交数据
    const submitData = {
      name: customerForm.name,
      phone: customerForm.phone,
      gender: customerForm.gender,
      birthday: customerForm.birthday,
      address: customerForm.address,
      remark: customerForm.notes
    }

    // 处理会员等级 - 如果是字符串则不传递，让后端使用默认值
    if (customerForm.memberLevel && customerForm.memberLevel !== 'normal') {
      // 这里可以根据需要映射会员等级字符串到ID
      // 暂时不传递，使用后端默认值
    }

    console.log('保存客户:', submitData)

    if (isEditMode.value) {
      // 更新客户
      await updateCustomer(customerForm.id, submitData)
      ElMessage.success('客户更新成功')
    } else {
      // 创建客户
      await createCustomer(submitData)
      ElMessage.success('客户创建成功')
    }

    dialogVisible.value = false
    loadCustomers()

  } catch (error) {
    console.error('保存客户失败:', error)
    ElMessage.error('保存客户失败: ' + (error.response?.data?.detail || error.message || '未知错误'))
  } finally {
    saving.value = false
  }
}

const resetCustomerForm = () => {
  Object.assign(customerForm, {
    id: null,
    name: '',
    phone: '',
    gender: 'male',
    birthday: null,
    memberLevel: 'normal',
    address: '',
    notes: ''
  })
}

const exportCustomers = () => {
  showExportDialog.value = true
}

const debugData = () => {
  console.log('=== 调试数据 ===')
  console.log('customerList.value:', customerList.value)
  console.log('customerList.value.length:', customerList.value.length)
  console.log('pagination:', pagination)
  console.log('loading:', loading.value)
  alert(`customerList长度: ${customerList.value.length}, pagination.total: ${pagination.total}`)
}

// 导出相关
const showExportDialog = ref(false)
const exportForm = reactive({
  format: 'excel',
  fields: ['name', 'phone', 'gender', 'memberLevel', 'totalConsumption', 'lastVisit', 'status', 'createdAt']
})
const exportLoading = ref(false)

// 确认导出
const confirmExport = async () => {
  try {
    exportLoading.value = true

    // 准备导出数据
    const exportData = customerList.value.map(customer => ({
      姓名: customer.name,
      手机号: customer.phone,
      性别: customer.gender === 'male' ? '男' : '女',
      会员等级: getMemberLevelText(customer.memberLevel),
      累计消费: `¥${customer.totalConsumption.toFixed(2)}`,
      最后到店: customer.lastVisit,
      状态: customer.status === 'active' ? '正常' : '禁用',
      注册时间: customer.createdAt
    }))

    if (exportForm.format === 'excel') {
      // 导出Excel
      const headers = [
        { label: '姓名', key: '姓名' },
        { label: '手机号', key: '手机号' },
        { label: '性别', key: '性别' },
        { label: '会员等级', key: '会员等级' },
        { label: '累计消费', key: '累计消费' },
        { label: '最后到店', key: '最后到店' },
        { label: '状态', key: '状态' },
        { label: '注册时间', key: '注册时间' }
      ]

      // 这里应该使用导出工具类
      // await exportService.exportToExcel(exportData, '客户列表', headers)

      // 简单的CSV导出实现
      const csvContent = [
        headers.map(h => h.label).join(','),
        ...exportData.map(row => headers.map(h => row[h.key]).join(','))
      ].join('\n')

      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `客户列表_${new Date().toISOString().slice(0, 10)}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      ElMessage.success('客户数据导出成功')
    } else {
      // 导出JSON
      const jsonData = JSON.stringify(exportData, null, 2)
      const blob = new Blob([jsonData], { type: 'application/json' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `客户列表_${new Date().toISOString().slice(0, 10)}.json`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      ElMessage.success('客户数据导出成功')
    }

    showExportDialog.value = false

  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 初始化
onMounted(() => {
  loadCustomers()
})
</script>

<style lang="scss" scoped>
.customer-list {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
  
  h1 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: #303133;
  }
  
  p {
    margin: 0;
    color: #909399;
    font-size: 14px;
  }
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  .search-section {
    display: flex;
    align-items: center;
  }
  
  .action-section {
    display: flex;
    gap: 12px;
  }
}

.amount {
  font-weight: 600;
  color: #E6A23C;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 16px;
    
    .search-section {
      flex-wrap: wrap;
      gap: 10px;
    }
  }
}
</style>
