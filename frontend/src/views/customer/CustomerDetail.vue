<template>
  <div class="customer-detail">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" icon="ArrowLeft">返回</el-button>
        <h1>客户详情</h1>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="editCustomer">
          <el-icon><Edit /></el-icon>
          编辑客户
        </el-button>
        <el-dropdown @command="handleCommand">
          <el-button>
            <el-icon><More /></el-icon>
            更多操作
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="createOrder">创建订单</el-dropdown-item>
              <el-dropdown-item command="createAppointment">预约服务</el-dropdown-item>
              <el-dropdown-item command="sendMessage">发送消息</el-dropdown-item>
              <el-dropdown-item command="exportData">导出数据</el-dropdown-item>
              <el-dropdown-item command="delete" divided>
                <span style="color: #F56C6C;">删除客户</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton style="width: 100%" animated>
        <template #template>
          <div style="padding: 20px;">
            <el-skeleton-item variant="image" style="width: 100px; height: 100px; border-radius: 50%;" />
            <div style="display: flex; flex-direction: column; margin-left: 20px;">
              <el-skeleton-item variant="h1" style="width: 50%;" />
              <el-skeleton-item variant="text" style="margin-top: 16px; width: 30%;" />
              <el-skeleton-item variant="text" style="margin-top: 16px; width: 40%;" />
            </div>
          </div>
        </template>
      </el-skeleton>
    </div>

    <!-- 客户详情内容 -->
    <div v-else class="customer-content">
      <!-- 基本信息卡片 -->
      <el-card class="info-card">
        <div class="customer-basic-info">
          <div class="avatar-container">
            <el-avatar :size="100" :src="customer.avatar">
              {{ customer.name?.charAt(0) }}
            </el-avatar>
            <div class="member-badge" v-if="customer.memberLevel !== 'normal'">
              <el-tag :type="getMemberLevelType(customer.memberLevel)" effect="dark">
                {{ getMemberLevelText(customer.memberLevel) }}
              </el-tag>
            </div>
          </div>
          <div class="info-container">
            <h2 class="customer-name">{{ customer.name }}</h2>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">手机号码</span>
                <span class="value">{{ customer.phone }}</span>
              </div>
              <div class="info-item">
                <span class="label">性别</span>
                <span class="value">{{ customer.gender === 'male' ? '男' : '女' }}</span>
              </div>
              <div class="info-item">
                <span class="label">生日</span>
                <span class="value">{{ formatDate(customer.birthday) || '未设置' }}</span>
              </div>
              <div class="info-item">
                <span class="label">注册时间</span>
                <span class="value">{{ formatDate(customer.createdAt) }}</span>
              </div>
              <div class="info-item">
                <span class="label">最后到店</span>
                <span class="value">{{ formatDate(customer.lastVisit) || '从未到店' }}</span>
              </div>
              <div class="info-item">
                <span class="label">客户状态</span>
                <span class="value">
                  <el-tag :type="customer.status === 'active' ? 'success' : 'danger'" size="small">
                    {{ customer.status === 'active' ? '正常' : '禁用' }}
                  </el-tag>
                </span>
              </div>
            </div>
            <div class="address-item">
              <span class="label">地址</span>
              <span class="value">{{ customer.address || '未设置' }}</span>
            </div>
            <div class="notes-item" v-if="customer.notes">
              <span class="label">备注</span>
              <span class="value">{{ customer.notes }}</span>
            </div>
          </div>
          <div class="stats-container">
            <div class="stat-item">
              <span class="stat-value">{{ formatCurrency(customer.totalConsumption) }}</span>
              <span class="stat-label">累计消费</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ customer.orderCount || 0 }}</span>
              <span class="stat-label">订单数</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ customer.points || 0 }}</span>
              <span class="stat-label">积分</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 标签卡片 -->
      <el-card class="tags-card">
        <template #header>
          <div class="card-header">
            <span>客户标签</span>
            <el-button type="primary" text @click="manageCustomerTags">
              <el-icon><Plus /></el-icon>
              管理标签
            </el-button>
          </div>
        </template>
        <div class="tags-content">
          <el-tag
            v-for="tag in customer.tags"
            :key="tag.id"
            :type="tag.type"
            class="customer-tag"
            effect="plain"
          >
            {{ tag.name }}
          </el-tag>
          <div v-if="!customer.tags || customer.tags.length === 0" class="empty-data">
            <el-empty description="暂无标签" />
          </div>
        </div>
      </el-card>

      <!-- 选项卡 -->
      <el-tabs v-model="activeTab" class="detail-tabs">
        <el-tab-pane label="消费记录" name="orders">
          <div class="tab-header">
            <el-button type="primary" @click="createOrderForCustomer">
              <el-icon><Plus /></el-icon>
              新增订单
            </el-button>
          </div>
          <el-table
            :data="orders"
            style="width: 100%"
            v-loading="ordersLoading"
          >
            <el-table-column prop="orderNo" label="订单号" width="180" />
            <el-table-column prop="createTime" label="下单时间" width="180">
              <template #default="scope">
                {{ formatDateTime(scope.row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="items" label="服务项目">
              <template #default="scope">
                <el-tag v-for="item in scope.row.items" :key="item.id" class="item-tag">
                  {{ item.name }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="金额" width="120" align="right">
              <template #default="scope">
                <span class="amount">¥{{ scope.row.amount.toFixed(2) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getOrderStatusType(scope.row.status)">
                  {{ getOrderStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="scope">
                <el-button type="primary" link @click="viewOrder(scope.row)">
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div v-if="!orders.length && !ordersLoading" class="empty-data">
            <el-empty description="暂无消费记录" />
          </div>
          <div class="pagination-wrapper" v-if="orders.length">
            <el-pagination
              v-model:current-page="ordersPagination.page"
              v-model:page-size="ordersPagination.size"
              :page-sizes="[5, 10, 20, 50]"
              :total="ordersPagination.total"
              layout="total, sizes, prev, pager, next"
              @size-change="handleOrdersSizeChange"
              @current-change="handleOrdersCurrentChange"
            />
          </div>
        </el-tab-pane>

        <el-tab-pane label="会员信息" name="membership">
          <div class="membership-content" v-loading="membershipLoading">
            <template v-if="membership">
              <div class="membership-card">
                <div class="card-header">
                  <div class="level-info">
                    <el-tag :type="getMemberLevelType(membership.level)" effect="dark" size="large">
                      {{ getMemberLevelText(membership.level) }}
                    </el-tag>
                    <span class="card-no">卡号: {{ membership.cardNo }}</span>
                  </div>
                  <div class="card-actions">
                    <el-button type="primary" @click="upgradeMembership">升级会员</el-button>
                    <el-button @click="renewMembership">续费</el-button>
                  </div>
                </div>
                <div class="card-body">
                  <div class="membership-info">
                    <div class="info-row">
                      <span class="label">开卡时间</span>
                      <span class="value">{{ formatDate(membership.startDate) }}</span>
                    </div>
                    <div class="info-row">
                      <span class="label">到期时间</span>
                      <span class="value">{{ formatDate(membership.endDate) }}</span>
                    </div>
                    <div class="info-row">
                      <span class="label">当前积分</span>
                      <span class="value">{{ membership.points }}</span>
                    </div>
                    <div class="info-row">
                      <span class="label">会员状态</span>
                      <span class="value">
                        <el-tag :type="membership.status === 'active' ? 'success' : 'danger'">
                          {{ membership.status === 'active' ? '有效' : '已过期' }}
                        </el-tag>
                      </span>
                    </div>
                  </div>
                  <div class="membership-benefits">
                    <h4>会员权益</h4>
                    <ul class="benefits-list">
                      <li v-for="(benefit, index) in membership.benefits" :key="index">
                        <el-icon><Check /></el-icon>
                        <span>{{ benefit }}</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              <div class="points-history">
                <div class="section-header">
                  <h3>积分记录</h3>
                </div>
                <el-table :data="pointsHistory" style="width: 100%">
                  <el-table-column prop="date" label="日期" width="180">
                    <template #default="scope">
                      {{ formatDateTime(scope.row.date) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="type" label="类型" width="120">
                    <template #default="scope">
                      <el-tag :type="scope.row.type === 'earn' ? 'success' : 'danger'">
                        {{ scope.row.type === 'earn' ? '获得' : '使用' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="points" label="积分" width="100" />
                  <el-table-column prop="description" label="说明" />
                  <el-table-column prop="balance" label="余额" width="100" />
                </el-table>
                <div v-if="!pointsHistory.length" class="empty-data">
                  <el-empty description="暂无积分记录" />
                </div>
              </div>
            </template>
            <div v-else class="no-membership">
              <el-empty description="该客户尚未成为会员">
                <template #extra>
                  <el-button type="primary" @click="createMembership">创建会员</el-button>
                </template>
              </el-empty>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="预约记录" name="appointments">
          <div v-loading="appointmentsLoading">
            <el-table :data="appointments" style="width: 100%">
              <el-table-column prop="appointmentNo" label="预约号" width="180" />
              <el-table-column prop="appointmentTime" label="预约时间" width="180">
                <template #default="scope">
                  {{ formatDateTime(scope.row.appointmentTime) }}
                </template>
              </el-table-column>
              <el-table-column prop="service" label="服务项目" />
              <el-table-column prop="technician" label="技师" width="120" />
              <el-table-column prop="status" label="状态" width="100" align="center">
                <template #default="scope">
                  <el-tag :type="getAppointmentStatusType(scope.row.status)">
                    {{ getAppointmentStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" fixed="right">
                <template #default="scope">
                  <el-button type="primary" link @click="viewAppointment(scope.row)">
                    查看
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div v-if="!appointments.length && !appointmentsLoading" class="empty-data">
              <el-empty description="暂无预约记录" />
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 客户编辑对话框 -->
    <el-dialog
      title="编辑客户"
      v-model="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="customerForm" :rules="customerRules" ref="customerFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户姓名" prop="name">
              <el-input v-model="customerForm.name" placeholder="请输入客户姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="customerForm.phone" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-radio-group v-model="customerForm.gender">
                <el-radio label="male">男</el-radio>
                <el-radio label="female">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生日" prop="birthday">
              <el-date-picker
                v-model="customerForm.birthday"
                type="date"
                placeholder="选择生日"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="会员等级" prop="memberLevel">
          <el-select v-model="customerForm.memberLevel" placeholder="请选择会员等级" style="width: 100%">
            <el-option label="普通客户" value="normal" />
            <el-option label="银卡会员" value="silver" />
            <el-option label="金卡会员" value="gold" />
            <el-option label="钻石会员" value="diamond" />
          </el-select>
        </el-form-item>

        <el-form-item label="地址" prop="address">
          <el-input v-model="customerForm.address" placeholder="请输入地址" />
        </el-form-item>

        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="customerForm.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveCustomer" :loading="saving">
            更新
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 标签管理对话框 -->
    <el-dialog
      v-model="showTagManageDialog"
      title="管理客户标签"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-loading="tagLoading">
        <div class="tag-categories">
          <h4>选择标签</h4>
          <div class="available-tags">
            <el-checkbox-group v-model="selectedTags">
              <div v-for="tag in availableTags" :key="tag.id" class="tag-item">
                <el-checkbox :label="tag.id">
                  <el-tag :color="tag.color" effect="dark" style="margin-left: 8px;">
                    {{ tag.name }}
                  </el-tag>
                  <span class="tag-category">{{ tag.category }}</span>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>
        </div>

        <div class="selected-tags-preview" v-if="selectedTags.length > 0">
          <h4>已选择的标签</h4>
          <div class="tags-preview">
            <el-tag
              v-for="tagId in selectedTags"
              :key="tagId"
              :color="availableTags.find(t => t.id === tagId)?.color"
              effect="dark"
              style="margin: 4px;"
            >
              {{ availableTags.find(t => t.id === tagId)?.name }}
            </el-tag>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showTagManageDialog = false">取消</el-button>
          <el-button type="primary" @click="saveCustomerTags" :loading="tagLoading">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 会员升级对话框 -->
    <el-dialog
      v-model="showUpgradeDialog"
      title="会员升级"
      width="500px"
      :close-on-click-modal="false"
    >
      <div v-loading="upgradeLoading">
        <div class="upgrade-content">
          <h4>选择升级等级</h4>
          <el-radio-group v-model="selectedLevel" class="level-options">
            <div v-for="level in memberLevels" :key="level.value" class="level-option">
              <el-radio :label="level.value" class="level-radio">
                <div class="level-info">
                  <div class="level-header">
                    <span class="level-name">{{ level.label }}</span>
                    <span class="level-price">¥{{ level.price }}</span>
                  </div>
                  <div class="level-benefits">
                    <div v-for="benefit in level.benefits" :key="benefit" class="benefit-item">
                      <el-icon><Check /></el-icon>
                      {{ benefit }}
                    </div>
                  </div>
                </div>
              </el-radio>
            </div>
          </el-radio-group>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showUpgradeDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmUpgrade" :loading="upgradeLoading">
            确认升级
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft, Edit, More, Plus, Check
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const saving = ref(false)
const dialogVisible = ref(false)
const ordersLoading = ref(false)
const membershipLoading = ref(false)
const appointmentsLoading = ref(false)
const activeTab = ref('orders')

// 客户信息
const customer = ref({
  id: null,
  name: '',
  phone: '',
  gender: 'male',
  birthday: null,
  memberLevel: 'normal',
  address: '',
  notes: '',
  avatar: null,
  status: 'active',
  totalConsumption: 0,
  orderCount: 0,
  points: 0,
  lastVisit: null,
  createdAt: null,
  tags: []
})

// 订单数据
const orders = ref([])
const ordersPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 会员信息
const membership = ref(null)
const pointsHistory = ref([])

// 预约记录
const appointments = ref([])

// 标签管理
const showTagManageDialog = ref(false)
const availableTags = ref([])
const selectedTags = ref([])
const tagLoading = ref(false)

// 会员升级
const showUpgradeDialog = ref(false)
const memberLevels = ref([
  { value: 'silver', label: '银卡会员', price: 500, benefits: ['享受9.5折优惠', '生日当月免费服务一次'] },
  { value: 'gold', label: '金卡会员', price: 1000, benefits: ['享受9折优惠', '生日当月免费服务一次', '优先预约权'] },
  { value: 'diamond', label: '钻石会员', price: 2000, benefits: ['享受8.5折优惠', '生日当月免费服务一次', '优先预约权', '专属客服服务'] }
])
const selectedLevel = ref('')
const upgradeLoading = ref(false)

// 客户表单
const customerForm = reactive({
  name: '',
  phone: '',
  gender: 'male',
  birthday: null,
  memberLevel: 'normal',
  address: '',
  notes: ''
})

const customerFormRef = ref(null)

// 表单验证规则
const customerRules = {
  name: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

// 计算属性
const getMemberLevelType = (level) => {
  const types = {
    normal: 'info',
    silver: '',
    gold: 'warning',
    diamond: 'danger'
  }
  return types[level] || 'info'
}

const getMemberLevelText = (level) => {
  const texts = {
    normal: '普通客户',
    silver: '银卡会员',
    gold: '金卡会员',
    diamond: '钻石会员'
  }
  return texts[level] || '普通客户'
}

// 方法
const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

const formatDateTime = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString()
}

const formatCurrency = (amount) => {
  return `¥${(amount || 0).toFixed(2)}`
}

const getOrderStatusType = (status) => {
  const types = {
    pending: 'warning',
    paid: 'success',
    completed: 'success',
    cancelled: 'danger',
    refunded: 'info'
  }
  return types[status] || 'info'
}

const getOrderStatusText = (status) => {
  const texts = {
    pending: '待支付',
    paid: '已支付',
    completed: '已完成',
    cancelled: '已取消',
    refunded: '已退款'
  }
  return texts[status] || '未知'
}

const getAppointmentStatusType = (status) => {
  const types = {
    pending: 'warning',
    confirmed: 'success',
    completed: 'success',
    cancelled: 'danger',
    no_show: 'info'
  }
  return types[status] || 'info'
}

const getAppointmentStatusText = (status) => {
  const texts = {
    pending: '待确认',
    confirmed: '已确认',
    completed: '已完成',
    cancelled: '已取消',
    no_show: '未到店'
  }
  return texts[status] || '未知'
}

const loadCustomerDetail = async () => {
  loading.value = true
  try {
    const customerId = route.params.id

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟客户数据
    customer.value = {
      id: customerId,
      name: '张三',
      phone: '13800138001',
      gender: 'male',
      birthday: '1990-05-15',
      memberLevel: 'gold',
      address: '北京市朝阳区某某街道123号',
      notes: '老客户，服务态度要好',
      avatar: null,
      status: 'active',
      totalConsumption: 5680.50,
      orderCount: 12,
      points: 568,
      lastVisit: '2024-01-15',
      createdAt: '2023-06-01',
      tags: [
        { id: 1, name: 'VIP客户', type: 'danger' },
        { id: 2, name: '高消费', type: 'warning' },
        { id: 3, name: '推荐客户', type: 'success' }
      ]
    }

    // 加载订单数据
    loadOrders()

    // 加载会员信息
    loadMembership()

    // 加载预约记录
    loadAppointments()

  } catch (error) {
    console.error('加载客户详情失败:', error)
    ElMessage.error('加载客户详情失败')
  } finally {
    loading.value = false
  }
}

const loadOrders = async () => {
  ordersLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 500))

    orders.value = [
      {
        id: 1,
        orderNo: 'ORD202401150001',
        createTime: '2024-01-15 14:30:00',
        items: [
          { id: 1, name: '全身按摩' },
          { id: 2, name: '足疗' }
        ],
        amount: 298.00,
        status: 'completed'
      },
      {
        id: 2,
        orderNo: 'ORD202401100002',
        createTime: '2024-01-10 16:20:00',
        items: [
          { id: 3, name: '肩颈按摩' }
        ],
        amount: 128.00,
        status: 'completed'
      }
    ]

    ordersPagination.total = 2

  } catch (error) {
    console.error('加载订单失败:', error)
  } finally {
    ordersLoading.value = false
  }
}

const loadMembership = async () => {
  membershipLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 500))

    membership.value = {
      level: 'gold',
      cardNo: 'VIP20230601001',
      startDate: '2023-06-01',
      endDate: '2024-06-01',
      points: 568,
      status: 'active',
      benefits: [
        '享受9折优惠',
        '生日当月免费服务一次',
        '优先预约权',
        '专属客服服务',
        '积分兑换礼品'
      ]
    }

    pointsHistory.value = [
      {
        date: '2024-01-15 14:30:00',
        type: 'earn',
        points: 30,
        description: '消费获得积分',
        balance: 568
      },
      {
        date: '2024-01-10 16:20:00',
        type: 'earn',
        points: 13,
        description: '消费获得积分',
        balance: 538
      },
      {
        date: '2024-01-05 10:00:00',
        type: 'use',
        points: -50,
        description: '积分兑换礼品',
        balance: 525
      }
    ]

  } catch (error) {
    console.error('加载会员信息失败:', error)
  } finally {
    membershipLoading.value = false
  }
}

const loadAppointments = async () => {
  appointmentsLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 500))

    appointments.value = [
      {
        id: 1,
        appointmentNo: 'APT202401200001',
        appointmentTime: '2024-01-20 15:00:00',
        service: '全身按摩',
        technician: '李师傅',
        status: 'confirmed'
      },
      {
        id: 2,
        appointmentNo: 'APT202401150002',
        appointmentTime: '2024-01-15 14:30:00',
        service: '足疗',
        technician: '王师傅',
        status: 'completed'
      }
    ]

  } catch (error) {
    console.error('加载预约记录失败:', error)
  } finally {
    appointmentsLoading.value = false
  }
}

const goBack = () => {
  router.go(-1)
}

// 为当前客户创建订单
const createOrderForCustomer = () => {
  // 跳转到订单创建页面，并传递客户ID参数
  router.push({
    path: '/orders/create',
    query: {
      customerId: customerId.value
    }
  })
}

const editCustomer = () => {
  Object.assign(customerForm, {
    name: customer.value.name,
    phone: customer.value.phone,
    gender: customer.value.gender,
    birthday: customer.value.birthday ? new Date(customer.value.birthday) : null,
    memberLevel: customer.value.memberLevel,
    address: customer.value.address || '',
    notes: customer.value.notes || ''
  })
  dialogVisible.value = true
}

const saveCustomer = async () => {
  if (!customerFormRef.value) return

  try {
    await customerFormRef.value.validate()
    saving.value = true

    console.log('保存客户:', customerForm)
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 更新本地数据
    Object.assign(customer.value, customerForm)

    ElMessage.success('客户信息更新成功')
    dialogVisible.value = false

  } catch (error) {
    console.error('保存客户失败:', error)
    ElMessage.error('保存客户失败')
  } finally {
    saving.value = false
  }
}

const handleCommand = (command) => {
  switch (command) {
    case 'createOrder':
      router.push(`/orders/create?customerId=${customer.value.id}`)
      break
    case 'createAppointment':
      router.push(`/appointments/create?customerId=${customer.value.id}`)
      break
    case 'sendMessage':
      ElMessage.info('发送消息功能开发中...')
      break
    case 'exportData':
      ElMessage.info('导出数据功能开发中...')
      break
    case 'delete':
      deleteCustomer()
      break
  }
}

const deleteCustomer = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除客户 "${customer.value.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    console.log('删除客户:', customer.value)
    ElMessage.success('客户删除成功')
    router.push('/customers')

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除客户失败:', error)
      ElMessage.error('删除客户失败')
    }
  }
}

const manageCustomerTags = () => {
  showTagManageDialog.value = true
  loadAvailableTags()
}

// 加载可用标签
const loadAvailableTags = async () => {
  try {
    tagLoading.value = true
    // 这里应该调用API获取所有可用标签
    // const response = await getCustomerTags()
    // availableTags.value = response.data

    // 模拟数据
    availableTags.value = [
      { id: 1, name: 'VIP客户', color: '#f56c6c', category: '等级' },
      { id: 2, name: '高消费', color: '#e6a23c', category: '消费' },
      { id: 3, name: '推荐客户', color: '#67c23a', category: '来源' },
      { id: 4, name: '活跃用户', color: '#409eff', category: '活跃度' },
      { id: 5, name: '潜在流失', color: '#909399', category: '风险' },
      { id: 6, name: '新客户', color: '#67c23a', category: '状态' }
    ]

    // 设置当前客户已有的标签
    selectedTags.value = customer.value.tags ? customer.value.tags.map(tag => tag.id) : []

  } catch (error) {
    console.error('加载标签失败:', error)
    ElMessage.error('加载标签失败')
  } finally {
    tagLoading.value = false
  }
}

// 保存客户标签
const saveCustomerTags = async () => {
  try {
    tagLoading.value = true

    // 这里应该调用API更新客户标签
    // await updateCustomerTags(customerId.value, selectedTags.value)

    // 更新本地数据
    const selectedTagObjects = availableTags.value.filter(tag => selectedTags.value.includes(tag.id))
    customer.value.tags = selectedTagObjects

    ElMessage.success('标签更新成功')
    showTagManageDialog.value = false

  } catch (error) {
    console.error('更新标签失败:', error)
    ElMessage.error('更新标签失败')
  } finally {
    tagLoading.value = false
  }
}

const viewOrder = (order) => {
  router.push(`/orders/${order.id}`)
}

const viewAppointment = (appointment) => {
  router.push(`/appointments/${appointment.id}`)
}

const upgradeMembership = () => {
  // 过滤出比当前等级更高的等级
  const currentLevel = membership.value?.level || 'normal'
  const levelOrder = ['normal', 'silver', 'gold', 'diamond']
  const currentIndex = levelOrder.indexOf(currentLevel)

  const availableLevels = memberLevels.value.filter((level, index) => {
    const levelIndex = levelOrder.indexOf(level.value)
    return levelIndex > currentIndex
  })

  if (availableLevels.length === 0) {
    ElMessage.info('您已经是最高等级会员了')
    return
  }

  memberLevels.value = availableLevels
  selectedLevel.value = ''
  showUpgradeDialog.value = true
}

// 确认升级会员
const confirmUpgrade = async () => {
  if (!selectedLevel.value) {
    ElMessage.warning('请选择要升级的会员等级')
    return
  }

  try {
    upgradeLoading.value = true

    // 这里应该调用API进行会员升级
    // await upgradeCustomerMembership(customerId.value, selectedLevel.value)

    // 更新本地数据
    const selectedLevelInfo = memberLevels.value.find(level => level.value === selectedLevel.value)
    if (membership.value) {
      membership.value.level = selectedLevel.value
    }
    customer.value.memberLevel = selectedLevel.value

    ElMessage.success(`成功升级为${selectedLevelInfo.label}`)
    showUpgradeDialog.value = false

  } catch (error) {
    console.error('升级会员失败:', error)
    ElMessage.error('升级会员失败')
  } finally {
    upgradeLoading.value = false
  }
}

const renewMembership = () => {
  ElMessage.info('会员续费功能开发中...')
}

const createMembership = () => {
  ElMessage.info('创建会员功能开发中...')
}

const handleOrdersSizeChange = (size) => {
  ordersPagination.size = size
  loadOrders()
}

const handleOrdersCurrentChange = (page) => {
  ordersPagination.page = page
  loadOrders()
}

// 初始化
onMounted(() => {
  loadCustomerDetail()
})
</script>

<style lang="scss" scoped>
.customer-detail {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
  }

  .header-right {
    display: flex;
    gap: 12px;
  }
}

.loading-container {
  padding: 20px;
}

.customer-content {
  .info-card {
    margin-bottom: 20px;
  }

  .tags-card {
    margin-bottom: 20px;
  }
}

.customer-basic-info {
  display: flex;
  gap: 24px;

  .avatar-container {
    position: relative;
    flex-shrink: 0;

    .member-badge {
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
    }
  }

  .info-container {
    flex: 1;

    .customer-name {
      margin: 0 0 16px 0;
      font-size: 20px;
      font-weight: 600;
      color: #303133;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      margin-bottom: 16px;
    }

    .info-item, .address-item, .notes-item {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .label {
        font-size: 12px;
        color: #909399;
        font-weight: 500;
      }

      .value {
        font-size: 14px;
        color: #303133;
      }
    }

    .address-item, .notes-item {
      grid-column: 1 / -1;
    }
  }

  .stats-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex-shrink: 0;

    .stat-item {
      text-align: center;

      .stat-value {
        display: block;
        font-size: 24px;
        font-weight: 600;
        color: #E6A23C;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tags-content {
  .customer-tag {
    margin-right: 8px;
    margin-bottom: 8px;
  }
}

.detail-tabs {
  .el-tab-pane {
    padding-top: 20px;
  }

  .tab-header {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
  }
}

.amount {
  font-weight: 600;
  color: #E6A23C;
}

.item-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.empty-data {
  padding: 40px 0;
  text-align: center;
}

.membership-content {
  .membership-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 24px;
    color: white;
    margin-bottom: 24px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .level-info {
        display: flex;
        align-items: center;
        gap: 16px;

        .card-no {
          font-size: 14px;
          opacity: 0.9;
        }
      }
    }

    .card-body {
      display: flex;
      gap: 40px;

      .membership-info {
        flex: 1;

        .info-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 12px;

          .label {
            opacity: 0.8;
          }

          .value {
            font-weight: 500;
          }
        }
      }

      .membership-benefits {
        flex: 1;

        h4 {
          margin: 0 0 16px 0;
          font-size: 16px;
        }

        .benefits-list {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 14px;

            .el-icon {
              color: #67C23A;
            }
          }
        }
      }
    }
  }

  .points-history {
    .section-header {
      margin-bottom: 16px;

      h3 {
        margin: 0;
        font-size: 18px;
        color: #303133;
      }
    }
  }

  .no-membership {
    padding: 60px 0;
    text-align: center;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 标签管理对话框样式 */
.tag-categories {
  margin-bottom: 20px;

  h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: #303133;
  }

  .available-tags {
    .tag-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      padding: 8px;
      border-radius: 6px;
      transition: background-color 0.3s;

      &:hover {
        background-color: #f5f7fa;
      }

      .el-checkbox {
        margin-right: 0;
      }

      .tag-category {
        margin-left: 8px;
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.selected-tags-preview {
  border-top: 1px solid #ebeef5;
  padding-top: 16px;

  h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: #303133;
  }

  .tags-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
}

/* 会员升级对话框样式 */
.upgrade-content {
  h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: #303133;
  }

  .level-options {
    width: 100%;

    .level-option {
      margin-bottom: 16px;
      border: 1px solid #ebeef5;
      border-radius: 8px;
      padding: 16px;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
        background-color: #f0f9ff;
      }

      .level-radio {
        width: 100%;

        .el-radio__label {
          width: 100%;
          padding-left: 0;
        }
      }

      .level-info {
        width: 100%;

        .level-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .level-name {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }

          .level-price {
            font-size: 18px;
            font-weight: 600;
            color: #f56c6c;
          }
        }

        .level-benefits {
          .benefit-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
            color: #606266;

            .el-icon {
              margin-right: 8px;
              color: #67c23a;
            }
          }
        }
      }
    }

    .level-option:has(.el-radio.is-checked) {
      border-color: #409eff;
      background-color: #f0f9ff;
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .customer-basic-info {
    flex-direction: column;

    .info-container .info-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .stats-container {
      flex-direction: row;
      justify-content: space-around;
    }
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .customer-basic-info {
    .info-container .info-grid {
      grid-template-columns: 1fr;
    }
  }

  .membership-content .membership-card .card-body {
    flex-direction: column;
    gap: 20px;
  }
}
</style>