<template>
  <div class="store-list-container">
    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" @click="handleAddStore">
        <el-icon><Plus /></el-icon>新增门店
      </el-button>
      
      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索门店名称"
          clearable
          @clear="handleSearch"
        >
          <template #append>
            <el-button :icon="Search" @click="handleSearch" />
          </template>
        </el-input>
      </div>
    </div>
    
    <!-- 门店列表 -->
    <el-card shadow="never" class="store-list-card">
      <!-- 数据加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>正在加载门店数据...</span>
      </div>

      <!-- 数据加载错误 -->
      <div v-else-if="loadError" class="error-container">
        <el-icon class="error-icon"><Warning /></el-icon>
        <div class="error-content">
          <h3>数据加载失败</h3>
          <p>{{ errorMessage }}</p>
          <el-button type="primary" @click="retryLoad">重试</el-button>
        </div>
      </div>

      <!-- 空数据状态 -->
      <div v-else-if="storeList.length === 0" class="empty-container">
        <el-icon class="empty-icon"><Shop /></el-icon>
        <div class="empty-content">
          <h3>暂无门店数据</h3>
          <p>点击"新增门店"按钮创建第一个门店</p>
          <el-button type="primary" @click="handleAddStore">新增门店</el-button>
        </div>
      </div>

      <el-table
        v-else
        v-loading="loading"
        :data="storeList"
        style="width: 100%"
        border
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="门店名称" min-width="150" />
        <el-table-column prop="address" label="地址" min-width="250" />
        <el-table-column prop="phone" label="联系电话" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
              {{ scope.row.status === 'active' ? '营业中' : '已停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button link type="primary" @click="handleViewStore(scope.row)">查看</el-button>
            <el-button link type="primary" @click="handleEditStore(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑门店对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="isEdit ? '编辑门店' : '新增门店'"
      width="600px"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="storeFormRef"
        :model="storeForm"
        :rules="storeFormRules"
        label-width="100px"
      >
        <el-form-item label="门店名称" prop="name">
          <el-input v-model="storeForm.name" placeholder="请输入门店名称" />
        </el-form-item>

        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="storeForm.phone" placeholder="请输入联系电话" />
        </el-form-item>

        <el-form-item label="详细地址" prop="address">
          <el-input
            v-model="storeForm.address"
            type="textarea"
            :rows="3"
            placeholder="请输入详细地址"
          />
        </el-form-item>

        <el-form-item label="营业时间" prop="business_hours">
          <el-input v-model="storeForm.business_hours" placeholder="例如：09:00-21:00" />
        </el-form-item>

        <el-form-item label="门店状态" prop="status">
          <el-radio-group v-model="storeForm.status">
            <el-radio label="active">营业中</el-radio>
            <el-radio label="inactive">已停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Search, Plus, Loading, Warning, Shop } from '@element-plus/icons-vue'
import storeApi from '@/api/store'

const router = useRouter()

// 列表数据
const loading = ref(false)
const loadError = ref(false)
const errorMessage = ref('')
const storeList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const searchKeyword = ref('')

// 对话框相关
const showCreateDialog = ref(false)
const isEdit = ref(false)
const submitLoading = ref(false)
const storeFormRef = ref()

// 表单数据
const storeForm = ref({
  name: '',
  phone: '',
  address: '',
  business_hours: '',
  status: 'active'
})

// 表单验证规则
const storeFormRules = {
  name: [
    { required: true, message: '请输入门店名称', trigger: 'blur' },
    { min: 2, max: 50, message: '门店名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入详细地址', trigger: 'blur' },
    { min: 5, max: 200, message: '地址长度在 5 到 200 个字符', trigger: 'blur' }
  ],
  business_hours: [
    { required: true, message: '请输入营业时间', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择门店状态', trigger: 'change' }
  ]
}

// 初始化数据
onMounted(() => {
  console.log('StoreList组件mounted')
  fetchStoreList()
})

// 获取门店列表
const fetchStoreList = async () => {
  loading.value = true
  loadError.value = false
  errorMessage.value = ''

  try {
    console.log('开始获取门店列表')
    const params = {
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value
    }

    if (searchKeyword.value) {
      params.search = searchKeyword.value
    }

    const res = await storeApi.getStoreList(params)
    console.log('API返回数据:', res)

    // 后端直接返回数组，不是包装在data字段中
    storeList.value = Array.isArray(res) ? res : []
    total.value = Array.isArray(res) ? res.length : 0

    console.log('门店列表更新:', storeList.value)
  } catch (error) {
    console.error('获取门店列表失败', error)
    loadError.value = true
    errorMessage.value = error.response?.data?.detail || error.message || '网络连接失败，请检查网络设置'
    ElMessage.error('获取门店列表失败')
  } finally {
    loading.value = false
  }
}

// 重试加载
const retryLoad = () => {
  fetchStoreList()
}

// 重置表单
const resetForm = () => {
  storeForm.value = {
    name: '',
    phone: '',
    address: '',
    business_hours: '',
    status: 'active'
  }
  isEdit.value = false
  if (storeFormRef.value) {
    storeFormRef.value.clearValidate()
  }
}

// 关闭对话框
const handleDialogClose = () => {
  showCreateDialog.value = false
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!storeFormRef.value) return

  try {
    const valid = await storeFormRef.value.validate()
    if (!valid) return

    submitLoading.value = true

    if (isEdit.value) {
      // 编辑门店
      await storeApi.updateStore(storeForm.value.id, storeForm.value)
      ElMessage.success('门店更新成功')
    } else {
      // 创建门店
      await storeApi.createStore(storeForm.value)
      ElMessage.success('门店创建成功')
    }

    handleDialogClose()
    fetchStoreList() // 刷新列表

  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(error.response?.data?.detail || '操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchStoreList()
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchStoreList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchStoreList()
}

// 新增门店
const handleAddStore = () => {
  console.log('新增门店')
  showCreateDialog.value = true
  resetForm()
}

// 查看门店
const handleViewStore = (row) => {
  router.push(`/stores/${row.id}`)
}

// 编辑门店
const handleEditStore = (row) => {
  console.log('编辑门店', row)
  isEdit.value = true
  storeForm.value = {
    id: row.id,
    name: row.name,
    phone: row.phone,
    address: row.address,
    business_hours: row.business_hours || '',
    status: row.status
  }
  showCreateDialog.value = true
}
</script>

<style lang="scss" scoped>
.store-list-container {
  .action-bar {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    
    .search-box {
      width: 300px;
    }
  }
  
  .store-list-card {
    margin-bottom: 20px;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  /* 加载状态样式 */
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #909399;
  }

  .loading-container .el-icon {
    font-size: 32px;
    margin-bottom: 16px;
  }

  /* 错误状态样式 */
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
  }

  .error-icon {
    font-size: 48px;
    color: #F56C6C;
    margin-bottom: 16px;
  }

  .error-content h3 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 18px;
  }

  .error-content p {
    margin: 0 0 20px 0;
    color: #606266;
    font-size: 14px;
  }

  /* 空数据状态样式 */
  .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
  }

  .empty-icon {
    font-size: 48px;
    color: #C0C4CC;
    margin-bottom: 16px;
  }

  .empty-content h3 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 18px;
  }

  .empty-content p {
    margin: 0 0 20px 0;
    color: #606266;
    font-size: 14px;
  }
}
</style>
