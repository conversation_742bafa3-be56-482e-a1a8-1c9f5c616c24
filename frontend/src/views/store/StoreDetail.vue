<template>
  <div class="store-detail-container">
    <!-- 返回按钮 -->
    <div class="back-button">
      <el-button @click="goBack" plain>
        <el-icon><Back /></el-icon>返回列表
      </el-button>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>
    
    <!-- 详情内容 -->
    <template v-else>
      <!-- 基本信息卡片 -->
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <span>门店基本信息</span>
            <el-button type="primary" @click="handleEdit" plain>编辑</el-button>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="门店名称">{{ storeInfo.name }}</el-descriptions-item>
          <el-descriptions-item label="门店状态">
            <el-tag :type="storeInfo.status === 'active' ? 'success' : 'danger'">
              {{ storeInfo.status === 'active' ? '营业中' : '已停用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ storeInfo.phone }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(storeInfo.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="详细地址" :span="2">{{ storeInfo.address }}</el-descriptions-item>
          <el-descriptions-item label="营业时间" :span="2">{{ storeInfo.business_hours || '未设置' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 统计信息卡片 -->
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <span>经营概况</span>
            <el-radio-group v-model="statTimeRange" size="small">
              <el-radio-button label="week">本周</el-radio-button>
              <el-radio-button label="month">本月</el-radio-button>
              <el-radio-button label="year">本年</el-radio-button>
            </el-radio-group>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="6" v-for="(item, index) in statItems" :key="index">
            <div class="stat-item">
              <div class="stat-title">{{ item.title }}</div>
              <div class="stat-value">{{ item.value }}</div>
              <div class="stat-compare" :class="item.trend">
                <el-icon v-if="item.trend === 'up'"><CaretTop /></el-icon>
                <el-icon v-else><CaretBottom /></el-icon>
                {{ item.compare }}
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 员工列表卡片 -->
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <span>门店员工 ({{ employees.length }})</span>
            <el-button type="primary" plain>添加员工</el-button>
          </div>
        </template>
        <el-table :data="employees" style="width: 100%" border>
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="姓名" width="120" />
          <el-table-column prop="role" label="职位" width="120">
            <template #default="scope">
              <el-tag :type="getRoleType(scope.row.role)">{{ getRoleLabel(scope.row.role) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="phone" label="手机号" width="150" />
          <el-table-column prop="hire_date" label="入职日期" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
                {{ scope.row.status === 'active' ? '在职' : '离职' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button link type="primary">查看</el-button>
              <el-button link type="primary">编辑</el-button>
              <el-button link type="danger">离职</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      
      <!-- 项目列表卡片 -->
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <span>门店项目 ({{ services.length }})</span>
            <el-button type="primary" plain>添加项目</el-button>
          </div>
        </template>
        <el-table :data="services" style="width: 100%" border>
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="项目名称" min-width="150" />
          <el-table-column prop="type" label="类型" width="120">
            <template #default="scope">
              <el-tag :type="scope.row.type === 'service' ? 'primary' : 'success'">
                {{ scope.row.type === 'service' ? '服务' : '商品' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="group_price" label="集团价格" width="120">
            <template #default="scope">
              ¥ {{ scope.row.group_price }}
            </template>
          </el-table-column>
          <el-table-column prop="store_price" label="门店价格" width="120">
            <template #default="scope">
              ¥ {{ scope.row.store_price }}
            </template>
          </el-table-column>
          <el-table-column prop="is_enabled" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.is_enabled ? 'success' : 'info'">
                {{ scope.row.is_enabled ? '已启用' : '已禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button link type="primary">编辑价格</el-button>
              <el-button link :type="scope.row.is_enabled ? 'danger' : 'success'">
                {{ scope.row.is_enabled ? '禁用' : '启用' }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </template>
    
    <!-- 编辑门店对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="编辑门店信息"
      width="600px"
      destroy-on-close
    >
      <el-form
        ref="storeFormRef"
        :model="storeForm"
        :rules="storeRules"
        label-width="100px"
      >
        <el-form-item label="门店名称" prop="name">
          <el-input v-model="storeForm.name" placeholder="请输入门店名称" />
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="storeForm.address" placeholder="请输入门店地址" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="storeForm.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="营业时间">
          <el-input
            v-model="storeForm.businessHours"
            type="textarea"
            :rows="2"
            placeholder="例如：周一至周五 10:00-22:00，周六至周日 10:00-23:00"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="storeForm.status">
            <el-radio label="active">营业中</el-radio>
            <el-radio label="inactive">已停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Back, CaretTop, CaretBottom } from '@element-plus/icons-vue'
import storeApi from '@/api/store'

const router = useRouter()
const route = useRoute()
const storeId = route.params.id

// 加载状态
const loading = ref(true)

// 门店信息
const storeInfo = ref({})

// 统计时间范围
const statTimeRange = ref('month')

// 统计数据
const statItems = ref([
  { title: '营业额', value: '¥ 56,789', trend: 'up', compare: '12% 同比上月' },
  { title: '订单数', value: '324', trend: 'up', compare: '8% 同比上月' },
  { title: '客流量', value: '412', trend: 'up', compare: '5% 同比上月' },
  { title: '客单价', value: '¥ 175', trend: 'down', compare: '3% 同比上月' }
])

// 员工数据
const employees = ref([])

// 服务项目数据
const services = ref([])

// 编辑表单
const dialogVisible = ref(false)
const submitLoading = ref(false)
const storeFormRef = ref(null)
const storeForm = reactive({
  id: '',
  name: '',
  address: '',
  phone: '',
  businessHours: '',
  status: 'active'
})

// 表单验证规则
const storeRules = {
  name: [
    { required: true, message: '请输入门店名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入门店地址', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/, message: '请输入正确的电话格式', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择门店状态', trigger: 'change' }
  ]
}

// 获取门店详情
const fetchStoreDetail = async () => {
  loading.value = true
  try {
    // 获取门店基本信息
    const storeData = await storeApi.getStoreDetail(storeId)
    storeInfo.value = storeData
    
    // 这里可以添加获取员工和服务项目的API调用
    // 暂时使用模拟数据
    employees.value = [
      { id: 1, name: '张店长', role: 'manager', phone: '13800138001', hire_date: '2023-01-01', status: 'active' },
      { id: 2, name: '李技师', role: 'technician', phone: '13800138002', hire_date: '2023-02-01', status: 'active' },
      { id: 3, name: '王技师', role: 'technician', phone: '13800138003', hire_date: '2023-02-15', status: 'active' },
      { id: 4, name: '赵收银', role: 'cashier', phone: '13800138004', hire_date: '2023-03-01', status: 'active' }
    ]
    
    services.value = [
      { id: 1, name: '全身按摩', type: 'service', group_price: 198, store_price: 198, is_enabled: true },
      { id: 2, name: '足底按摩', type: 'service', group_price: 128, store_price: 138, is_enabled: true },
      { id: 3, name: '肩颈按摩', type: 'service', group_price: 98, store_price: 108, is_enabled: true },
      { id: 4, name: '精油按摩', type: 'service', group_price: 228, store_price: 228, is_enabled: false }
    ]
    
    loading.value = false
  } catch (error) {
    console.error('获取门店详情失败', error)
    ElMessage.error('获取门店详情失败')
    loading.value = false
  }
}

// 返回列表页
const goBack = () => {
  router.push('/stores')
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 获取角色类型
const getRoleType = (role) => {
  const map = {
    'manager': 'danger',
    'technician': 'primary',
    'cashier': 'success',
    'assistant': 'warning'
  }
  return map[role] || 'info'
}

// 获取角色标签
const getRoleLabel = (role) => {
  const map = {
    'manager': '店长',
    'technician': '技师',
    'cashier': '收银',
    'assistant': '助理'
  }
  return map[role] || role
}

// 编辑门店信息
const handleEdit = () => {
  Object.assign(storeForm, {
    id: storeInfo.value.id,
    name: storeInfo.value.name,
    address: storeInfo.value.address,
    phone: storeInfo.value.phone,
    businessHours: storeInfo.value.business_hours || '',
    status: storeInfo.value.status
  })
  dialogVisible.value = true
}

// 提交编辑
const handleSubmit = async () => {
  if (!storeFormRef.value) return
  
  await storeFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      
      try {
        // 处理营业时间格式
        const formData = { ...storeForm }
        if (formData.businessHours) {
          formData.business_hours = formData.businessHours
          delete formData.businessHours
        }
        
        // 更新门店信息
        const { id, ...updateData } = formData
        await storeApi.updateStore(id, updateData)
        
        // 更新本地数据
        const updatedStore = await storeApi.getStoreDetail(id)
        storeInfo.value = updatedStore
        
        ElMessage.success('更新门店信息成功')
        dialogVisible.value = false
        submitLoading.value = false
      } catch (error) {
        console.error('提交失败', error)
        ElMessage.error('提交失败')
        submitLoading.value = false
      }
    }
  })
}

// 初始化
onMounted(() => {
  fetchStoreDetail()
})
</script>

<style lang="scss" scoped>
.store-detail-container {
  .back-button {
    margin-bottom: 20px;
  }
  
  .loading-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
  }
  
  .detail-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .stat-item {
      padding: 20px;
      text-align: center;
      
      .stat-title {
        font-size: 14px;
        color: #909399;
        margin-bottom: 10px;
      }
      
      .stat-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 10px;
      }
      
      .stat-compare {
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &.up {
          color: #67C23A;
        }
        
        &.down {
          color: #F56C6C;
        }
      }
    }
  }
}
</style> 