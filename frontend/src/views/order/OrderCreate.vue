<template>
  <div class="order-create">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" icon="ArrowLeft">返回</el-button>
        <h1>{{ isEdit ? '编辑订单' : '新增订单' }}</h1>
      </div>
      <div class="header-right">
        <el-button @click="saveDraft">保存草稿</el-button>
        <el-button type="primary" @click="submitOrder" :loading="submitting">
          {{ isEdit ? '更新订单' : '创建订单' }}
        </el-button>
      </div>
    </div>

    <!-- 订单表单 -->
    <div class="order-form">
      <el-row :gutter="20">
        <!-- 左侧表单 -->
        <el-col :span="16">
          <!-- 客户信息 -->
          <el-card class="form-card">
            <template #header>
              <span>客户信息</span>
            </template>
            <el-form :model="orderForm" :rules="orderRules" ref="orderFormRef" label-width="100px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="选择客户" prop="customerId">
                    <el-select
                      v-model="orderForm.customerId"
                      placeholder="请选择客户"
                      filterable
                      remote
                      :remote-method="searchCustomers"
                      :loading="customerLoading"
                      style="width: 100%"
                      @change="handleCustomerChange"
                    >
                      <el-option
                        v-for="customer in customers"
                        :key="customer.id"
                        :label="`${customer.name} (${customer.phone})`"
                        :value="customer.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <el-button type="primary" @click="showCreateCustomerDialog">
                      <el-icon><Plus /></el-icon>
                      新增客户
                    </el-button>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <!-- 选中客户信息展示 -->
              <div v-if="selectedCustomer" class="selected-customer">
                <el-descriptions :column="3" border>
                  <el-descriptions-item label="客户姓名">{{ selectedCustomer.name }}</el-descriptions-item>
                  <el-descriptions-item label="手机号">{{ selectedCustomer.phone }}</el-descriptions-item>
                  <el-descriptions-item label="会员等级">{{ getMemberLevelText(selectedCustomer.memberLevel) }}</el-descriptions-item>
                  <el-descriptions-item label="累计消费">¥{{ selectedCustomer.totalConsumption?.toFixed(2) || '0.00' }}</el-descriptions-item>
                  <el-descriptions-item label="积分余额">{{ selectedCustomer.points || 0 }}</el-descriptions-item>
                  <el-descriptions-item label="上次到店">{{ formatDate(selectedCustomer.lastVisit) || '首次到店' }}</el-descriptions-item>
                </el-descriptions>
              </div>
            </el-form>
          </el-card>

          <!-- 服务项目 -->
          <el-card class="form-card">
            <template #header>
              <div class="card-header">
                <span>服务项目</span>
                <el-button type="primary" @click="showServiceSelector">
                  <el-icon><Plus /></el-icon>
                  添加服务
                </el-button>
              </div>
            </template>
            <el-table :data="orderForm.services" style="width: 100%">
              <el-table-column prop="name" label="服务名称" />
              <el-table-column prop="category" label="分类" width="120" />
              <el-table-column prop="duration" label="时长" width="100">
                <template #default="scope">
                  {{ scope.row.duration }}分钟
                </template>
              </el-table-column>
              <el-table-column prop="price" label="单价" width="120" align="right">
                <template #default="scope">
                  ¥{{ scope.row.price.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column prop="quantity" label="数量" width="100">
                <template #default="scope">
                  <el-input-number
                    v-model="scope.row.quantity"
                    :min="1"
                    :max="10"
                    size="small"
                    @change="calculateTotal"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="subtotal" label="小计" width="120" align="right">
                <template #default="scope">
                  ¥{{ (scope.row.price * scope.row.quantity).toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column prop="technician" label="技师" width="120">
                <template #default="scope">
                  <el-select
                    v-model="scope.row.technicianId"
                    placeholder="选择技师"
                    size="small"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="tech in technicians"
                      :key="tech.id"
                      :label="tech.name"
                      :value="tech.id"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80" align="center">
                <template #default="scope">
                  <el-button
                    type="danger"
                    size="small"
                    @click="removeService(scope.$index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div v-if="orderForm.services.length === 0" class="empty-services">
              <el-empty description="请添加服务项目" />
            </div>
          </el-card>

          <!-- 备注信息 -->
          <el-card class="form-card">
            <template #header>
              <span>备注信息</span>
            </template>
            <el-form :model="orderForm" label-width="100px">
              <el-form-item label="订单备注">
                <el-input
                  v-model="orderForm.notes"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入订单备注"
                />
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>

        <!-- 右侧订单汇总 -->
        <el-col :span="8">
          <el-card class="summary-card" style="position: sticky; top: 20px;">
            <template #header>
              <span>订单汇总</span>
            </template>
            <div class="order-summary">
              <div class="summary-item">
                <span class="label">服务项目：</span>
                <span class="value">{{ orderForm.services.length }}项</span>
              </div>
              <div class="summary-item">
                <span class="label">服务时长：</span>
                <span class="value">{{ totalDuration }}分钟</span>
              </div>
              <div class="summary-item">
                <span class="label">原价金额：</span>
                <span class="value">¥{{ originalAmount.toFixed(2) }}</span>
              </div>
              <div class="summary-item" v-if="discountAmount > 0">
                <span class="label">优惠金额：</span>
                <span class="value discount">-¥{{ discountAmount.toFixed(2) }}</span>
              </div>
              <div class="summary-item total">
                <span class="label">实付金额：</span>
                <span class="value amount">¥{{ totalAmount.toFixed(2) }}</span>
              </div>
              
              <!-- 优惠设置 -->
              <el-divider />
              <div class="discount-section">
                <h4>优惠设置</h4>
                <el-form label-width="80px">
                  <el-form-item label="优惠类型">
                    <el-select v-model="discountType" @change="handleDiscountTypeChange">
                      <el-option label="无优惠" value="none" />
                      <el-option label="会员折扣" value="member" />
                      <el-option label="固定金额" value="fixed" />
                      <el-option label="百分比" value="percent" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="优惠值" v-if="discountType !== 'none'">
                    <el-input-number
                      v-model="discountValue"
                      :min="0"
                      :max="discountType === 'percent' ? 100 : originalAmount"
                      :precision="2"
                      @change="calculateDiscount"
                    />
                    <span v-if="discountType === 'percent'">%</span>
                    <span v-else-if="discountType === 'fixed'">元</span>
                  </el-form-item>
                </el-form>
              </div>
              
              <!-- 支付方式 -->
              <el-divider />
              <div class="payment-section">
                <h4>支付方式</h4>
                <el-radio-group v-model="orderForm.paymentMethod">
                  <el-radio label="cash">现金</el-radio>
                  <el-radio label="alipay">支付宝</el-radio>
                  <el-radio label="wechat">微信支付</el-radio>
                  <el-radio label="card">银行卡</el-radio>
                  <el-radio label="member">会员卡</el-radio>
                </el-radio-group>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 服务选择对话框 -->
    <el-dialog
      title="选择服务项目"
      v-model="serviceDialogVisible"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="service-selector">
        <div class="service-categories">
          <el-menu
            :default-active="activeCategory"
            @select="handleCategorySelect"
            mode="vertical"
          >
            <el-menu-item
              v-for="category in serviceCategories"
              :key="category.id"
              :index="category.id"
            >
              {{ category.name }}
            </el-menu-item>
          </el-menu>
        </div>
        <div class="service-list">
          <el-row :gutter="20">
            <el-col
              v-for="service in filteredServices"
              :key="service.id"
              :span="8"
            >
              <el-card
                class="service-item"
                :class="{ selected: isServiceSelected(service) }"
                @click="toggleService(service)"
              >
                <div class="service-info">
                  <h4>{{ service.name }}</h4>
                  <p class="service-desc">{{ service.description }}</p>
                  <div class="service-meta">
                    <span class="duration">{{ service.duration }}分钟</span>
                    <span class="price">¥{{ service.price.toFixed(2) }}</span>
                  </div>
                </div>
                <div class="service-actions">
                  <el-checkbox :model-value="isServiceSelected(service)" />
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="serviceDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmServiceSelection">
            确定 (已选{{ selectedServices.length }}项)
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新增客户对话框 -->
    <CustomerEditDialog
      v-model:visible="customerDialogVisible"
      :is-edit="false"
      @submit="handleCustomerCreate"
      @cancel="customerDialogVisible = false"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Plus } from '@element-plus/icons-vue'
import CustomerEditDialog from '@/components/customer/CustomerEditDialog.vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const submitting = ref(false)
const customerLoading = ref(false)
const serviceDialogVisible = ref(false)
const customerDialogVisible = ref(false)
const isEdit = ref(false)

const customers = ref([])
const selectedCustomer = ref(null)
const technicians = ref([])
const serviceCategories = ref([])
const services = ref([])
const selectedServices = ref([])
const activeCategory = ref('')

// 表单数据
const orderForm = reactive({
  customerId: null,
  services: [],
  paymentMethod: 'cash',
  notes: ''
})

const orderFormRef = ref(null)

// 优惠相关
const discountType = ref('none')
const discountValue = ref(0)
const discountAmount = ref(0)

// 表单验证规则
const orderRules = {
  customerId: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ]
}

// 计算属性
const originalAmount = computed(() => {
  return orderForm.services.reduce((total, service) => {
    return total + (service.price * service.quantity)
  }, 0)
})

const totalAmount = computed(() => {
  return Math.max(0, originalAmount.value - discountAmount.value)
})

const totalDuration = computed(() => {
  return orderForm.services.reduce((total, service) => {
    return total + (service.duration * service.quantity)
  }, 0)
})

const filteredServices = computed(() => {
  if (!activeCategory.value) return services.value
  return services.value.filter(service => service.categoryId === activeCategory.value)
})

// 方法
const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

const getMemberLevelText = (level) => {
  const texts = {
    normal: '普通客户',
    silver: '银卡会员',
    gold: '金卡会员',
    diamond: '钻石会员'
  }
  return texts[level] || '普通客户'
}

// 加载客户列表
const loadCustomers = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))

    const allCustomers = [
      {
        id: 1,
        name: '张三',
        phone: '13800138001',
        memberLevel: 'gold',
        totalConsumption: 5680.50,
        points: 568,
        lastVisit: '2024-01-15'
      },
      {
        id: 2,
        name: '李四',
        phone: '13800138002',
        memberLevel: 'silver',
        totalConsumption: 2340.00,
        points: 234,
        lastVisit: '2024-01-10'
      },
      {
        id: 3,
        name: '王五',
        phone: '13800138003',
        memberLevel: 'diamond',
        totalConsumption: 12800.00,
        points: 1280,
        lastVisit: '2024-01-18'
      },
      {
        id: 4,
        name: '赵六',
        phone: '13800138004',
        memberLevel: 'normal',
        totalConsumption: 580.00,
        points: 58,
        lastVisit: '2024-01-12'
      }
    ]

    customers.value = allCustomers

  } catch (error) {
    console.error('加载客户列表失败:', error)
  }
}

const searchCustomers = async (query) => {
  if (!query) {
    // 如果没有查询条件，加载所有客户
    await loadCustomers()
    return
  }

  customerLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))

    const allCustomers = [
      {
        id: 1,
        name: '张三',
        phone: '13800138001',
        memberLevel: 'gold',
        totalConsumption: 5680.50,
        points: 568,
        lastVisit: '2024-01-15'
      },
      {
        id: 2,
        name: '李四',
        phone: '13800138002',
        memberLevel: 'silver',
        totalConsumption: 2340.00,
        points: 234,
        lastVisit: '2024-01-10'
      },
      {
        id: 3,
        name: '王五',
        phone: '13800138003',
        memberLevel: 'diamond',
        totalConsumption: 12800.00,
        points: 1280,
        lastVisit: '2024-01-18'
      },
      {
        id: 4,
        name: '赵六',
        phone: '13800138004',
        memberLevel: 'normal',
        totalConsumption: 580.00,
        points: 58,
        lastVisit: '2024-01-12'
      }
    ]

    customers.value = allCustomers.filter(customer =>
      customer.name.includes(query) || customer.phone.includes(query)
    )

  } catch (error) {
    console.error('搜索客户失败:', error)
  } finally {
    customerLoading.value = false
  }
}

const handleCustomerChange = (customerId) => {
  selectedCustomer.value = customers.value.find(c => c.id === customerId)
  
  // 如果是会员，自动应用会员折扣
  if (selectedCustomer.value?.memberLevel !== 'normal') {
    discountType.value = 'member'
    calculateDiscount()
  }
}

const loadTechnicians = async () => {
  try {
    // 模拟API调用
    technicians.value = [
      { id: 1, name: '李师傅' },
      { id: 2, name: '王师傅' },
      { id: 3, name: '张师傅' }
    ]
  } catch (error) {
    console.error('加载技师列表失败:', error)
  }
}

const loadServices = async () => {
  try {
    // 模拟API调用
    serviceCategories.value = [
      { id: '1', name: '按摩服务' },
      { id: '2', name: '足疗服务' },
      { id: '3', name: '美容服务' }
    ]
    
    services.value = [
      {
        id: 1,
        name: '全身按摩',
        description: '专业全身按摩，缓解疲劳',
        categoryId: '1',
        duration: 90,
        price: 198.00
      },
      {
        id: 2,
        name: '肩颈按摩',
        description: '针对肩颈部位的专业按摩',
        categoryId: '1',
        duration: 60,
        price: 128.00
      },
      {
        id: 3,
        name: '足疗',
        description: '传统足疗，促进血液循环',
        categoryId: '2',
        duration: 60,
        price: 100.00
      }
    ]
    
    if (serviceCategories.value.length > 0) {
      activeCategory.value = serviceCategories.value[0].id
    }
    
  } catch (error) {
    console.error('加载服务列表失败:', error)
  }
}

const showServiceSelector = () => {
  selectedServices.value = [...orderForm.services]
  serviceDialogVisible.value = true
}

const handleCategorySelect = (categoryId) => {
  activeCategory.value = categoryId
}

const isServiceSelected = (service) => {
  return selectedServices.value.some(s => s.id === service.id)
}

const toggleService = (service) => {
  const index = selectedServices.value.findIndex(s => s.id === service.id)
  if (index > -1) {
    selectedServices.value.splice(index, 1)
  } else {
    selectedServices.value.push({
      ...service,
      quantity: 1,
      technicianId: null
    })
  }
}

const confirmServiceSelection = () => {
  orderForm.services = [...selectedServices.value]
  calculateTotal()
  serviceDialogVisible.value = false
}

const removeService = (index) => {
  orderForm.services.splice(index, 1)
  calculateTotal()
}

const calculateTotal = () => {
  calculateDiscount()
}

const handleDiscountTypeChange = () => {
  discountValue.value = 0
  calculateDiscount()
}

const calculateDiscount = () => {
  switch (discountType.value) {
    case 'none':
      discountAmount.value = 0
      break
    case 'member':
      // 会员折扣逻辑
      const memberDiscounts = {
        silver: 0.05, // 5%
        gold: 0.1,    // 10%
        diamond: 0.15 // 15%
      }
      const rate = memberDiscounts[selectedCustomer.value?.memberLevel] || 0
      discountAmount.value = originalAmount.value * rate
      break
    case 'fixed':
      discountAmount.value = Math.min(discountValue.value, originalAmount.value)
      break
    case 'percent':
      discountAmount.value = originalAmount.value * (discountValue.value / 100)
      break
  }
}

const showCreateCustomerDialog = () => {
  customerDialogVisible.value = true
}

const handleCustomerCreate = async (customerData) => {
  try {
    console.log('创建客户:', customerData)
    // 这里应该调用API创建客户
    
    ElMessage.success('客户创建成功')
    customerDialogVisible.value = false
    
    // 重新搜索客户列表
    await searchCustomers(customerData.name)
    
  } catch (error) {
    console.error('创建客户失败:', error)
    ElMessage.error('创建客户失败')
  }
}

const submitOrder = async () => {
  if (!orderFormRef.value) return
  
  try {
    await orderFormRef.value.validate()
    
    if (orderForm.services.length === 0) {
      ElMessage.warning('请至少添加一个服务项目')
      return
    }
    
    submitting.value = true
    
    const orderData = {
      ...orderForm,
      originalAmount: originalAmount.value,
      discountAmount: discountAmount.value,
      totalAmount: totalAmount.value,
      discountType: discountType.value,
      discountValue: discountValue.value
    }
    
    console.log('提交订单:', orderData)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('订单创建成功')
    router.push('/orders')
    
  } catch (error) {
    console.error('创建订单失败:', error)
    ElMessage.error('创建订单失败')
  } finally {
    submitting.value = false
  }
}

const saveDraft = () => {
  ElMessage.info('草稿保存功能开发中...')
}

const goBack = () => {
  router.go(-1)
}

// 初始化
onMounted(() => {
  loadTechnicians()
  loadServices()
  loadCustomers() // 初始化时加载客户列表

  // 检查是否是编辑模式
  if (route.params.id) {
    isEdit.value = true
    // 加载订单数据
  }

  // 检查是否是复制订单
  if (route.query.duplicate) {
    // 加载要复制的订单数据
  }

  // 检查URL参数中是否有客户ID，如果有则自动选择该客户
  if (route.query.customerId) {
    const customerId = parseInt(route.query.customerId)
    // 延迟一下确保客户列表已加载
    setTimeout(() => {
      orderForm.customerId = customerId
      handleCustomerChange(customerId)
    }, 500)
  }
})
</script>

<style lang="scss" scoped>
.order-create {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    
    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .header-right {
    display: flex;
    gap: 12px;
  }
}

.order-form {
  .form-card {
    margin-bottom: 20px;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selected-customer {
  margin-top: 16px;
}

.empty-services {
  padding: 40px 0;
  text-align: center;
}

.summary-card {
  .order-summary {
    .summary-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      
      .label {
        color: #606266;
      }
      
      .value {
        font-weight: 500;
        
        &.amount {
          color: #E6A23C;
          font-size: 18px;
          font-weight: 600;
        }
        
        &.discount {
          color: #67C23A;
        }
      }
      
      &.total {
        font-size: 16px;
        font-weight: 600;
        padding-top: 12px;
        border-top: 1px solid #EBEEF5;
      }
    }
    
    .discount-section, .payment-section {
      h4 {
        margin: 0 0 16px 0;
        font-size: 14px;
        color: #303133;
      }
    }
  }
}

.service-selector {
  display: flex;
  height: 500px;
  
  .service-categories {
    width: 200px;
    border-right: 1px solid #EBEEF5;
    
    .el-menu {
      border-right: none;
    }
  }
  
  .service-list {
    flex: 1;
    padding: 0 20px;
    overflow-y: auto;
    
    .service-item {
      margin-bottom: 16px;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
      
      &.selected {
        border-color: #409EFF;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
      }
      
      .el-card__body {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
      }
      
      .service-info {
        flex: 1;
        
        h4 {
          margin: 0 0 8px 0;
          font-size: 16px;
          color: #303133;
        }
        
        .service-desc {
          margin: 0 0 12px 0;
          font-size: 12px;
          color: #909399;
        }
        
        .service-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .duration {
            font-size: 12px;
            color: #606266;
          }
          
          .price {
            font-size: 16px;
            font-weight: 600;
            color: #E6A23C;
          }
        }
      }
      
      .service-actions {
        margin-left: 16px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .order-form .el-row {
    flex-direction: column;
    
    .el-col {
      width: 100% !important;
      margin-bottom: 20px;
    }
  }
  
  .summary-card {
    position: static !important;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .service-selector {
    flex-direction: column;
    height: auto;
    
    .service-categories {
      width: 100%;
      border-right: none;
      border-bottom: 1px solid #EBEEF5;
      
      .el-menu {
        display: flex;
        overflow-x: auto;
      }
    }
    
    .service-list {
      padding: 20px 0;
      
      .el-col {
        width: 100% !important;
      }
    }
  }
}
</style>
