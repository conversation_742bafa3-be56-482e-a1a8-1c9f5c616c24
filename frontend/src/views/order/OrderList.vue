<template>
  <div class="order-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>订单管理</h1>
      <p>管理所有订单信息，包括订单状态、支付信息和服务记录</p>
    </div>

    <!-- 搜索和操作栏 -->
    <div class="toolbar">
      <div class="search-section">
        <el-input
          v-model="searchForm.keyword"
          placeholder="搜索订单号、客户姓名、手机号"
          style="width: 300px"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="searchForm.status"
          placeholder="订单状态"
          style="width: 150px; margin-left: 10px"
          clearable
        >
          <el-option label="待支付" value="pending" />
          <el-option label="已支付" value="paid" />
          <el-option label="服务中" value="in_service" />
          <el-option label="已完成" value="completed" />
          <el-option label="已取消" value="cancelled" />
          <el-option label="已退款" value="refunded" />
        </el-select>
        
        <el-select
          v-model="searchForm.storeId"
          placeholder="门店"
          style="width: 150px; margin-left: 10px"
          clearable
        >
          <el-option 
            v-for="store in stores" 
            :key="store.id" 
            :label="store.name" 
            :value="store.id" 
          />
        </el-select>
        
        <el-date-picker
          v-model="searchForm.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="margin-left: 10px"
          @change="handleDateRangeChange"
        />
        
        <el-button type="primary" @click="handleSearch" style="margin-left: 10px">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        
        <el-button @click="resetSearch">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </div>
      
      <div class="action-section">
        <el-button type="primary" @click="createOrder">
          <el-icon><Plus /></el-icon>
          新增订单
        </el-button>
        
        <el-button @click="exportOrders">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
        
        <el-dropdown @command="handleBatchCommand">
          <el-button>
            <el-icon><More /></el-icon>
            批量操作
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="batchCancel">批量取消</el-dropdown-item>
              <el-dropdown-item command="batchRefund">批量退款</el-dropdown-item>
              <el-dropdown-item command="batchExport">批量导出</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ stats.totalOrders }}</div>
              <div class="stat-label">总订单数</div>
            </div>
            <div class="stat-icon">
              <el-icon><Document /></el-icon>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ stats.totalAmount.toFixed(2) }}</div>
              <div class="stat-label">总金额</div>
            </div>
            <div class="stat-icon">
              <el-icon><Money /></el-icon>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ stats.todayOrders }}</div>
              <div class="stat-label">今日订单</div>
            </div>
            <div class="stat-icon">
              <el-icon><Calendar /></el-icon>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ stats.todayAmount.toFixed(2) }}</div>
              <div class="stat-label">今日金额</div>
            </div>
            <div class="stat-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 订单列表表格 -->
    <el-table
      :data="orderList"
      v-loading="loading"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      
      <el-table-column prop="orderNo" label="订单号" width="180" fixed="left">
        <template #default="scope">
          <el-button type="primary" link @click="viewOrder(scope.row)">
            {{ scope.row.orderNo }}
          </el-button>
        </template>
      </el-table-column>
      
      <el-table-column prop="customer" label="客户信息" width="150">
        <template #default="scope">
          <div class="customer-info">
            <div class="customer-name">{{ scope.row.customer?.name }}</div>
            <div class="customer-phone">{{ scope.row.customer?.phone }}</div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="store" label="门店" width="120">
        <template #default="scope">
          {{ scope.row.store?.name }}
        </template>
      </el-table-column>
      
      <el-table-column prop="services" label="服务项目" min-width="200">
        <template #default="scope">
          <el-tag 
            v-for="service in scope.row.services" 
            :key="service.id" 
            class="service-tag"
            size="small"
          >
            {{ service.name }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="totalAmount" label="订单金额" width="120" align="right">
        <template #default="scope">
          <span class="amount">¥{{ scope.row.totalAmount.toFixed(2) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="paymentMethod" label="支付方式" width="100" align="center">
        <template #default="scope">
          <el-tag :type="getPaymentMethodType(scope.row.paymentMethod)" size="small">
            {{ getPaymentMethodText(scope.row.paymentMethod) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="订单状态" width="100" align="center">
        <template #default="scope">
          <el-tag :type="getOrderStatusType(scope.row.status)" size="small">
            {{ getOrderStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="createdAt" label="下单时间" width="160">
        <template #default="scope">
          {{ formatDateTime(scope.row.createdAt) }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button type="primary" size="small" @click="viewOrder(scope.row)">
            查看
          </el-button>
          <el-button 
            type="warning" 
            size="small" 
            @click="editOrder(scope.row)"
            :disabled="!canEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-dropdown @command="(command) => handleOrderCommand(command, scope.row)">
            <el-button size="small">
              更多
              <el-icon><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item 
                  command="cancel" 
                  :disabled="!canCancel(scope.row)"
                >
                  取消订单
                </el-dropdown-item>
                <el-dropdown-item 
                  command="refund" 
                  :disabled="!canRefund(scope.row)"
                >
                  申请退款
                </el-dropdown-item>
                <el-dropdown-item command="print">打印订单</el-dropdown-item>
                <el-dropdown-item command="duplicate">复制订单</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 导出对话框 -->
    <el-dialog
      v-model="showExportDialog"
      title="导出订单数据"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form :model="exportForm" label-width="80px">
        <el-form-item label="导出格式">
          <el-radio-group v-model="exportForm.format">
            <el-radio label="excel">Excel (.csv)</el-radio>
            <el-radio label="json">JSON</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="导出字段">
          <el-checkbox-group v-model="exportForm.fields">
            <el-checkbox label="orderNo">订单号</el-checkbox>
            <el-checkbox label="customerName">客户姓名</el-checkbox>
            <el-checkbox label="storeName">门店</el-checkbox>
            <el-checkbox label="services">服务项目</el-checkbox>
            <el-checkbox label="totalAmount">订单金额</el-checkbox>
            <el-checkbox label="paymentMethod">支付方式</el-checkbox>
            <el-checkbox label="status">订单状态</el-checkbox>
            <el-checkbox label="createdAt">下单时间</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showExportDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmExport" :loading="exportLoading">
            导出
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, Refresh, Plus, Download, More, Document, Money, 
  Calendar, TrendCharts, ArrowDown 
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const orderList = ref([])
const selectedOrders = ref([])
const stores = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  storeId: null,
  dateRange: null,
  startDate: '',
  endDate: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计数据
const stats = reactive({
  totalOrders: 0,
  totalAmount: 0,
  todayOrders: 0,
  todayAmount: 0
})

// 计算属性
const getOrderStatusType = (status) => {
  const types = {
    pending: 'warning',
    paid: 'success',
    in_service: 'primary',
    completed: 'success',
    cancelled: 'info',
    refunded: 'danger'
  }
  return types[status] || 'info'
}

const getOrderStatusText = (status) => {
  const texts = {
    pending: '待支付',
    paid: '已支付',
    in_service: '服务中',
    completed: '已完成',
    cancelled: '已取消',
    refunded: '已退款'
  }
  return texts[status] || '未知'
}

const getPaymentMethodType = (method) => {
  const types = {
    cash: 'success',
    alipay: 'primary',
    wechat: 'success',
    card: 'warning',
    member: 'danger'
  }
  return types[method] || 'info'
}

const getPaymentMethodText = (method) => {
  const texts = {
    cash: '现金',
    alipay: '支付宝',
    wechat: '微信',
    card: '刷卡',
    member: '会员卡'
  }
  return texts[method] || '未知'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待支付',
    paid: '已支付',
    in_service: '服务中',
    completed: '已完成',
    cancelled: '已取消',
    refunded: '已退款'
  }
  return texts[status] || status
}

const canEdit = (order) => {
  return ['pending', 'paid'].includes(order.status)
}

const canCancel = (order) => {
  return ['pending', 'paid'].includes(order.status)
}

const canRefund = (order) => {
  return ['paid', 'completed'].includes(order.status)
}

// 方法
const formatDateTime = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString()
}

const loadOrders = async () => {
  loading.value = true
  try {
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 500))
    
    orderList.value = [
      {
        id: 1,
        orderNo: 'ORD202401150001',
        customer: {
          id: 1,
          name: '张三',
          phone: '13800138001'
        },
        store: {
          id: 1,
          name: '朝阳店'
        },
        services: [
          { id: 1, name: '全身按摩' },
          { id: 2, name: '足疗' }
        ],
        totalAmount: 298.00,
        paymentMethod: 'wechat',
        status: 'completed',
        createdAt: '2024-01-15 14:30:00'
      },
      {
        id: 2,
        orderNo: 'ORD202401150002',
        customer: {
          id: 2,
          name: '李四',
          phone: '13800138002'
        },
        store: {
          id: 1,
          name: '朝阳店'
        },
        services: [
          { id: 3, name: '肩颈按摩' }
        ],
        totalAmount: 128.00,
        paymentMethod: 'alipay',
        status: 'paid',
        createdAt: '2024-01-15 16:20:00'
      }
    ]
    
    pagination.total = 2
    
    // 更新统计数据
    stats.totalOrders = 2
    stats.totalAmount = 426.00
    stats.todayOrders = 2
    stats.todayAmount = 426.00
    
  } catch (error) {
    console.error('加载订单列表失败:', error)
    ElMessage.error('加载订单列表失败')
  } finally {
    loading.value = false
  }
}

const loadStores = async () => {
  try {
    // 模拟门店数据
    stores.value = [
      { id: 1, name: '朝阳店' },
      { id: 2, name: '海淀店' },
      { id: 3, name: '西城店' }
    ]
  } catch (error) {
    console.error('加载门店列表失败:', error)
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadOrders()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: '',
    storeId: null,
    dateRange: null,
    startDate: '',
    endDate: ''
  })
  handleSearch()
}

const handleDateRangeChange = (dates) => {
  if (dates && dates.length === 2) {
    searchForm.startDate = dates[0]
    searchForm.endDate = dates[1]
  } else {
    searchForm.startDate = ''
    searchForm.endDate = ''
  }
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadOrders()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadOrders()
}

const handleSelectionChange = (selection) => {
  selectedOrders.value = selection
}

const createOrder = () => {
  router.push('/orders/create')
}

const viewOrder = (order) => {
  router.push(`/orders/${order.id}`)
}

const editOrder = (order) => {
  router.push(`/orders/${order.id}/edit`)
}

const handleOrderCommand = async (command, order) => {
  switch (command) {
    case 'cancel':
      await cancelOrder(order)
      break
    case 'refund':
      await refundOrder(order)
      break
    case 'print':
      printOrder(order)
      break
    case 'duplicate':
      duplicateOrder(order)
      break
  }
}

const cancelOrder = async (order) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消订单 "${order.orderNo}" 吗？`,
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    console.log('取消订单:', order)
    ElMessage.success('订单取消成功')
    loadOrders()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消订单失败:', error)
      ElMessage.error('取消订单失败')
    }
  }
}

const refundOrder = async (order) => {
  try {
    await ElMessageBox.confirm(
      `确定要申请退款订单 "${order.orderNo}" 吗？`,
      '确认退款',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    console.log('申请退款:', order)
    ElMessage.success('退款申请已提交')
    loadOrders()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('申请退款失败:', error)
      ElMessage.error('申请退款失败')
    }
  }
}

const printOrder = (order) => {
  ElMessage.info('打印功能开发中...')
}

const duplicateOrder = (order) => {
  router.push(`/orders/create?duplicate=${order.id}`)
}

const handleBatchCommand = (command) => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请先选择要操作的订单')
    return
  }
  
  switch (command) {
    case 'batchCancel':
      ElMessage.info('批量取消功能开发中...')
      break
    case 'batchRefund':
      ElMessage.info('批量退款功能开发中...')
      break
    case 'batchExport':
      ElMessage.info('批量导出功能开发中...')
      break
  }
}

const exportOrders = () => {
  showExportDialog.value = true
}

// 导出相关
const showExportDialog = ref(false)
const exportForm = reactive({
  format: 'excel',
  fields: ['orderNo', 'customerName', 'storeName', 'services', 'totalAmount', 'paymentMethod', 'status', 'createdAt']
})
const exportLoading = ref(false)

// 确认导出
const confirmExport = async () => {
  try {
    exportLoading.value = true

    // 准备导出数据
    const exportData = orderList.value.map(order => ({
      订单号: order.orderNo,
      客户姓名: order.customer?.name || '散客',
      客户手机: order.customer?.phone || '',
      门店: order.store?.name || '',
      服务项目: order.services?.map(s => s.name).join(', ') || '',
      订单金额: `¥${order.totalAmount.toFixed(2)}`,
      支付方式: getPaymentMethodText(order.paymentMethod),
      订单状态: getStatusText(order.status),
      下单时间: order.createdAt
    }))

    if (exportForm.format === 'excel') {
      // 导出Excel
      const headers = [
        { label: '订单号', key: '订单号' },
        { label: '客户姓名', key: '客户姓名' },
        { label: '客户手机', key: '客户手机' },
        { label: '门店', key: '门店' },
        { label: '服务项目', key: '服务项目' },
        { label: '订单金额', key: '订单金额' },
        { label: '支付方式', key: '支付方式' },
        { label: '订单状态', key: '订单状态' },
        { label: '下单时间', key: '下单时间' }
      ]

      // 简单的CSV导出实现
      const csvContent = [
        headers.map(h => h.label).join(','),
        ...exportData.map(row => headers.map(h => row[h.key]).join(','))
      ].join('\n')

      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `订单列表_${new Date().toISOString().slice(0, 10)}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      ElMessage.success('订单数据导出成功')
    } else {
      // 导出JSON
      const jsonData = JSON.stringify(exportData, null, 2)
      const blob = new Blob([jsonData], { type: 'application/json' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `订单列表_${new Date().toISOString().slice(0, 10)}.json`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      ElMessage.success('订单数据导出成功')
    }

    showExportDialog.value = false

  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 初始化
onMounted(() => {
  loadStores()
  loadOrders()
})
</script>

<style lang="scss" scoped>
.order-list {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
  
  h1 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: #303133;
  }
  
  p {
    margin: 0;
    color: #909399;
    font-size: 14px;
  }
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 16px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  .search-section {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .action-section {
    display: flex;
    gap: 12px;
  }
}

.stats-cards {
  margin-bottom: 20px;
  
  .stat-card {
    .el-card__body {
      padding: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .stat-content {
      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
      }
    }
    
    .stat-icon {
      font-size: 32px;
      color: #409EFF;
      opacity: 0.8;
    }
  }
}

.customer-info {
  .customer-name {
    font-weight: 500;
    color: #303133;
  }
  
  .customer-phone {
    font-size: 12px;
    color: #909399;
    margin-top: 2px;
  }
}

.service-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}

.amount {
  font-weight: 600;
  color: #E6A23C;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .toolbar {
    flex-direction: column;
    gap: 16px;
    
    .search-section {
      width: 100%;
    }
    
    .action-section {
      width: 100%;
      justify-content: flex-end;
    }
  }
}

@media (max-width: 768px) {
  .stats-cards {
    .el-col {
      margin-bottom: 16px;
    }
  }
  
  .search-section {
    flex-direction: column;
    align-items: stretch !important;
    
    > * {
      width: 100% !important;
      margin-left: 0 !important;
    }
  }
}
</style>
