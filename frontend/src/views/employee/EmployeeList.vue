<template>
  <div class="employee-list-container">
    <div class="page-header">
      <h1>员工管理</h1>
      <el-button type="primary" @click="handleAdd">添加员工</el-button>
    </div>
    
    <!-- 搜索过滤区域 -->
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="filter-form">
        <el-form-item label="门店">
          <el-select v-model="queryParams.store_id" placeholder="选择门店" clearable>
            <el-option
              v-for="store in storeOptions"
              :key="store.id"
              :label="store.name"
              :value="store.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="职位">
          <el-select v-model="queryParams.role" placeholder="选择职位" clearable>
            <el-option label="店长" value="店长" />
            <el-option label="技师" value="技师" />
            <el-option label="助理" value="助理" />
            <el-option label="收银" value="收银" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="选择状态" clearable>
            <el-option label="在职" value="active" />
            <el-option label="休假" value="inactive" />
            <el-option label="离职" value="left" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    


    <!-- 数据加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>正在加载员工数据...</span>
    </div>

    <!-- 数据加载错误 -->
    <div v-else-if="loadError" class="error-container">
      <el-icon class="error-icon"><Warning /></el-icon>
      <div class="error-content">
        <h3>数据加载失败</h3>
        <p>{{ errorMessage }}</p>
        <el-button type="primary" @click="retryLoad">重试</el-button>
      </div>
    </div>

    <!-- 空数据状态 -->
    <div v-else-if="employeeList.length === 0" class="empty-container">
      <el-icon class="empty-icon"><User /></el-icon>
      <div class="empty-content">
        <h3>暂无员工数据</h3>
        <p>点击"添加员工"按钮创建第一个员工</p>
        <el-button type="primary" @click="handleAdd">添加员工</el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-else
      v-loading="loading"
      :data="employeeList"
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="name" label="姓名" width="120" />
      <el-table-column prop="phone" label="手机号" width="150" />
      <el-table-column prop="role" label="职位" width="100" />
      <el-table-column label="照片" width="100">
        <template #default="scope">
          <el-image
            v-if="scope.row.photo_url"
            :src="scope.row.photo_url"
            :preview-src-list="[scope.row.photo_url]"
            fit="cover"
            style="width: 40px; height: 40px; border-radius: 4px"
          />
          <el-avatar v-else :size="40" icon="el-icon-user" />
        </template>
      </el-table-column>
      <el-table-column label="技能标签" min-width="180">
        <template #default="scope">
          <el-tag
            v-for="(skill, index) in scope.row.skills || []"
            :key="index"
            size="small"
            class="skill-tag"
          >
            {{ skill }}
          </el-tag>
          <span v-if="!scope.row.skills || scope.row.skills.length === 0">无</span>
        </template>
      </el-table-column>
      <el-table-column prop="hire_date" label="入职日期" width="120" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            link
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            size="small"
            type="primary"
            link
            @click="handleView(scope.row)"
          >
            查看
          </el-button>
          <el-popconfirm
            title="确定要删除该员工吗？"
            @confirm="handleDelete(scope.row)"
          >
            <template #reference>
              <el-button size="small" type="danger" link>删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.page"
        v-model:page-size="queryParams.limit"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 添加/编辑员工对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加员工' : '编辑员工'"
      width="600px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="所属门店" prop="store_id">
          <el-select v-model="form.store_id" placeholder="选择门店">
            <el-option
              v-for="store in storeOptions"
              :key="store.id"
              :label="store.name"
              :value="store.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="职位" prop="role">
          <el-select v-model="form.role" placeholder="选择职位">
            <el-option label="店长" value="店长" />
            <el-option label="技师" value="技师" />
            <el-option label="助理" value="助理" />
            <el-option label="收银" value="收银" />
          </el-select>
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="dialogType === 'add'">
          <el-input v-model="form.password" placeholder="请输入密码" type="password" />
        </el-form-item>
        <el-form-item label="照片" prop="photo_url">
          <el-upload
            class="avatar-uploader"
            action="#"
            :http-request="() => {}"
            :show-file-list="false"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="form.photo_url" :src="form.photo_url" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="技能标签" prop="skills">
          <el-select
            v-model="form.skills"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请选择或创建技能标签"
          >
            <el-option
              v-for="item in skillOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="入职日期" prop="hire_date">
          <el-date-picker
            v-model="form.hire_date"
            type="date"
            placeholder="选择入职日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="选择状态">
            <el-option label="在职" value="active" />
            <el-option label="休假" value="inactive" />
            <el-option label="离职" value="left" />
          </el-select>
        </el-form-item>
        <el-form-item label="证件附件">
          <div class="attachment-list" v-if="attachmentList.length > 0">
            <div v-for="(item, index) in attachmentList" :key="index" class="attachment-item">
              <span class="attachment-name">{{ item.name }}</span>
              <el-button type="danger" link @click="removeAttachment(item.name)">删除</el-button>
            </div>
          </div>
          <el-button type="primary" @click="openAttachmentDialog">添加附件</el-button>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确认</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 附件上传对话框 -->
    <el-dialog
      v-model="attachmentDialogVisible"
      title="上传证件附件"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="attachmentFormRef"
        :model="attachmentForm"
        :rules="attachmentRules"
        label-width="100px"
      >
        <el-form-item label="附件名称" prop="name">
          <el-input v-model="attachmentForm.name" placeholder="如：健康证、资格证" />
        </el-form-item>
        <el-form-item label="选择文件" prop="file">
          <el-upload
            class="attachment-uploader"
            action="#"
            :http-request="() => {}"
            :on-change="handleAttachmentFileChange"
            :auto-upload="false"
            :limit="1"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持jpg/png/pdf等格式，文件大小不超过10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="attachmentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAttachmentForm">上传</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Loading, Warning, User } from '@element-plus/icons-vue'
import employeeApi from '@/api/employee'
import storeApi from '@/api/store'
import uploadApi from '@/api/upload'

// 路由
const router = useRouter()

// 数据加载状态
const loading = ref(false)
const loadError = ref(false)
const errorMessage = ref('')

// 员工列表数据
const employeeList = ref([])
const total = ref(0)

// 门店选项
const storeOptions = ref([])

// 技能标签选项
const skillOptions = ref([
  '足疗', '推拿', '按摩', '刮痧', '拔罐', '小儿推拿', '精油SPA', '头疗', '采耳', '修脚'
])

// 查询参数
const queryParams = reactive({
  page: 1,
  limit: 10,
  store_id: null,
  role: null,
  status: null
})

// 表单数据
const dialogVisible = ref(false)
const dialogType = ref('add') // add 或 edit
const formRef = ref(null)
const form = reactive({
  store_id: null,
  name: '',
  phone: '',
  role: '',
  password: '',
  photo_url: '',
  skills: [],
  attachments: {},
  hire_date: '',
  status: 'active'
})

// 表单验证规则
const rules = {
  store_id: [{ required: true, message: '请选择所属门店', trigger: 'change' }],
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  role: [{ required: true, message: '请选择职位', trigger: 'change' }],
  password: [
    { required: dialogType.value === 'add', message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 附件上传对话框
const attachmentDialogVisible = ref(false)
const attachmentForm = reactive({
  name: '',
  file: null
})
const attachmentRules = {
  name: [{ required: true, message: '请输入附件名称', trigger: 'blur' }],
  file: [{ required: true, message: '请选择文件', trigger: 'change' }]
}
const attachmentFormRef = ref(null)

// 生命周期钩子
onMounted(() => {
  fetchEmployeeList()
  fetchStoreOptions()
})

// 获取员工列表
const fetchEmployeeList = async () => {
  loading.value = true
  loadError.value = false
  errorMessage.value = ''

  try {
    const params = {
      skip: (queryParams.page - 1) * queryParams.limit,
      limit: queryParams.limit
    }

    // 只添加有值的参数，避免传递undefined
    if (queryParams.store_id !== undefined && queryParams.store_id !== null) {
      params.store_id = queryParams.store_id
    }
    if (queryParams.role !== undefined && queryParams.role !== null && queryParams.role !== '') {
      params.role = queryParams.role
    }
    if (queryParams.status !== undefined && queryParams.status !== null && queryParams.status !== '') {
      params.status = queryParams.status
    }

    const res = await employeeApi.getEmployeeList(params)

    // 确保响应是数组格式
    if (Array.isArray(res)) {
      employeeList.value = res
      total.value = res.length
    } else if (res && res.items && Array.isArray(res.items)) {
      // 如果是分页格式 {items: [], total: number}
      employeeList.value = res.items
      total.value = res.total || res.items.length
    } else {
      console.error('API响应格式不正确:', res)
      employeeList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取员工列表失败', error)
    loadError.value = true
    errorMessage.value = error.response?.data?.detail || error.message || '网络连接失败，请检查网络设置'
    ElMessage.error('获取员工列表失败')
  } finally {
    loading.value = false
  }
}

// 重试加载
const retryLoad = () => {
  fetchEmployeeList()
}

// 获取门店选项
const fetchStoreOptions = async () => {
  try {
    const res = await storeApi.getStoreList()
    storeOptions.value = res
  } catch (error) {
    console.error('获取门店列表失败', error)
  }
}

// 搜索
const handleSearch = () => {
  queryParams.page = 1
  fetchEmployeeList()
}

// 重置查询
const resetQuery = () => {
  queryParams.store_id = null
  queryParams.role = null
  queryParams.status = null
  queryParams.page = 1
  fetchEmployeeList()
}

// 添加员工
const handleAdd = () => {
  dialogType.value = 'add'
  resetForm()
  dialogVisible.value = true
}

// 编辑员工
const handleEdit = (row) => {
  dialogType.value = 'edit'
  resetForm()
  Object.assign(form, row)
  form.password = '' // 清空密码
  dialogVisible.value = true
}

// 查看员工详情
const handleView = (row) => {
  router.push(`/employees/${row.id}`)
}

// 删除员工
const handleDelete = async (row) => {
  try {
    await employeeApi.deleteEmployee(row.id)
    ElMessage.success('删除成功')
    fetchEmployeeList()
  } catch (error) {
    console.error('删除员工失败', error)
    ElMessage.error('删除员工失败')
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  Object.assign(form, {
    store_id: null,
    name: '',
    phone: '',
    role: '',
    password: '',
    photo_url: '',
    skills: [],
    attachments: {},
    hire_date: '',
    status: 'active'
  })
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 准备提交数据，处理空值
        const submitData = { ...form }

        // 如果hire_date为空，设置为null或删除该字段
        if (!submitData.hire_date || submitData.hire_date === '') {
          submitData.hire_date = null
        }

        // 确保skills是数组
        if (!Array.isArray(submitData.skills)) {
          submitData.skills = []
        }

        // 确保attachments是对象
        if (!submitData.attachments || typeof submitData.attachments !== 'object') {
          submitData.attachments = {}
        }

        if (dialogType.value === 'add') {
          await employeeApi.createEmployee(submitData)
          ElMessage.success('添加成功')
        } else {
          await employeeApi.updateEmployee(submitData.id, submitData)
          ElMessage.success('更新成功')
        }

        dialogVisible.value = false
        fetchEmployeeList()
      } catch (error) {
        console.error('保存员工失败', error)
        const errorMsg = error.response?.data?.detail || error.message || '保存员工失败'
        ElMessage.error(errorMsg)
      }
    }
  })
}

// 处理头像上传成功
const handleAvatarSuccess = (res) => {
  form.photo_url = res.url
  ElMessage.success('上传成功')
}

// 头像上传前验证
const beforeAvatarUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2
  
  if (!isImage) {
    ElMessage.error('上传头像图片只能是图片格式!')
    return false
  }
  
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 2MB!')
    return false
  }
  
  // 使用自定义上传API
  uploadApi.uploadEmployeePhoto(file).then(res => {
    form.photo_url = res.url
    ElMessage.success('上传成功')
  }).catch(err => {
    console.error('上传失败', err)
    ElMessage.error('上传失败')
  })
  
  return false // 阻止默认上传行为
}

// 打开附件上传对话框
const openAttachmentDialog = () => {
  attachmentForm.name = ''
  attachmentForm.file = null
  attachmentDialogVisible.value = true
}

// 处理附件文件选择
const handleAttachmentFileChange = (file) => {
  attachmentForm.file = file.raw
}

// 提交附件表单
const submitAttachmentForm = async () => {
  if (!attachmentFormRef.value) return
  
  await attachmentFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const res = await uploadApi.uploadEmployeeAttachment(attachmentForm.file)
        
        // 更新表单附件对象
        form.attachments = {
          ...form.attachments,
          [attachmentForm.name]: res.url
        }
        
        attachmentDialogVisible.value = false
        ElMessage.success('附件上传成功')
      } catch (error) {
        console.error('上传附件失败', error)
        ElMessage.error('上传附件失败')
      }
    }
  })
}

// 删除附件
const removeAttachment = (key) => {
  ElMessageBox.confirm('确定要删除该附件吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const newAttachments = { ...form.attachments }
    delete newAttachments[key]
    form.attachments = newAttachments
    ElMessage.success('附件已删除')
  }).catch(() => {})
}

// 计算附件列表
const attachmentList = computed(() => {
  return Object.entries(form.attachments || {}).map(([name, url]) => ({
    name,
    url
  }))
})

// 分页相关方法
const handleSizeChange = (val) => {
  queryParams.limit = val
  fetchEmployeeList()
}

const handleCurrentChange = (val) => {
  queryParams.page = val
  fetchEmployeeList()
}

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'active':
      return 'success'
    case 'inactive':
      return 'warning'
    case 'left':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'active':
      return '在职'
    case 'inactive':
      return '休假'
    case 'left':
      return '离职'
    default:
      return '未知'
  }
}
</script>

<style scoped>
.employee-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-container {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

/* 修复下拉框样式 */
.filter-form .el-form-item {
  margin-bottom: 10px;
}

.filter-form .el-select {
  min-width: 120px;
  width: 100%;
}

.filter-form .el-select .el-input {
  min-width: 120px;
}

.filter-form .el-select .el-input__wrapper {
  min-width: 120px;
  padding: 8px 12px;
}

/* 确保下拉框文本可见 */
.filter-form .el-select .el-input__inner {
  min-width: 100px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.skill-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

.avatar-uploader {
  display: flex;
  justify-content: center;
}

.avatar-uploader .avatar {
  width: 100px;
  height: 100px;
  display: block;
  object-fit: cover;
  border-radius: 4px;
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  text-align: center;
  line-height: 100px;
}

.attachment-list {
  margin-bottom: 10px;
}

.attachment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.attachment-name {
  font-size: 14px;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #909399;
}

.loading-container .el-icon {
  font-size: 32px;
  margin-bottom: 16px;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.error-icon {
  font-size: 48px;
  color: #F56C6C;
  margin-bottom: 16px;
}

.error-content h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
}

.error-content p {
  margin: 0 0 20px 0;
  color: #606266;
  font-size: 14px;
}

/* 空数据状态样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  color: #C0C4CC;
  margin-bottom: 16px;
}

.empty-content h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
}

.empty-content p {
  margin: 0 0 20px 0;
  color: #606266;
  font-size: 14px;
}
</style>