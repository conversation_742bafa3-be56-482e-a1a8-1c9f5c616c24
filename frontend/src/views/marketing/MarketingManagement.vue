<template>
  <div class="marketing-management-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>营销管理</h1>
      <p>全面的营销活动管理，支持多门店协同营销和精准客户营销</p>
    </div>

    <!-- 营销概览统计 -->
    <el-row :gutter="20" class="marketing-stats">
      <el-col :span="6">
        <el-card shadow="never" class="stat-card">
          <div class="stat-number">{{ marketingStats.activeActivities }}</div>
          <div class="stat-label">进行中活动</div>
          <div class="stat-trend">
            <el-icon class="trend-icon up"><TrendCharts /></el-icon>
            <span class="trend-text">+12%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="never" class="stat-card">
          <div class="stat-number">{{ marketingStats.totalCoupons }}</div>
          <div class="stat-label">优惠券总数</div>
          <div class="stat-trend">
            <el-icon class="trend-icon up"><TrendCharts /></el-icon>
            <span class="trend-text">+8%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="never" class="stat-card">
          <div class="stat-number">{{ marketingStats.memberCards }}</div>
          <div class="stat-label">会员卡类型</div>
          <div class="stat-trend">
            <el-icon class="trend-icon up"><TrendCharts /></el-icon>
            <span class="trend-text">+5%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="never" class="stat-card">
          <div class="stat-number">{{ marketingStats.conversionRate }}%</div>
          <div class="stat-label">营销转化率</div>
          <div class="stat-trend">
            <el-icon class="trend-icon up"><TrendCharts /></el-icon>
            <span class="trend-text">+15%</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能模块导航 -->
    <div class="module-navigation">
      <h2>营销功能模块</h2>
      <el-row :gutter="20" class="module-cards">
        <!-- 营销活动管理 -->
        <el-col :span="8">
          <el-card shadow="hover" class="module-card" @click="navigateTo('/marketing/activities')">
            <div class="module-icon">
              <el-icon size="48"><Present /></el-icon>
            </div>
            <div class="module-content">
              <h3>营销活动</h3>
              <p>创建和管理各类营销活动，支持多门店同步推广</p>
              <div class="module-stats">
                <span>进行中: {{ marketingStats.activeActivities }}</span>
                <span>已完成: {{ marketingStats.completedActivities }}</span>
              </div>
            </div>
            <div class="module-action">
              <el-button type="primary" size="small">管理活动</el-button>
            </div>
          </el-card>
        </el-col>

        <!-- 优惠券管理 -->
        <el-col :span="8">
          <el-card shadow="hover" class="module-card" @click="navigateTo('/marketing/coupons')">
            <div class="module-icon">
              <el-icon size="48"><Ticket /></el-icon>
            </div>
            <div class="module-content">
              <h3>优惠券管理</h3>
              <p>创建优惠券模板，管理优惠券发放和使用情况</p>
              <div class="module-stats">
                <span>已发放: {{ marketingStats.issuedCoupons }}</span>
                <span>已使用: {{ marketingStats.usedCoupons }}</span>
              </div>
            </div>
            <div class="module-action">
              <el-button type="primary" size="small">管理优惠券</el-button>
            </div>
          </el-card>
        </el-col>

        <!-- 会员卡管理 -->
        <el-col :span="8">
          <el-card shadow="hover" class="module-card" @click="navigateTo('/marketing/member-cards')">
            <div class="module-icon">
              <el-icon size="48"><CreditCard /></el-icon>
            </div>
            <div class="module-content">
              <h3>会员卡管理</h3>
              <p>设计会员卡类型，配置会员权益和积分规则</p>
              <div class="module-stats">
                <span>卡类型: {{ marketingStats.memberCards }}</span>
                <span>活跃会员: {{ marketingStats.activeMembers }}</span>
              </div>
            </div>
            <div class="module-action">
              <el-button type="primary" size="small">管理会员卡</el-button>
            </div>
          </el-card>
        </el-col>

        <!-- 营销自动化 -->
        <el-col :span="8">
          <el-card shadow="hover" class="module-card" @click="navigateTo('/marketing/automation/rules')">
            <div class="module-icon">
              <el-icon size="48"><Setting /></el-icon>
            </div>
            <div class="module-content">
              <h3>营销自动化</h3>
              <p>设置自动化营销规则，智能触发营销活动</p>
              <div class="module-stats">
                <span>规则数: {{ marketingStats.automationRules }}</span>
                <span>触发次数: {{ marketingStats.automationTriggers }}</span>
              </div>
            </div>
            <div class="module-action">
              <el-button type="primary" size="small">管理规则</el-button>
            </div>
          </el-card>
        </el-col>

        <!-- 客户关怀 -->
        <el-col :span="8">
          <el-card shadow="hover" class="module-card" @click="navigateTo('/marketing/retention')">
            <div class="module-icon">
              <el-icon size="48"><User /></el-icon>
            </div>
            <div class="module-content">
              <h3>客户关怀</h3>
              <p>流失客户唤醒、生日关怀、消费激励等客户关怀功能</p>
              <div class="module-stats">
                <span>关怀计划: {{ marketingStats.carePlans }}</span>
                <span>成功唤醒: {{ marketingStats.reactivatedCustomers }}</span>
              </div>
            </div>
            <div class="module-action">
              <el-button type="primary" size="small">客户关怀</el-button>
            </div>
          </el-card>
        </el-col>

        <!-- 精准营销 -->
        <el-col :span="8">
          <el-card shadow="hover" class="module-card" @click="showPrecisionMarketing">
            <div class="module-icon">
              <el-icon size="48"><Aim /></el-icon>
            </div>
            <div class="module-content">
              <h3>精准营销</h3>
              <p>基于客户标签和行为数据进行精准营销推送</p>
              <div class="module-stats">
                <span>标签数: {{ marketingStats.customerTags }}</span>
                <span>推送成功率: {{ marketingStats.pushSuccessRate }}%</span>
              </div>
            </div>
            <div class="module-action">
              <el-button type="primary" size="small">精准营销</el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最近营销活动 -->
    <div class="recent-activities">
      <div class="section-header">
        <h2>最近营销活动</h2>
        <div class="header-actions">
          <el-button @click="refreshActivities">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button type="primary" @click="navigateTo('/marketing/activities')">
            查看全部
          </el-button>
        </div>
      </div>

      <el-table :data="recentActivities" v-loading="activitiesLoading" stripe>
        <el-table-column prop="name" label="活动名称" min-width="200" />
        <el-table-column prop="type" label="活动类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getActivityTypeColor(row.type)">
              {{ getActivityTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="start_date" label="开始时间" width="120">
          <template #default="{ row }">
            {{ formatDate(row.start_date) }}
          </template>
        </el-table-column>
        <el-table-column prop="end_date" label="结束时间" width="120">
          <template #default="{ row }">
            {{ formatDate(row.end_date) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getActivityStatusColor(row.status)">
              {{ getActivityStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="participation_count" label="参与人数" width="100" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewActivityDetail(row)">详情</el-button>
            <el-button size="small" type="primary" @click="editActivity(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 营销效果分析 -->
    <div class="marketing-analysis">
      <div class="section-header">
        <h2>营销效果分析</h2>
        <div class="header-actions">
          <el-select v-model="analysisTimeRange" @change="updateAnalysis" style="width: 150px;">
            <el-option label="最近7天" value="7days" />
            <el-option label="最近30天" value="30days" />
            <el-option label="最近3个月" value="3months" />
          </el-select>
        </div>
      </div>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="never" class="analysis-card">
            <template #header>
              <span>营销活动效果</span>
            </template>
            <div class="chart-container">
              <div class="chart-placeholder">
                <el-icon size="64"><TrendCharts /></el-icon>
                <p>营销活动效果图表</p>
                <p class="chart-note">显示不同营销活动的参与度和转化率</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="never" class="analysis-card">
            <template #header>
              <span>客户获取渠道</span>
            </template>
            <div class="chart-container">
              <div class="chart-placeholder">
                <el-icon size="64"><PieChart /></el-icon>
                <p>客户获取渠道分析</p>
                <p class="chart-note">显示不同营销渠道的客户获取效果</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 精准营销对话框 -->
    <el-dialog
      v-model="precisionMarketingVisible"
      title="精准营销"
      width="800px"
      destroy-on-close
    >
      <div class="precision-marketing-content">
        <h3>基于客户标签的精准营销</h3>
        <p>选择目标客户群体，创建个性化营销活动</p>
        
        <el-form :model="precisionForm" label-width="120px">
          <el-form-item label="目标客户标签">
            <el-select v-model="precisionForm.targetTags" multiple placeholder="选择客户标签" style="width: 100%">
              <el-option v-for="tag in customerTags" :key="tag.value" :label="tag.label" :value="tag.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="营销内容">
            <el-input v-model="precisionForm.content" type="textarea" :rows="4" placeholder="输入营销内容" />
          </el-form-item>
          <el-form-item label="推送时间">
            <el-date-picker
              v-model="precisionForm.pushTime"
              type="datetime"
              placeholder="选择推送时间"
              style="width: 100%"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="precisionMarketingVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPrecisionMarketing">发送营销</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Present, Ticket, CreditCard, Setting, User, Aim, 
  TrendCharts, PieChart, Refresh 
} from '@element-plus/icons-vue'

// 路由
const router = useRouter()

// 响应式数据
const activitiesLoading = ref(false)
const analysisTimeRange = ref('30days')
const precisionMarketingVisible = ref(false)

// 营销统计数据
const marketingStats = reactive({
  activeActivities: 8,
  totalCoupons: 156,
  memberCards: 5,
  conversionRate: 23.5,
  completedActivities: 24,
  issuedCoupons: 1250,
  usedCoupons: 890,
  activeMembers: 3420,
  automationRules: 12,
  automationTriggers: 2340,
  carePlans: 6,
  reactivatedCustomers: 145,
  customerTags: 28,
  pushSuccessRate: 87.5
})

// 最近营销活动
const recentActivities = ref([
  {
    id: 1,
    name: '春季养生套餐优惠',
    type: 'discount',
    start_date: '2025-07-20',
    end_date: '2025-08-20',
    status: 'active',
    participation_count: 156
  },
  {
    id: 2,
    name: '新客户体验活动',
    type: 'experience',
    start_date: '2025-07-15',
    end_date: '2025-07-30',
    status: 'active',
    participation_count: 89
  },
  {
    id: 3,
    name: '会员积分双倍活动',
    type: 'points',
    start_date: '2025-07-10',
    end_date: '2025-07-25',
    status: 'completed',
    participation_count: 234
  }
])

// 精准营销表单
const precisionForm = reactive({
  targetTags: [],
  content: '',
  pushTime: null
})

// 客户标签选项
const customerTags = ref([
  { label: 'VIP客户', value: 'vip' },
  { label: '高消费客户', value: 'high_value' },
  { label: '新客户', value: 'new_customer' },
  { label: '流失客户', value: 'churned' },
  { label: '活跃客户', value: 'active' }
])

// 生命周期
onMounted(() => {
  // 初始化数据
})

// 方法
const navigateTo = (path) => {
  router.push(path)
}

const refreshActivities = () => {
  activitiesLoading.value = true
  // 模拟刷新
  setTimeout(() => {
    activitiesLoading.value = false
    ElMessage.success('数据已刷新')
  }, 1000)
}

const updateAnalysis = () => {
  ElMessage.info('正在更新分析数据...')
}

const showPrecisionMarketing = () => {
  precisionMarketingVisible.value = true
}

const submitPrecisionMarketing = () => {
  if (!precisionForm.targetTags.length) {
    ElMessage.warning('请选择目标客户标签')
    return
  }
  if (!precisionForm.content) {
    ElMessage.warning('请输入营销内容')
    return
  }
  
  ElMessage.success('精准营销已发送')
  precisionMarketingVisible.value = false
}

const viewActivityDetail = (row) => {
  ElMessage.info(`查看活动详情: ${row.name}`)
}

const editActivity = (row) => {
  ElMessage.info(`编辑活动: ${row.name}`)
}

// 工具方法
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}/${date.getDate()}`
}

const getActivityTypeColor = (type) => {
  const colorMap = {
    'discount': 'success',
    'experience': 'primary',
    'points': 'warning'
  }
  return colorMap[type] || 'info'
}

const getActivityTypeText = (type) => {
  const textMap = {
    'discount': '优惠活动',
    'experience': '体验活动',
    'points': '积分活动'
  }
  return textMap[type] || '其他'
}

const getActivityStatusColor = (status) => {
  const colorMap = {
    'active': 'success',
    'completed': 'info',
    'pending': 'warning',
    'cancelled': 'danger'
  }
  return colorMap[status] || 'info'
}

const getActivityStatusText = (status) => {
  const textMap = {
    'active': '进行中',
    'completed': '已完成',
    'pending': '待开始',
    'cancelled': '已取消'
  }
  return textMap[status] || '未知'
}
</script>

<style scoped>
.marketing-management-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 16px;
}

.marketing-stats {
  margin-bottom: 30px;
}

.stat-card {
  text-align: center;
  padding: 20px;
  position: relative;
}

.stat-number {
  font-size: 32px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
  margin-bottom: 8px;
}

.stat-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.trend-icon.up {
  color: #67c23a;
}

.trend-text {
  font-size: 12px;
  color: #67c23a;
}

.module-navigation {
  margin-bottom: 30px;
}

.module-navigation h2 {
  margin: 0 0 20px 0;
  font-size: 20px;
  color: #303133;
}

.module-cards {
  margin-bottom: 20px;
}

.module-card {
  cursor: pointer;
  transition: all 0.3s;
  height: 200px;
  display: flex;
  flex-direction: column;
}

.module-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.module-icon {
  text-align: center;
  color: #409eff;
  margin-bottom: 15px;
}

.module-content {
  flex: 1;
}

.module-content h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #303133;
}

.module-content p {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
}

.module-stats {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #909399;
}

.module-action {
  text-align: center;
  margin-top: 15px;
}

.recent-activities {
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  margin: 0;
  font-size: 20px;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.marketing-analysis {
  margin-bottom: 30px;
}

.analysis-card {
  height: 300px;
}

.chart-container {
  height: 220px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #909399;
}

.chart-placeholder p {
  margin: 10px 0 5px 0;
  font-size: 16px;
}

.chart-note {
  font-size: 12px !important;
  color: #c0c4cc !important;
}

.precision-marketing-content h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.precision-marketing-content p {
  margin: 0 0 20px 0;
  color: #606266;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-card__body) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-table th) {
  background-color: #fafafa;
}
</style>
