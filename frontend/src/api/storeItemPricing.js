import request from '@/utils/request'

// 获取门店项目及其价格信息
export function getStoreItemsWithPricing(storeId) {
  return request({
    url: `/store-item-pricing/stores/${storeId}/items`,
    method: 'get'
  })
}

// 更新门店项目价格
export function updateStoreItemPricing(itemId, data) {
  return request({
    url: `/store-item-pricing/items/${itemId}/pricing`,
    method: 'put',
    data
  })
}

// 移除门店项目的会员价
export function removeMemberPrice(itemId) {
  return request({
    url: `/store-item-pricing/items/${itemId}/member-price`,
    method: 'delete'
  })
}

export default {
  getStoreItemsWithPricing,
  updateStoreItemPricing,
  removeMemberPrice
}
