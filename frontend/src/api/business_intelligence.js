import request from './request'

// 获取多维度分析报表数据
export function getMultiDimensionalAnalysis(params) {
  return request.post('/bi/multidimensional-analysis', params)
}

// 获取经营指标分析数据
export function getBusinessIndicators(params) {
  return request.post('/bi/business-indicators', params)
}

// 获取趋势预测数据
export function getTrendPrediction(params) {
  return request.post('/bi/trend-prediction', params)
}

// 获取报表模板列表
export function getReportTemplates(params) {
  return request.get('/business_intelligence/report_templates', params)
}

// 获取报表模板详情
export function getReportTemplateDetail(id) {
  return request.get(`/business_intelligence/report_templates/${id}`)
}

// 创建共享链接
export function createShareLink(data) {
  return request.post('/business_intelligence/share', data)
}

// 获取共享报表数据
export function getSharedReport(id) {
  return request.get(`/business_intelligence/shared/${id}`)
}

// 获取实时数据
export function getRealtimeData(params) {
  return request.get('/business_intelligence/realtime', params)
}

// 用户自定义仪表盘相关API

// 获取用户仪表盘列表
export function getUserDashboards() {
  return request.get('/business_intelligence/user_dashboards')
}

// 获取用户仪表盘详情
export function getUserDashboard(id) {
  return request.get(`/business_intelligence/user_dashboards/${id}`)
}

// 创建用户仪表盘
export function createUserDashboard(data) {
  return request.post('/business_intelligence/user_dashboards', data)
}

// 更新用户仪表盘
export function updateUserDashboard(id, data) {
  return request.put(`/business_intelligence/user_dashboards/${id}`, data)
}

// 删除用户仪表盘
export function deleteUserDashboard(id) {
  return request.delete(`/business_intelligence/user_dashboards/${id}`)
}

// 分享用户仪表盘
export function shareUserDashboard(id, data) {
  return request.post(`/business_intelligence/user_dashboards/${id}/share`, data)
}

// 获取仪表盘数据源
export function getDashboardDataSource(type, params) {
  return request.get(`/business_intelligence/data_source/${type}`, params)
}

export default {
  getMultiDimensionalAnalysis,
  getBusinessIndicators,
  getTrendPrediction,
  getReportTemplates,
  getReportTemplateDetail,
  createShareLink,
  getSharedReport,
  getRealtimeData,
  getUserDashboards,
  getUserDashboard,
  createUserDashboard,
  updateUserDashboard,
  deleteUserDashboard,
  shareUserDashboard,
  getDashboardDataSource
} 