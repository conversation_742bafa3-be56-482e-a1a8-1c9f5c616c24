import { createRouter, createWebHistory } from 'vue-router';
import MainLayout from '@/layouts/MainLayout.vue';
import AdaptiveLayout from '@/layouts/AdaptiveLayout.vue';

// 共享报表路由
const sharedRoutes = [
  {
    path: '/shared-report/:id',
    name: 'SharedReport',
    component: () => import('@/views/shared/SharedReport.vue'),
    meta: { 
      title: '共享报表',
      requiresAuth: false
    }
  },
  {
    path: '/share/:type/:config',
    name: 'SharePreview',
    component: () => import('@/views/shared/SharePreview.vue'),
    meta: { 
      title: '报表预览',
      requiresAuth: false
    }
  }
]

// 路由配置
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { title: '登录', requiresAuth: false }
  },
  {
    path: '/shareholder-login',
    name: 'ShareholderLogin',
    component: () => import('@/views/auth/ShareholderLogin.vue'),
    meta: { title: '股东登录', requiresAuth: false }
  },
  {
    path: '/admin-login',
    name: 'AdminLogin',
    component: () => import('@/views/auth/AdminLogin.vue'),
    meta: { title: '超级管理员登录', requiresAuth: false }
  },
  {
    path: '/',
    component: AdaptiveLayout,
    redirect: '/dashboard',
    children: [
      // 工作台
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '工作台' }
      },
      // 运营中心
      {
        path: 'operations-center',
        name: 'OperationsCenter',
        component: () => import('@/views/OperationsCenter.vue'),
        meta: { title: '工作台', icon: 'el-icon-s-home', requiresAuth: true }
      },
      // 门店管理
      {
        path: 'stores',
        name: 'StoreList',
        component: () => import('@/views/store/StoreList.vue'),
        meta: { title: '门店列表', icon: 'el-icon-s-shop', requiresAuth: true }
      },
      {
        path: 'stores/:id',
        name: 'StoreDetail',
        component: () => import('@/views/store/StoreDetail.vue'),
        meta: { title: '门店详情', requiresAuth: true, hidden: true, showBackButton: true }
      },
      // 员工管理
      {
        path: 'employees',
        name: 'EmployeeList',
        component: () => import('@/views/employee/EmployeeList.vue'),
        meta: { title: '员工列表', icon: 'el-icon-user', requiresAuth: true }
      },
      {
        path: 'employees/:id',
        name: 'EmployeeDetail',
        component: () => import('@/views/employee/EmployeeDetail.vue'),
        meta: { title: '员工详情', requiresAuth: true, hidden: true, showBackButton: true }
      },
      // 项目管理
      {
        path: 'group-items',
        name: 'GroupItemList',
        component: () => import('@/views/item/GroupItemList.vue'),
        meta: { title: '集团项目库', icon: 'el-icon-s-management', requiresAuth: true }
      },
      {
        path: 'store-items',
        name: 'StoreItemList',
        component: () => import('@/views/item/StoreItemList.vue'),
        meta: { title: '门店项目库', icon: 'el-icon-s-management', requiresAuth: true }
      },
      // 提成方案
      {
        path: 'commission-schemes',
        name: 'SchemeList',
        component: () => import('@/views/commission/SchemeList.vue'),
        meta: { title: '提成方案', icon: 'el-icon-s-finance', requiresAuth: true }
      },
      {
        path: 'commission-schemes/:id',
        name: 'SchemeDetail',
        component: () => import('@/views/commission/SchemeDetail.vue'),
        meta: { title: '方案详情', requiresAuth: true, hidden: true, showBackButton: true }
      },
      // 订单管理
      {
        path: 'orders',
        name: 'OrderList',
        component: () => import('@/views/order/OrderList.vue'),
        meta: { title: '订单管理', icon: 'el-icon-s-order', requiresAuth: true }
      },
      {
        path: 'orders/create',
        name: 'OrderCreate',
        component: () => import('@/views/order/OrderCreate.vue'),
        meta: { title: '新增订单', requiresAuth: true, hidden: true, showBackButton: true }
      },
      {
        path: 'orders/:id',
        name: 'OrderDetail',
        component: () => import('@/views/order/OrderDetail.vue'),
        meta: { title: '订单详情', requiresAuth: true, hidden: true, showBackButton: true }
      },
      {
        path: 'orders/:id/edit',
        name: 'OrderEdit',
        component: () => import('@/views/order/OrderCreate.vue'),
        meta: { title: '编辑订单', requiresAuth: true, hidden: true, showBackButton: true }
      },
      // 收银管理
      {
        path: 'cashier',
        name: 'CashierPos',
        component: () => import('@/views/cashier/CashierPos.vue'),
        meta: { title: '收银台', icon: 'el-icon-money', requiresAuth: true }
      },
      {
        path: 'cashier/records',
        name: 'CashierRecords',
        component: () => import('@/views/cashier/CashierRecords.vue'),
        meta: { title: '收银记录', requiresAuth: true, hidden: true, showBackButton: true }
      },
      // 库存管理
      {
        path: 'inventory',
        name: 'InventoryList',
        component: () => import('@/views/inventory/InventoryList.vue'),
        meta: { title: '库存管理', icon: 'el-icon-box', requiresAuth: true }
      },
      {
        path: 'inventory/products/:id',
        name: 'ProductDetail',
        component: () => import('@/views/inventory/ProductDetail.vue'),
        meta: { title: '商品详情', requiresAuth: true, hidden: true, showBackButton: true }
      },
      // 财务管理
      {
        path: 'finance',
        name: 'FinanceReport',
        component: () => import('@/views/finance/FinanceReport.vue'),
        meta: { title: '财务报表', icon: 'el-icon-s-finance', requiresAuth: true }
      },
      {
        path: 'finance/detail',
        name: 'FinanceDetail',
        component: () => import('@/views/finance/FinanceDetail.vue'),
        meta: { title: '财务详情', requiresAuth: true, hidden: true, showBackButton: true }
      },
      // 系统设置
      {
        path: 'settings',
        name: 'SystemSettings',
        component: () => import('@/views/settings/SystemSettings.vue'),
        meta: { title: '系统设置', icon: 'el-icon-setting', requiresAuth: true }
      },
      // 营业数据
      {
        path: 'operations',
        name: 'OperationList',
        component: () => import('@/views/operation/OperationList.vue'),
        meta: { title: '营业数据', icon: 'el-icon-s-data', requiresAuth: true }
      },
      {
        path: 'operations/:id',
        name: 'OperationDetail',
        component: () => import('@/views/operation/OperationDetail.vue'),
        meta: { title: '订单详情', requiresAuth: true, hidden: true, showBackButton: true }
      },
      {
        path: 'operation-stats',
        name: 'OperationStats',
        component: () => import('@/views/operation/OperationStats.vue'),
        meta: { title: '营业统计', icon: 'el-icon-s-data', requiresAuth: true }
      },
      // 数据决策中心
      {
        path: 'bi/dashboard',
        name: 'DataDashboard',
        component: () => import('@/views/business_intelligence/DataDashboard.vue'),
        meta: { title: '数据驾驶舱', icon: 'el-icon-data-line', requiresAuth: true }
      },
      {
        path: 'bi/analysis',
        name: 'MultiDimensionalAnalysis',
        component: () => import('@/views/business_intelligence/MultiDimensionalAnalysis.vue'),
        meta: { title: '多维度分析报表', icon: 'el-icon-pie-chart', requiresAuth: true }
      },
      {
        path: 'bi/chart-gallery',
        name: 'ChartGallery',
        component: () => import('@/views/business_intelligence/ChartGallery.vue'),
        meta: { title: '数据可视化图表库', icon: 'el-icon-data-analysis', requiresAuth: true }
      },
      {
        path: 'bi/report-templates',
        name: 'ReportTemplates',
        component: () => import('@/views/business_intelligence/ReportTemplates.vue'),
        meta: { title: '报表模板库', icon: 'el-icon-document', requiresAuth: true }
      },
      {
        path: 'bi/realtime-analytics',
        name: 'RealtimeAnalytics',
        component: () => import('@/views/business_intelligence/RealtimeAnalytics.vue'),
        meta: { title: '实时数据分析', icon: 'el-icon-refresh', requiresAuth: true }
      },
      {
        path: 'bi/ai-analytics',
        name: 'AIAnalytics',
        component: () => import('@/views/business_intelligence/AIAnalytics.vue'),
        meta: { title: 'AI智能分析', icon: 'el-icon-cpu', requiresAuth: true }
      },
      // 用户自定义仪表盘
      {
        path: 'bi/user-dashboards',
        name: 'UserDashboards',
        component: () => import('@/views/business_intelligence/UserDashboards.vue'),
        meta: { title: '我的仪表盘', icon: 'el-icon-s-platform', requiresAuth: true }
      },
      {
        path: 'bi/user-dashboard-edit',
        name: 'UserDashboardEdit',
        component: () => import('@/views/business_intelligence/UserDashboardEdit.vue'),
        meta: { title: '编辑仪表盘', requiresAuth: true, hidden: true, showBackButton: true }
      },
      // 评价管理
      {
        path: 'reviews',
        name: 'ReviewList',
        component: () => import('@/views/operation/ReviewList.vue'),
        meta: { title: '评价管理', icon: 'el-icon-star-on', requiresAuth: true }
      },
      // 会员卡管理
      {
        path: 'member/cards',
        name: 'CardList',
        component: () => import('@/views/member/ResponsiveCardList.vue'),
        meta: { title: '会员卡管理', icon: 'el-icon-bank-card', requiresAuth: true }
      },
      {
        path: 'member/cards/:id',
        name: 'CardDetail',
        component: () => import('@/views/member/CardDetail.vue'),
        meta: { title: '会员卡详情', requiresAuth: true, hidden: true, showBackButton: true }
      },
      {
        path: 'member/levels',
        name: 'MemberLevels',
        component: () => import('@/views/member-levels/index.vue'),
        meta: { title: '会员等级管理', icon: 'el-icon-medal', requiresAuth: true }
      },
      {
        path: 'product-categories',
        name: 'ProductCategories',
        component: () => import('@/views/product-categories/index.vue'),
        meta: { title: '商品分类管理', icon: 'el-icon-menu', requiresAuth: true }
      },
      {
        path: 'employees/enhanced',
        name: 'EmployeesEnhanced',
        component: () => import('@/views/employees/enhanced.vue'),
        meta: { title: '员工增强管理', icon: 'el-icon-user', requiresAuth: true }
      },
      {
        path: 'projects/enhanced',
        name: 'ProjectsEnhanced',
        component: () => import('@/views/projects/enhanced.vue'),
        meta: { title: '项目管理升级', icon: 'el-icon-setting', requiresAuth: true }
      },
      {
        path: 'payment-methods',
        name: 'PaymentMethods',
        component: () => import('@/views/payment-methods/index.vue'),
        meta: { title: '支付方式管理', icon: 'el-icon-credit-card', requiresAuth: true }
      },
      {
        path: 'group-buying-platforms',
        name: 'GroupBuyingPlatforms',
        component: () => import('@/views/group-buying-platforms/index.vue'),
        meta: { title: '团购平台管理', icon: 'el-icon-platform-eleme', requiresAuth: true }
      },
      // 绩效统计
      {
        path: 'employee-performance',
        name: 'EmployeePerformance',
        component: () => import('@/views/performance/EmployeePerformance.vue'),
        meta: { title: '员工绩效', icon: 'el-icon-s-data', requiresAuth: true }
      },
      {
        path: 'store-performance',
        name: 'StorePerformance',
        component: () => import('@/views/performance/StorePerformance.vue'),
        meta: { title: '门店绩效', icon: 'el-icon-s-data', requiresAuth: true }
      },
      // 股东管理
      {
        path: 'shareholders',
        name: 'ShareholderList',
        component: () => import('@/views/shareholder/ShareholderList.vue'),
        meta: { title: '股东管理', icon: 'el-icon-s-custom', requiresAuth: true }
      },
      {
        path: 'shareholders/:id',
        name: 'ShareholderDetail',
        component: () => import('@/views/shareholder/ShareholderDetail.vue'),
        meta: { title: '股东详情', requiresAuth: true, hidden: true, showBackButton: true }
      },
      {
        path: 'shareholder-equity',
        name: 'ShareholderEquity',
        component: () => import('@/views/shareholder/ShareholderEquity.vue'),
        meta: { title: '股权结构', icon: 'el-icon-s-operation', requiresAuth: true }
      },
      {
        path: 'shareholder-dashboard',
        name: 'ShareholderDashboard',
        component: () => import('@/views/shareholder/ShareholderDashboard.vue'),
        meta: { title: '股东数据中心', icon: 'el-icon-s-data', requiresAuth: true }
      },
      // 分红方案
      {
        path: 'dividend-schemes',
        name: 'DividendSchemes',
        component: () => import('@/views/dividend/DividendSchemes.vue'),
        meta: { title: '分红方案', icon: 'el-icon-money', requiresAuth: true }
      },
      // 分红记录
      {
        path: 'dividend-records',
        name: 'DividendRecords',
        component: () => import('@/views/dividend/DividendRecords.vue'),
        meta: { title: '分红记录', icon: 'el-icon-s-data', requiresAuth: true }
      },
      // 分红计算
      {
        path: 'dividend/calculator',
        name: 'DividendCalculator',
        component: () => import('@/views/dividend/DividendCalculator.vue'),
        meta: { title: '分红计算器', icon: 'el-icon-money', requiresAuth: true }
      },
      // 分红报表
      {
        path: 'dividend/report',
        name: 'DividendReport',
        component: () => import('@/views/dividend/DividendReport.vue'),
        meta: { title: '分红报表', icon: 'el-icon-s-data', requiresAuth: true }
      },
      // 员工绩效
      {
        path: 'performances/employee',
        name: 'EmployeePerformance',
        component: () => import('@/views/performances/EmployeePerformance.vue'),
        meta: { title: '员工绩效', icon: 'el-icon-user', requiresAuth: true }
      },
      // 门店绩效
      {
        path: 'performances/store',
        name: 'StorePerformance',
        component: () => import('@/views/performances/StorePerformance.vue'),
        meta: { title: '门店绩效', icon: 'el-icon-office-building', requiresAuth: true }
      },
      // 知识库
      {
        path: 'knowledge/articles',
        name: 'ArticleList',
        component: () => import('@/views/knowledge/ArticleList.vue'),
        meta: { title: '知识库文章', icon: 'el-icon-document', requiresAuth: true }
      },
      {
        path: 'knowledge/articles/:id',
        name: 'ArticleDetail',
        component: () => import('@/views/knowledge/ArticleDetail.vue'),
        meta: { title: '文章详情', requiresAuth: true, hidden: true, showBackButton: true }
      },
      {
        path: 'knowledge/articles/edit/:id',
        name: 'ArticleEdit',
        component: () => import('@/views/knowledge/ArticleEdit.vue'),
        meta: { title: '编辑文章', requiresAuth: true, hidden: true }
      },
      {
        path: 'knowledge/categories',
        name: 'CategoryManage',
        component: () => import('@/views/knowledge/CategoryManage.vue'),
        meta: { title: '分类管理', icon: 'el-icon-folder', requiresAuth: true }
      },
      {
        path: 'knowledge/tags',
        name: 'TagManage',
        component: () => import('@/views/knowledge/TagManage.vue'),
        meta: { title: '标签管理', icon: 'el-icon-collection-tag', requiresAuth: true }
      },
      // 客户管理
      {
        path: 'customers',
        name: 'CustomerList',
        component: () => import('@/views/customer/CustomerList.vue'),
        meta: { title: '客户管理', icon: 'el-icon-user', requiresAuth: true }
      },
      {
        path: 'customers/:id',
        name: 'CustomerDetail',
        component: () => import('@/views/customer/CustomerDetail.vue'),
        meta: { title: '客户详情', requiresAuth: true, hidden: true, showBackButton: true }
      },
      // 客户画像
      {
        path: 'customer/profile/:id',
        name: 'CustomerProfile',
        component: () => import('@/views/customer/CustomerProfile.vue'),
        meta: { title: '客户画像', requiresAuth: true, hidden: true, showBackButton: true }
      },
      {
        path: 'customer/behavior',
        name: 'CustomerBehavior',
        component: () => import('@/views/customer/CustomerBehavior.vue'),
        meta: { title: '客户行为分析', icon: 'el-icon-data-analysis', requiresAuth: true }
      },
      {
        path: 'customer/tag',
        name: 'CustomerTag',
        component: () => import('@/views/customer/CustomerTag.vue'),
        meta: { title: '客户标签管理', icon: 'el-icon-collection-tag', requiresAuth: true }
      },
      {
        path: 'customer/segment',
        name: 'CustomerSegment',
        component: () => import('@/views/customer/CustomerSegment.vue'),
        meta: { title: '客户分群管理', icon: 'el-icon-user', requiresAuth: true }
      },
      // 预约管理
      {
        path: 'appointment/calendar',
        name: 'AppointmentCalendar',
        component: () => import('@/views/appointment/AppointmentCalendar.vue'),
        meta: { title: '预约日历', icon: 'el-icon-date', requiresAuth: true }
      },
      {
        path: 'appointment/resources',
        name: 'ResourceList',
        component: () => import('@/views/appointment/ResourceList.vue'),
        meta: { title: '资源管理', icon: 'el-icon-office-building', requiresAuth: true }
      },
      {
        path: 'appointment/schedules',
        name: 'ScheduleList',
        component: () => import('@/views/appointment/ScheduleList.vue'),
        meta: { title: '排班管理', icon: 'el-icon-s-check', requiresAuth: true }
      },
      {
        path: 'appointment/follow-ups',
        name: 'FollowUpList',
        component: () => import('@/views/appointment/FollowUpList.vue'),
        meta: { title: '随访管理', icon: 'el-icon-phone-outline', requiresAuth: true }
      },
      {
        path: 'appointment/schedule-rules',
        name: 'ScheduleRuleSettings',
        component: () => import('@/views/appointment/ScheduleRuleSettings.vue'),
        meta: { title: '排班规则', icon: 'el-icon-set-up', requiresAuth: true }
      },
      {
        path: 'appointments/:id',
        name: 'AppointmentDetail',
        component: () => import('@/views/appointment/AppointmentDetail.vue'),
        meta: { title: '预约详情', requiresAuth: true, hidden: true, showBackButton: true }
      },
      // 优惠券管理
      {
        path: 'marketing/coupon-templates',
        name: 'CouponTemplateList',
        component: () => import('@/views/marketing/CouponTemplateList.vue'),
        meta: { title: '优惠券模板', icon: 'el-icon-tickets', requiresAuth: true }
      },
      {
        path: 'marketing/coupons',
        name: 'CouponList',
        component: () => import('@/views/marketing/CouponList.vue'),
        meta: { title: '优惠券管理', icon: 'el-icon-shopping-cart-full', requiresAuth: true }
      },
      // 营销活动
      {
        path: 'marketing/activities',
        name: 'MarketingActivityList',
        component: () => import('@/views/marketing/MarketingActivityList.vue'),
        meta: { title: '营销活动', icon: 'el-icon-present', requiresAuth: true }
      },
      // 营销自动化
      {
        path: 'marketing/automation/rules',
        name: 'AutomationRules',
        component: () => import('@/views/marketing/AutomationRules.vue'),
        meta: { title: '自动化规则', icon: 'el-icon-setting', requiresAuth: true }
      },
      {
        path: 'marketing/retention',
        name: 'CustomerRetention',
        component: () => import('@/views/marketing/CustomerRetention.vue'),
        meta: { title: '流失会员唤醒', icon: 'el-icon-user', requiresAuth: true }
      },
      {
        path: 'marketing/birthday',
        name: 'BirthdayCare',
        component: () => import('@/views/marketing/BirthdayCare.vue'),
        meta: { title: '生日关怀', icon: 'el-icon-present', requiresAuth: true }
      },
      {
        path: 'marketing/incentive',
        name: 'ConsumptionIncentive',
        component: () => import('@/views/marketing/ConsumptionIncentive.vue'),
        meta: { title: '消费激励', icon: 'el-icon-wallet', requiresAuth: true }
      },
      {
        path: 'marketing/coupons',
        name: 'CouponManagement',
        component: () => import('@/views/marketing/CouponManagement.vue'),
        meta: { title: '优惠券管理', icon: 'el-icon-ticket', requiresAuth: true }
      },
      {
        path: 'marketing/member-cards',
        name: 'MemberCardManagement',
        component: () => import('@/views/marketing/MemberCardManagement.vue'),
        meta: { title: '会员卡管理', icon: 'el-icon-credit-card', requiresAuth: true }
      },
      // 财务管理
      {
        path: 'financial/software-integration',
        name: 'SoftwareIntegration',
        component: () => import('@/views/financial/SoftwareIntegration.vue'),
        meta: { title: '财务软件集成', icon: 'el-icon-connection', requiresAuth: true }
      },
      {
        path: 'financial/voucher-generation',
        name: 'VoucherGeneration',
        component: () => import('@/views/financial/VoucherGeneration.vue'),
        meta: { title: '自动凭证生成', icon: 'el-icon-document-add', requiresAuth: true }
      },
      {
        path: 'financial/auto-reconciliation',
        name: 'AutoReconciliation',
        component: () => import('@/views/financial/AutoReconciliation.vue'),
        meta: { title: '自动对账', icon: 'el-icon-document-checked', requiresAuth: true }
      },
      // 数据分析
      {
        path: 'dashboard/data-cockpit',
        name: 'DataCockpit',
        component: () => import('@/views/dashboard/DataCockpit.vue'),
        meta: { title: '数据驾驶舱', icon: 'el-icon-data-analysis', requiresAuth: true }
      },
      // 移动端
      {
        path: 'mobile/home',
        name: 'MobileHome',
        component: () => import('@/views/mobile/MobileHome.vue'),
        meta: { title: '移动端首页', icon: 'el-icon-mobile', requiresAuth: true, mobile: true }
      },
      // 客户推荐关系
      {
        path: 'referral/list',
        name: 'ReferralList',
        component: () => import('@/views/referral/ReferralList.vue'),
        meta: { title: '推荐关系管理', icon: 'el-icon-connection', requiresAuth: true }
      },
      {
        path: 'referral/codes',
        name: 'ReferralCodeList',
        component: () => import('@/views/referral/ReferralCodeList.vue'),
        meta: { title: '推荐码管理', icon: 'el-icon-postcard', requiresAuth: true }
      },
      {
        path: 'referral/settings',
        name: 'ReferralSettingList',
        component: () => import('@/views/referral/ReferralSettingList.vue'),
        meta: { title: '推荐奖励设置', icon: 'el-icon-setting', requiresAuth: true }
      },
      {
        path: 'referral/stats',
        name: 'ReferralRelationshipList',
        component: () => import('@/views/referral/ReferralRelationshipList.vue'),
        meta: { title: '推荐关系统计', icon: 'el-icon-data-analysis', requiresAuth: true }
      },
      // 系统设置
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('@/views/settings/Settings.vue'),
        meta: { title: '系统设置', icon: 'el-icon-setting', requiresAuth: true }
      },
      // 营销首页
      {
        path: 'marketing',
        name: 'MarketingHome',
        component: () => import('@/views/marketing/MarketingActivityList.vue'),
        meta: { title: '营销活动', icon: 'el-icon-present', requiresAuth: true }
      }
    ]
  },
  // 超级管理员路由
  {
    path: '/admin',
    component: () => import('@/layouts/AdminLayout.vue'),
    redirect: '/admin/dashboard',
    meta: { requiresAuth: true, userType: 'admin' },
    children: [
      // 管理员仪表板
      {
        path: 'dashboard',
        name: 'AdminDashboard',
        component: () => import('@/views/admin/AdminDashboard.vue'),
        meta: { title: '管理员仪表板', icon: 'el-icon-s-platform' }
      },
      // 用户管理
      {
        path: 'users',
        name: 'AdminUserList',
        component: () => import('@/views/admin/AdminUserList.vue'),
        meta: { title: '管理员用户', icon: 'el-icon-user-solid' }
      },
      // 角色管理
      {
        path: 'roles',
        name: 'AdminRoleList',
        component: () => import('@/views/admin/AdminRoleList.vue'),
        meta: { title: '角色管理', icon: 'el-icon-s-custom' }
      },
      // 权限管理
      {
        path: 'permissions',
        name: 'AdminPermissionList',
        component: () => import('@/views/admin/AdminPermissionList.vue'),
        meta: { title: '权限管理', icon: 'el-icon-key' }
      },
      // 业务角色管理
      {
        path: 'business-roles',
        name: 'BusinessRoleManagement',
        component: () => import('@/views/admin/BusinessRoleManagement.vue'),
        meta: { title: '业务角色管理', icon: 'el-icon-user-solid' }
      },
      // 系统配置
      {
        path: 'configs',
        name: 'SystemConfigList',
        component: () => import('@/views/admin/SystemConfigList.vue'),
        meta: { title: '系统配置', icon: 'el-icon-setting' }
      },
      // 操作日志
      {
        path: 'logs',
        name: 'AdminOperationLogs',
        component: () => import('@/views/admin/AdminOperationLogs.vue'),
        meta: { title: '操作日志', icon: 'el-icon-document' }
      },
      // 系统监控
      {
        path: 'monitor',
        name: 'SystemMonitor',
        component: () => import('@/views/admin/SystemMonitor.vue'),
        meta: { title: '系统监控', icon: 'el-icon-monitor' }
      },
      // 数据维护
      {
        path: 'data-maintenance',
        name: 'DataMaintenance',
        component: () => import('@/views/admin/DataMaintenance.vue'),
        meta: { title: '数据维护', icon: 'el-icon-s-tools' }
      },
      // 网站维护
      {
        path: 'site-maintenance',
        name: 'SiteMaintenance',
        component: () => import('@/views/admin/SiteMaintenance.vue'),
        meta: { title: '网站维护', icon: 'el-icon-s-cooperation' }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: { title: '404', requiresAuth: false }
  }
];

routes.push(...sharedRoutes)

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 按摩推拿连锁管理系统` : '按摩推拿连锁管理系统';

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    // 管理员路由认证
    if (to.meta.userType === 'admin') {
      const adminToken = localStorage.getItem('admin_token');
      if (!adminToken) {
        next({ name: 'AdminLogin', query: { redirect: to.fullPath } });
        return;
      }
    } else {
      // 普通用户认证
      const token = localStorage.getItem('token');
      if (!token) {
        next({ name: 'Login', query: { redirect: to.fullPath } });
        return;
      }
    }
  }

  next();
});

export default router;
