<template>
  <el-dialog
    v-model="dialogVisible"
    :title="'分享' + title"
    width="600px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div v-loading="loading" class="share-modal-content">
      <el-tabs v-model="activeTabKey">
        <el-tab-pane label="链接分享" name="link">
          <div class="share-section">
            <el-alert
              v-if="shareLink"
              type="success"
              title="分享链接已生成"
              description="您可以复制下面的链接分享给他人，或者使用其他分享方式。"
              show-icon
            />
            <div class="share-link-group">
              <el-input
                v-model="shareLink"
                readonly
                class="share-link-input"
                placeholder="生成分享链接..."
              >
                <template #append>
                  <el-button type="primary" @click="copyLink">
                    <el-icon><DocumentCopy /></el-icon>
                    复制
                  </el-button>
                </template>
              </el-input>
            </div>
              
              <div class="share-options">
                <a-button type="primary" @click="generateLink" :loading="generating">
                  <template #icon><LinkOutlined /></template>
                  生成临时链接
                </a-button>
                <a-button type="primary" @click="createPermanentLink" :loading="generating">
                  <template #icon><SaveOutlined /></template>
                  创建永久链接
                </a-button>
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="qrcode" tab="二维码分享">
            <div class="share-section qrcode-section">
              <div v-if="qrCodeUrl" class="qrcode-container">
                <img :src="qrCodeUrl" alt="分享二维码" class="qrcode-image" />
                <a-button type="primary" @click="saveQRCode">
                  <template #icon><DownloadOutlined /></template>
                  保存二维码
                </a-button>
              </div>
              <div v-else class="qrcode-placeholder">
                <a-empty description="请先生成分享链接" />
                <a-button type="primary" @click="generateQRCode" :disabled="!shareLink" :loading="generatingQR">
                  <template #icon><QrcodeOutlined /></template>
                  生成二维码
                </a-button>
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="social" tab="社交媒体分享">
            <div class="share-section social-section">
              <a-alert
                type="info"
                message="选择分享方式"
                description="请选择以下方式分享报表"
                show-icon
              />
              
              <div class="social-buttons">
                <a-button @click="shareToWeChat" :disabled="!shareLink">
                  <template #icon><WechatOutlined /></template>
                  微信
                </a-button>
                <a-button @click="shareToWorkWeChat" :disabled="!shareLink">
                  <template #icon><WechatOutlined /></template>
                  企业微信
                </a-button>
                <a-button @click="showEmailShare" :disabled="!shareLink">
                  <template #icon><MailOutlined /></template>
                  邮件
                </a-button>
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="settings" tab="分享设置">
            <div class="share-section settings-section">
              <a-form :model="shareSettings" layout="vertical">
                <a-form-item label="分享标题" name="title">
                  <a-input v-model:value="shareSettings.title" placeholder="请输入分享标题" />
                </a-form-item>
                
                <a-form-item label="过期时间" name="expiryType">
                  <a-radio-group v-model:value="shareSettings.expiryType" @change="handleExpiryTypeChange">
                    <a-radio value="never">永不过期</a-radio>
                    <a-radio value="date">指定日期</a-radio>
                  </a-radio-group>
                </a-form-item>
                
                <a-form-item v-if="shareSettings.expiryType === 'date'" label="选择过期日期" name="expiryDate">
                  <a-date-picker 
                    v-model:value="shareSettings.expiryDate" 
                    :disabled-date="disabledDate"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-form>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-spin>
    
    <!-- 邮件分享弹窗 -->
    <a-modal
      v-model:visible="emailModalVisible"
      title="邮件分享"
      @ok="shareByEmail"
      okText="发送"
      cancelText="取消"
    >
      <a-form :model="emailForm" layout="vertical">
        <a-form-item label="收件人邮箱" name="recipients">
          <a-input v-model:value="emailForm.recipients" placeholder="请输入收件人邮箱，多个邮箱用逗号分隔" />
        </a-form-item>
      </a-form>
    </a-modal>
  </a-modal>
</template>

<script>
import { ref, reactive, defineComponent, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Link,
  DocumentCopy,
  QrCode,
  Download,
  Message,
  Folder
} from '@element-plus/icons-vue'
import shareService from '@/utils/shareService'

export default defineComponent({
  name: 'ShareReportModal',
  components: {
    Link,
    DocumentCopy,
    QrCode,
    Download,
    Message,
    Folder
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    reportConfig: {
      type: Object,
      required: true
    },
    reportType: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: '报表'
    }
  },
  emits: ['update:visible', 'close'],
  setup(props, { emit }) {
    const loading = ref(false)
    const generating = ref(false)
    const generatingQR = ref(false)
    const activeTabKey = ref('link')
    const shareLink = ref('')
    const qrCodeUrl = ref('')
    const emailModalVisible = ref(false)
    
    // 分享设置
    const shareSettings = reactive({
      title: props.title,
      expiryType: 'never',
      expiryDate: null
    })
    
    // 邮件表单
    const emailForm = reactive({
      recipients: ''
    })
    
    // 监听标题变化
    watch(() => props.title, (newTitle) => {
      shareSettings.title = newTitle
    })
    
    // 生成临时分享链接
    const generateLink = () => {
      generating.value = true
      
      try {
        const link = shareService.generateShareLink(props.reportConfig, props.reportType)
        if (link) {
          shareLink.value = link
          message.success('分享链接生成成功')
        } else {
          message.error('生成分享链接失败')
        }
      } catch (error) {
        console.error('生成分享链接出错:', error)
        message.error('生成分享链接失败')
      } finally {
        generating.value = false
      }
    }
    
    // 创建永久分享链接
    const createPermanentLink = async () => {
      generating.value = true
      
      try {
        // 处理过期日期
        let expiryDate = null
        if (shareSettings.expiryType === 'date' && shareSettings.expiryDate) {
          expiryDate = shareSettings.expiryDate.toDate()
        }
        
        const result = await shareService.createPermanentShare(
          props.reportConfig, 
          props.reportType,
          shareSettings.title,
          expiryDate
        )
        
        if (result && result.shareLink) {
          shareLink.value = result.shareLink
          message.success('永久分享链接创建成功')
        } else {
          message.error('创建永久分享链接失败')
        }
      } catch (error) {
        console.error('创建永久分享链接出错:', error)
        message.error('创建永久分享链接失败')
      } finally {
        generating.value = false
      }
    }
    
    // 复制链接
    const copyLink = () => {
      if (!shareLink.value) {
        message.warning('请先生成分享链接')
        return
      }
      
      shareService.copyShareLink(shareLink.value)
    }
    
    // 生成二维码
    const generateQRCode = async () => {
      if (!shareLink.value) {
        message.warning('请先生成分享链接')
        return
      }
      
      generatingQR.value = true
      
      try {
        const dataUrl = await shareService.generateQRCode(shareLink.value)
        if (dataUrl) {
          qrCodeUrl.value = dataUrl
        } else {
          message.error('生成二维码失败')
        }
      } catch (error) {
        console.error('生成二维码出错:', error)
        message.error('生成二维码失败')
      } finally {
        generatingQR.value = false
      }
    }
    
    // 保存二维码
    const saveQRCode = () => {
      if (!qrCodeUrl.value) {
        message.warning('请先生成二维码')
        return
      }
      
      shareService.saveQRCode(qrCodeUrl.value, `${shareSettings.title}-分享二维码`)
    }
    
    // 微信分享
    const shareToWeChat = () => {
      if (!shareLink.value) {
        message.warning('请先生成分享链接')
        return
      }
      
      shareService.shareToWeChat(shareLink.value, shareSettings.title, `${shareSettings.title} - 按摩推拿连锁门店管理系统`)
    }
    
    // 企业微信分享
    const shareToWorkWeChat = () => {
      if (!shareLink.value) {
        message.warning('请先生成分享链接')
        return
      }
      
      shareService.shareToWorkWeChat(shareLink.value, shareSettings.title, `${shareSettings.title} - 按摩推拿连锁门店管理系统`)
    }
    
    // 显示邮件分享弹窗
    const showEmailShare = () => {
      if (!shareLink.value) {
        message.warning('请先生成分享链接')
        return
      }
      
      emailModalVisible.value = true
    }
    
    // 邮件分享
    const shareByEmail = () => {
      if (!shareLink.value) {
        message.warning('请先生成分享链接')
        return
      }
      
      if (!emailForm.recipients) {
        message.warning('请输入收件人邮箱')
        return
      }
      
      shareService.shareByEmail(shareLink.value, shareSettings.title, emailForm.recipients)
      emailModalVisible.value = false
    }
    
    // 处理过期类型变更
    const handleExpiryTypeChange = (e) => {
      if (e.target.value === 'never') {
        shareSettings.expiryDate = null
      } else if (e.target.value === 'date' && !shareSettings.expiryDate) {
        // 默认设置为一周后
        shareSettings.expiryDate = dayjs().add(7, 'day')
      }
    }
    
    // 禁用今天之前的日期
    const disabledDate = (current) => {
      return current && current < dayjs().startOf('day')
    }
    
    // 关闭弹窗
    const handleCancel = () => {
      emit('update:visible', false)
      emit('close')
    }
    
    return {
      loading,
      generating,
      generatingQR,
      activeTabKey,
      shareLink,
      qrCodeUrl,
      shareSettings,
      emailModalVisible,
      emailForm,
      generateLink,
      createPermanentLink,
      copyLink,
      generateQRCode,
      saveQRCode,
      shareToWeChat,
      shareToWorkWeChat,
      showEmailShare,
      shareByEmail,
      handleExpiryTypeChange,
      disabledDate,
      handleCancel
    }
  }
})
</script>

<style scoped>
.share-modal-content {
  min-height: 300px;
}

.share-section {
  margin: 16px 0;
}

.share-link-group {
  margin: 16px 0;
  display: flex;
}

.share-link-input {
  flex: 1;
}

.share-options {
  display: flex;
  gap: 16px;
  margin-top: 16px;
}

.qrcode-section {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.qrcode-image {
  width: 200px;
  height: 200px;
  border: 1px solid #eee;
}

.qrcode-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.social-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.social-buttons {
  display: flex;
  gap: 16px;
  margin-top: 16px;
}

.settings-section {
  padding: 0 16px;
}

@media screen and (max-width: 768px) {
  .share-options {
    flex-direction: column;
    gap: 12px;
  }
  
  .social-buttons {
    flex-direction: column;
    width: 100%;
  }
  
  .social-buttons .ant-btn {
    width: 100%;
    margin-bottom: 8px;
  }
  
  .qrcode-image {
    width: 180px;
    height: 180px;
  }
  
  .settings-section {
    padding: 0 8px;
  }
}
</style> 