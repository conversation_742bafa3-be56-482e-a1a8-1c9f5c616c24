<template>
  <el-dialog
    v-model="dialogVisible"
    :title="'分享' + title"
    width="600px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div v-loading="loading" class="share-modal-content">
      <div class="share-section">
        <el-alert
          v-if="shareLink"
          type="success"
          title="分享链接已生成"
          description="您可以复制下面的链接分享给他人。"
          show-icon
        />
        <div class="share-link-group">
          <el-input
            v-model="shareLink"
            readonly
            class="share-link-input"
            placeholder="生成分享链接..."
          >
            <template #append>
              <el-button type="primary" @click="copyLink">
                <el-icon><DocumentCopy /></el-icon>
                复制
              </el-button>
            </template>
          </el-input>
        </div>

        <div class="share-options">
          <el-button type="primary" @click="generateLink" :loading="generating">
            <el-icon><Link /></el-icon>
            生成分享链接
          </el-button>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, defineComponent, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Link,
  DocumentCopy
} from '@element-plus/icons-vue'

export default defineComponent({
  name: 'ShareReportModal',
  components: {
    Link,
    DocumentCopy
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    reportConfig: {
      type: Object,
      required: true
    },
    reportType: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: '报表'
    }
  },
  emits: ['update:visible', 'close'],
  setup(props, { emit }) {
    const loading = ref(false)
    const generating = ref(false)
    const shareLink = ref('')

    // 计算对话框显示状态
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    })

    // 生成分享链接
    const generateLink = async () => {
      generating.value = true
      try {
        // 模拟生成分享链接
        await new Promise(resolve => setTimeout(resolve, 1000))
        shareLink.value = `http://************:5173/shared/${Date.now()}`
        ElMessage.success('分享链接生成成功')
      } catch (error) {
        ElMessage.error('生成分享链接失败')
      } finally {
        generating.value = false
      }
    }

    // 复制链接
    const copyLink = async () => {
      if (!shareLink.value) {
        ElMessage.warning('请先生成分享链接')
        return
      }

      try {
        await navigator.clipboard.writeText(shareLink.value)
        ElMessage.success('链接已复制到剪贴板')
      } catch (error) {
        ElMessage.error('复制失败')
      }
    }

    // 取消分享
    const handleCancel = () => {
      dialogVisible.value = false
      emit('close')
    }

    return {
      dialogVisible,
      loading,
      generating,
      shareLink,
      generateLink,
      copyLink,
      handleCancel
    }
  }
})
</script>

<style scoped>
.share-modal-content {
  min-height: 200px;
}

.share-section {
  margin: 16px 0;
}

.share-link-group {
  margin: 16px 0;
}

.share-options {
  display: flex;
  gap: 16px;
  margin-top: 16px;
  justify-content: center;
}
</style>