<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BI API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>BI API 测试页面</h1>
    
    <div class="test-section">
        <h3>1. 登录测试</h3>
        <button onclick="testLogin()">测试登录</button>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 业务指标API测试</h3>
        <button onclick="testBusinessIndicators()">测试业务指标API</button>
        <div id="biResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 多维度分析API测试</h3>
        <button onclick="testMultidimensionalAnalysis()">测试多维度分析API</button>
        <div id="mdResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. 趋势预测API测试</h3>
        <button onclick="testTrendPrediction()">测试趋势预测API</button>
        <div id="tpResult" class="result"></div>
    </div>

    <script>
        let authToken = '';
        const API_BASE = 'http://************:8000/api/v1';
        
        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${isError ? 'error' : 'success'}`;
        }
        
        async function testLogin() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        phone: '13800138000',
                        password: '123456'
                    })
                });
                
                const data = await response.json();
                if (response.ok) {
                    authToken = data.access_token;
                    showResult('loginResult', { status: '登录成功', token: authToken.substring(0, 20) + '...' });
                } else {
                    showResult('loginResult', data, true);
                }
            } catch (error) {
                showResult('loginResult', { error: error.message }, true);
            }
        }
        
        async function testBusinessIndicators() {
            if (!authToken) {
                showResult('biResult', { error: '请先登录' }, true);
                return;
            }
            
            try {
                const requestData = {
                    start_date: '2024-12-01',
                    end_date: '2024-12-31',
                    indicators: ['revenue', 'order_count', 'customer_count', 'avg_order_value']
                };
                
                const response = await fetch(`${API_BASE}/bi/business-indicators`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(requestData)
                });
                
                const data = await response.json();
                if (response.ok) {
                    showResult('biResult', data);
                } else {
                    showResult('biResult', data, true);
                }
            } catch (error) {
                showResult('biResult', { error: error.message }, true);
            }
        }
        
        async function testMultidimensionalAnalysis() {
            if (!authToken) {
                showResult('mdResult', { error: '请先登录' }, true);
                return;
            }
            
            try {
                const requestData = {
                    data_type: 'orders',
                    dimensions: ['time_monthly'],
                    metrics: ['total_revenue'],
                    start_date: '2024-12-01',
                    end_date: '2024-12-31'
                };
                
                const response = await fetch(`${API_BASE}/bi/multidimensional-analysis`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(requestData)
                });
                
                const data = await response.json();
                if (response.ok) {
                    showResult('mdResult', data);
                } else {
                    showResult('mdResult', data, true);
                }
            } catch (error) {
                showResult('mdResult', { error: error.message }, true);
            }
        }
        
        async function testTrendPrediction() {
            if (!authToken) {
                showResult('tpResult', { error: '请先登录' }, true);
                return;
            }
            
            try {
                const requestData = {
                    prediction_type: 'revenue',
                    historical_months: 6,
                    prediction_months: 3
                };
                
                const response = await fetch(`${API_BASE}/bi/trend-prediction`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(requestData)
                });
                
                const data = await response.json();
                if (response.ok) {
                    showResult('tpResult', data);
                } else {
                    showResult('tpResult', data, true);
                }
            } catch (error) {
                showResult('tpResult', { error: error.message }, true);
            }
        }
    </script>
</body>
</html>
