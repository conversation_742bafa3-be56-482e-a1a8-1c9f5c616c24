# 启动脚本端口配置更新说明

## 📋 更新概述

已成功更新 `./menu.sh` 和 `./manage.sh` 启动脚本，固定了前后端服务的端口配置。

## 🔧 更新内容

### 1. 端口配置标准化

| 服务 | 端口 | 说明 |
|------|------|------|
| 前端服务 | 8080 | Vue.js + Vite 开发服务器 |
| 后端服务 | 8000 | FastAPI + Uvicorn 服务器 |
| 数据库管理 | 8081 | 数据库管理界面 |

### 2. 文件修改详情

#### `./manage.sh` 修改
- **端口配置**: 将 `FRONTEND_PORT` 从 `5173` 更改为 `8080`
- **启动命令**: 将 `npm run serve` 更改为 `npm run dev`
- **保持**: `BACKEND_PORT=8000` 不变

```bash
# 修改前
FRONTEND_PORT=5173
nohup npm run serve > "$LOGS_DIR/frontend.log" 2>&1 &

# 修改后  
FRONTEND_PORT=8080
nohup npm run dev > "$LOGS_DIR/frontend.log" 2>&1 &
```

#### `./menu.sh` 修改
- **前端界面URL**: 从 `http://************:5173` 更改为 `http://************:8080`
- **数据库管理URL**: 从 `http://************:8080` 更改为 `http://************:8081`
- **保持**: API文档URL `http://************:8000/docs` 不变

```bash
# 修改前
1) open_browser "http://************:5173" "前端界面" ;;
3) open_browser "http://************:8080" "数据库管理" ;;

# 修改后
1) open_browser "http://************:8080" "前端界面" ;;
3) open_browser "http://************:8081" "数据库管理" ;;
```

#### `frontend/vite.config.js` 修改
- **添加端口配置**: 在server配置中添加 `port: 8080`

```javascript
// 修改前
server: {
  host: '0.0.0.0',
  proxy: { ... }
}

// 修改后
server: {
  host: '0.0.0.0',
  port: 8080,
  proxy: { ... }
}
```

### 3. 验证配置

#### 配置验证结果
✅ **manage.sh 端口配置**: 
- `FRONTEND_PORT=8080`
- `BACKEND_PORT=8000`

✅ **vite.config.js 端口配置**: 
- `port: 8080`

✅ **run.py 端口配置**: 
- `port=8000`

✅ **menu.sh URL配置**: 
- 前端界面: `http://************:8080`
- API文档: `http://************:8000/docs`
- 数据库管理: `http://************:8081`

## 🚀 使用方法

### 启动服务
```bash
# 使用menu.sh交互式菜单
./menu.sh

# 或直接使用manage.sh命令
./manage.sh start all          # 启动所有服务
./manage.sh start frontend     # 只启动前端
./manage.sh start backend      # 只启动后端
```

### 访问地址
- **前端界面**: http://************:8080
- **API文档**: http://************:8000/docs
- **数据库管理**: http://************:8081

### 查看状态
```bash
./manage.sh status
```

## 📝 注意事项

1. **端口冲突**: 确保8080和8000端口未被其他服务占用
2. **防火墙**: 如需外部访问，请确保防火墙开放相应端口
3. **依赖安装**: 首次启动会自动安装前后端依赖
4. **日志查看**: 可通过 `./manage.sh logs [frontend|backend]` 查看日志

## 🔍 故障排除

### 端口被占用
```bash
# 检查端口占用
netstat -tuln | grep :8080
netstat -tuln | grep :8000

# 杀死占用进程
./manage.sh cleanup
```

### 服务启动失败
```bash
# 查看详细日志
./manage.sh logs frontend 100
./manage.sh logs backend 100
```

## ✅ 更新完成

所有端口配置已标准化，启动脚本已优化。系统现在使用固定的端口配置，便于部署和维护。
