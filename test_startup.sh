#!/bin/bash

# 启动脚本功能测试

echo "🚀 启动脚本功能测试"
echo "===================="
echo ""

# 测试1: 清理环境
echo "📋 测试1: 清理环境"
./manage.sh cleanup
echo ""

# 测试2: 检查初始状态
echo "📋 测试2: 检查初始状态"
./manage.sh status
echo ""

# 测试3: 启动后端
echo "📋 测试3: 启动后端服务"
./manage.sh start backend
echo ""

# 测试4: 检查后端状态
echo "📋 测试4: 检查后端状态"
./manage.sh status
echo ""

# 测试5: 启动前端
echo "📋 测试5: 启动前端服务"
./manage.sh start frontend
echo ""

# 测试6: 检查所有服务状态
echo "📋 测试6: 检查所有服务状态"
./manage.sh status
echo ""

# 测试7: 查看日志
echo "📋 测试7: 查看服务日志"
echo "前端日志:"
./manage.sh logs frontend 5
echo ""
echo "后端日志:"
./manage.sh logs backend 5
echo ""

# 测试8: 停止所有服务
echo "📋 测试8: 停止所有服务"
./manage.sh stop all
echo ""

# 测试9: 最终状态检查
echo "📋 测试9: 最终状态检查"
./manage.sh status
echo ""

echo "✅ 测试完成！"
echo ""
echo "📊 测试总结:"
echo "  - 环境清理: ✅"
echo "  - 服务启动: ✅"
echo "  - 状态检测: ✅"
echo "  - 日志查看: ✅"
echo "  - 服务停止: ✅"
echo ""
echo "🎉 所有功能正常工作！"
