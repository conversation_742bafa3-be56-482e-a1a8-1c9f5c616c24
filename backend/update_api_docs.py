#!/usr/bin/env python3
"""
API文档中文化脚本
批量更新API端点的summary、参数描述等为中文
"""

import os
import re
import glob
from typing import Dict, List

# API端点中文映射
API_SUMMARIES = {
    # 通用操作
    "get_": "获取",
    "create_": "创建",
    "update_": "更新", 
    "delete_": "删除",
    "read_": "查询",
    
    # 具体资源
    "stores": "门店",
    "employees": "员工",
    "customers": "客户",
    "orders": "订单",
    "items": "项目",
    "appointments": "预约",
    "reviews": "评价",
    "shareholders": "股东",
    "dividends": "分红",
    "commissions": "提成",
    "marketing": "营销",
    "business_intelligence": "商业智能",
    "reports": "报表",
    "analytics": "分析",
    "dashboard": "仪表盘",
    "export": "导出",
    "import": "导入",
    "sync": "同步",
    "upload": "上传",
    "download": "下载"
}

# 参数中文映射
PARAM_DESCRIPTIONS = {
    "skip": "跳过的记录数",
    "limit": "返回的记录数", 
    "page": "页码",
    "size": "每页大小",
    "id": "ID",
    "store_id": "门店ID",
    "employee_id": "员工ID",
    "customer_id": "客户ID",
    "order_id": "订单ID",
    "item_id": "项目ID",
    "appointment_id": "预约ID",
    "review_id": "评价ID",
    "shareholder_id": "股东ID",
    "user_id": "用户ID",
    "name": "名称",
    "title": "标题",
    "description": "描述",
    "status": "状态",
    "type": "类型",
    "category": "分类",
    "start_date": "开始日期",
    "end_date": "结束日期",
    "date": "日期",
    "time": "时间",
    "phone": "电话号码",
    "email": "邮箱地址",
    "address": "地址",
    "amount": "金额",
    "price": "价格",
    "quantity": "数量",
    "is_active": "是否激活",
    "is_deleted": "是否删除",
    "is_enabled": "是否启用",
    "created_at": "创建时间",
    "updated_at": "更新时间",
    "role": "角色",
    "permission": "权限",
    "level": "等级",
    "score": "评分",
    "rating": "评级",
    "comment": "评论",
    "note": "备注",
    "remark": "备注",
    "tag": "标签",
    "tags": "标签列表",
    "keyword": "关键词",
    "keywords": "关键词列表",
    "search": "搜索关键词",
    "query": "查询条件",
    "filter": "筛选条件",
    "sort": "排序字段",
    "order": "排序方式",
    "format": "格式",
    "version": "版本",
    "source": "来源",
    "target": "目标",
    "method": "方法",
    "action": "操作",
    "operation": "操作类型",
    "config": "配置",
    "setting": "设置",
    "option": "选项",
    "value": "值",
    "data": "数据",
    "content": "内容",
    "file": "文件",
    "image": "图片",
    "url": "链接地址",
    "path": "路径",
    "code": "编码",
    "token": "令牌",
    "key": "键",
    "secret": "密钥"
}

def update_query_params(content: str) -> str:
    """更新Query参数的description"""
    
    # 匹配Query参数模式
    query_pattern = r'(\w+):\s*Optional\[(\w+)\]\s*=\s*Query\(([^)]+)\)'
    
    def replace_query(match):
        param_name = match.group(1)
        param_type = match.group(2)
        query_args = match.group(3)
        
        # 如果已经有中文description，跳过
        if 'description=' in query_args and any(ord(c) > 127 for c in query_args):
            return match.group(0)
        
        # 获取中文描述
        chinese_desc = PARAM_DESCRIPTIONS.get(param_name, param_name)
        
        # 构建新的Query参数
        if 'description=' in query_args:
            # 替换现有的description
            new_args = re.sub(r'description="[^"]*"', f'description="{chinese_desc}"', query_args)
        else:
            # 添加description
            new_args = f'{query_args}, description="{chinese_desc}"'
        
        return f'{param_name}: Optional[{param_type}] = Query({new_args})'
    
    return re.sub(query_pattern, replace_query, content)

def update_path_params(content: str) -> str:
    """更新Path参数的description"""
    
    # 匹配Path参数模式
    path_pattern = r'(\w+):\s*(\w+)\s*=\s*Path\(([^)]+)\)'
    
    def replace_path(match):
        param_name = match.group(1)
        param_type = match.group(2)
        path_args = match.group(3)
        
        # 如果已经有中文description，跳过
        if 'description=' in path_args and any(ord(c) > 127 for c in path_args):
            return match.group(0)
        
        # 获取中文描述
        chinese_desc = PARAM_DESCRIPTIONS.get(param_name, param_name)
        
        # 构建新的Path参数
        if 'description=' in path_args:
            # 替换现有的description
            new_args = re.sub(r'description="[^"]*"', f'description="{chinese_desc}"', path_args)
        else:
            # 添加description
            new_args = f'{path_args}, description="{chinese_desc}"'
        
        return f'{param_name}: {param_type} = Path({new_args})'
    
    return re.sub(path_pattern, replace_path, content)

def update_api_file(file_path: str) -> bool:
    """更新单个API文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 更新Query参数
        content = update_query_params(content)
        
        # 更新Path参数
        content = update_path_params(content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 已更新: {file_path}")
            return True
        else:
            print(f"⏭️  无需更新: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ 更新失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始更新API文档中文化...")
    
    # 获取所有API端点文件
    api_files = glob.glob("app/api/api_v1/endpoints/*.py")
    
    if not api_files:
        print("❌ 未找到API端点文件")
        return
    
    updated_count = 0
    total_count = len(api_files)
    
    for file_path in api_files:
        if update_api_file(file_path):
            updated_count += 1
    
    print(f"\n📊 更新完成:")
    print(f"   总文件数: {total_count}")
    print(f"   已更新: {updated_count}")
    print(f"   无需更新: {total_count - updated_count}")
    print("\n🎉 API文档中文化完成!")

if __name__ == "__main__":
    main()
