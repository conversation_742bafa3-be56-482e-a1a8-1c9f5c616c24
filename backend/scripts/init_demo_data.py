#!/usr/bin/env python3
"""
初始化演示数据脚本
为按摩推拿连锁管理系统添加真实的模拟经营数据
"""

import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal
import random

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal, engine
from app.models import *

# 模拟数据
NAMES = ["张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十", "郑十一", "王十二",
         "陈小明", "刘小红", "黄小华", "林小丽", "杨小强", "朱小美", "徐小刚", "马小芳", "胡小军", "郭小燕"]

PHONE_PREFIXES = ["138", "139", "150", "151", "152", "158", "159", "188", "189"]

def generate_phone():
    """生成手机号"""
    prefix = random.choice(PHONE_PREFIXES)
    suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
    return prefix + suffix

def generate_name():
    """生成姓名"""
    return random.choice(NAMES)

def generate_address():
    """生成地址"""
    cities = ["广州市", "深圳市", "珠海市", "佛山市"]
    districts = ["天河区", "海珠区", "朝阳区", "白云区", "番禺区"]
    streets = ["中山路", "解放路", "人民路", "建设路", "和平路", "友谊路"]
    return f"{random.choice(cities)}{random.choice(districts)}{random.choice(streets)}{random.randint(1, 999)}号"

def create_demo_data():
    """创建演示数据"""
    db = SessionLocal()
    
    try:
        print("🚀 开始创建演示数据...")
        
        # 1. 创建门店数据
        print("📍 创建门店数据...")
        stores = create_stores(db)
        
        # 2. 创建员工数据
        print("👥 创建员工数据...")
        employees = create_employees(db, stores)
        
        # 3. 创建集团项目
        print("🛍️ 创建项目数据...")
        group_items = create_group_items(db)
        
        # 4. 创建门店项目
        print("🏪 创建门店项目数据...")
        store_items = create_store_items(db, stores, group_items)
        
        # 5. 创建客户数据
        print("👤 创建客户数据...")
        customers = create_customers(db)
        
        # 6. 创建订单数据（最近3个月）
        print("📋 创建订单数据...")
        orders = create_orders(db, stores, employees, customers, store_items)
        
        # 7. 创建股东数据
        print("💰 创建股东数据...")
        shareholders = create_shareholders(db)
        
        # 8. 创建股权结构
        print("📊 创建股权结构...")
        create_shareholder_structures(db, stores, shareholders)
        
        # 9. 创建绩效数据
        print("📈 创建绩效数据...")
        create_performance_data(db, stores, employees)
        
        db.commit()
        print("✅ 演示数据创建完成！")
        
        # 打印统计信息
        print_statistics(db)
        
    except Exception as e:
        print(f"❌ 创建演示数据失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def create_stores(db: Session):
    """创建门店数据"""
    stores_data = [
        {
            "name": "总店",
            "code": "HQ001",
            "address": "广州市天河区天河路123号",
            "phone": "020-12345678",
            "status": "active",
            "business_hours": "10:00-22:00, 周末: 10:00-23:00"
        },
        {
            "name": "朝阳店",
            "code": "CY001", 
            "address": "广州市朝阳区建国路456号",
            "phone": "020-87654321",
            "status": "active",
            "business_hours": "09:00-21:00"
        },
        {
            "name": "海珠店",
            "code": "HZ001",
            "address": "广州市海珠区新港路789号",
            "phone": "020-11223344",
            "status": "active", 
            "business_hours": "10:00-22:00"
        }
    ]
    
    stores = []
    for store_data in stores_data:
        store = Store(**store_data)
        db.add(store)
        stores.append(store)
    
    db.flush()  # 获取ID
    return stores

def create_employees(db: Session, stores):
    """创建员工数据"""
    employees_data = [
        # 总店员工
        {"name": "张店长", "phone": "***********", "password": "123456", "role": "manager", "position": "店长", "store_id": stores[0].id},
        {"name": "李技师", "phone": "***********", "password": "123456", "role": "technician", "position": "技师", "store_id": stores[0].id},
        {"name": "王技师", "phone": "***********", "password": "123456", "role": "technician", "position": "技师", "store_id": stores[0].id},
        {"name": "赵收银", "phone": "***********", "password": "123456", "role": "staff", "position": "收银", "store_id": stores[0].id},
        
        # 朝阳店员工
        {"name": "黄永江", "phone": "17707071701", "password": "123456", "role": "manager", "position": "店长", "store_id": stores[1].id},
        {"name": "陈技师", "phone": "13800138006", "password": "123456", "role": "technician", "position": "技师", "store_id": stores[1].id},
        {"name": "刘助理", "phone": "13800138007", "password": "123456", "role": "staff", "position": "助理", "store_id": stores[1].id},
        
        # 海珠店员工
        {"name": "吴店长", "phone": "13800138008", "password": "123456", "role": "manager", "position": "店长", "store_id": stores[2].id},
        {"name": "周技师", "phone": "13800138009", "password": "123456", "role": "technician", "position": "技师", "store_id": stores[2].id},
        {"name": "郑收银", "phone": "13800138010", "password": "123456", "role": "staff", "position": "收银", "store_id": stores[2].id},
    ]
    
    employees = []
    for emp_data in employees_data:
        # 随机入职日期（6个月到2年前）
        days_ago = random.randint(180, 730)
        emp_data["entry_date"] = datetime.now() - timedelta(days=days_ago)
        employee = Employee(**emp_data)
        db.add(employee)
        employees.append(employee)
    
    db.flush()
    
    # 设置门店经理
    stores[0].manager_id = employees[0].id  # 张店长
    stores[1].manager_id = employees[4].id  # 黄永江
    stores[2].manager_id = employees[7].id  # 吴店长
    
    return employees

def create_group_items(db: Session):
    """创建集团项目"""
    items_data = [
        {"name": "全身按摩", "category": "服务", "description": "全身放松按摩", "group_price": Decimal("198.00")},
        {"name": "足底按摩", "category": "服务", "description": "足底穴位按摩", "group_price": Decimal("128.00")},
        {"name": "肩颈按摩", "category": "服务", "description": "肩颈放松按摩", "group_price": Decimal("98.00")},
        {"name": "精油按摩", "category": "服务", "description": "精油芳疗按摩", "group_price": Decimal("228.00")},
        {"name": "拔罐", "category": "服务", "description": "传统拔罐疗法", "group_price": Decimal("88.00")},
        {"name": "刮痧", "category": "服务", "description": "传统刮痧疗法", "group_price": Decimal("68.00")},
        {"name": "艾灸", "category": "服务", "description": "艾草灸疗", "group_price": Decimal("108.00")},
        {"name": "推拿", "category": "服务", "description": "中医推拿", "group_price": Decimal("158.00")},
    ]
    
    group_items = []
    for item_data in items_data:
        item = GroupItem(**item_data)
        db.add(item)
        group_items.append(item)
    
    db.flush()
    return group_items

def create_store_items(db: Session, stores, group_items):
    """创建门店项目"""
    store_items = []
    
    for store in stores:
        for group_item in group_items:
            # 门店价格在集团价格基础上有小幅调整
            price_adjustment = random.uniform(0.9, 1.1)
            store_price = group_item.group_price * Decimal(str(price_adjustment))
            store_price = store_price.quantize(Decimal('0.01'))
            
            store_item = StoreItem(
                store_id=store.id,
                group_item_id=group_item.id,
                store_price=store_price,
                member_price=store_price * Decimal('0.9'),  # 会员价9折
                is_enabled=random.choice([True, True, True, False])  # 75%概率启用
            )
            db.add(store_item)
            store_items.append(store_item)
    
    db.flush()
    return store_items

def create_customers(db: Session):
    """创建客户数据"""
    customers = []
    
    # 创建100个客户
    for i in range(100):
        # 随机生日（18-70岁）
        age_days = random.randint(18*365, 70*365)
        birthday = datetime.now() - timedelta(days=age_days)

        customer = Customer(
            name=generate_name(),
            phone=generate_phone(),
            gender=random.choice(['male', 'female']),
            birthday=birthday,
            address=generate_address(),
            source=random.choice(['微信', '朋友推荐', '网络广告', '路过', '老客户']),
            member_level=random.choice(['normal', 'silver', 'gold', 'platinum']),
            status='active',
            remark=f"客户备注{i}" if random.random() < 0.3 else None
        )
        db.add(customer)
        customers.append(customer)
    
    db.flush()
    return customers

def create_orders(db: Session, stores, employees, customers, store_items):
    """创建订单数据（最近3个月）"""
    orders = []

    # 获取每个门店的员工和项目
    store_employees = {}
    store_store_items = {}

    for emp in employees:
        if emp.store_id not in store_employees:
            store_employees[emp.store_id] = []
        store_employees[emp.store_id].append(emp)

    for item in store_items:
        if item.store_id not in store_store_items:
            store_store_items[item.store_id] = []
        if item.is_enabled:  # 只包含启用的项目
            store_store_items[item.store_id].append(item)

    # 生成最近3个月的订单
    start_date = datetime.now() - timedelta(days=90)

    order_number = 1
    for day in range(90):
        current_date = start_date + timedelta(days=day)

        # 每天每个门店生成2-8个订单
        for store in stores:
            daily_orders = random.randint(2, 8)

            for _ in range(daily_orders):
                # 随机选择客户（80%概率有客户，20%匿名）
                customer = random.choice(customers) if random.random() < 0.8 else None

                # 随机选择员工（收银员）
                cashier = random.choice(store_employees[store.id])

                # 生成订单号
                order_num = f"ORD{current_date.strftime('%Y%m%d')}{order_number:04d}"
                order_number += 1

                # 随机选择1-3个项目
                selected_items = random.sample(
                    store_store_items[store.id],
                    min(random.randint(1, 3), len(store_store_items[store.id]))
                )

                # 计算订单金额
                total_amount = sum(item.store_price for item in selected_items)
                discount_amount = total_amount * Decimal(str(random.uniform(0, 0.2)))  # 0-20%折扣
                actual_amount = total_amount - discount_amount

                # 创建订单
                order = Order(
                    store_id=store.id,
                    customer_id=customer.id if customer else None,
                    employee_id=cashier.id,
                    order_number=order_num,
                    order_time=current_date + timedelta(
                        hours=random.randint(9, 21),
                        minutes=random.randint(0, 59)
                    ),
                    total_amount=total_amount,
                    discount_amount=discount_amount,
                    actual_amount=actual_amount,
                    payment_method=random.choice(['wechat', 'alipay', 'cash', 'card']),
                    status='completed',
                    remark=f"订单备注{order_number}" if random.random() < 0.2 else None
                )
                db.add(order)
                orders.append(order)

                # 创建订单明细
                for item in selected_items:
                    # 随机选择服务技师
                    technicians = [emp for emp in store_employees[store.id] if emp.role == 'technician']
                    service_employee = random.choice(technicians) if technicians else None

                    order_item = OrderItem(
                        order_id=order.id,
                        store_item_id=item.id,
                        service_employee_id=service_employee.id if service_employee else None,
                        quantity=1,
                        unit_price=item.store_price,
                        discount_price=item.store_price * Decimal('0.9') if random.random() < 0.3 else item.store_price,
                        subtotal=item.store_price
                    )
                    db.add(order_item)

    db.flush()
    return orders

def create_shareholders(db: Session):
    """创建股东数据"""
    shareholders_data = [
        {
            "name": "张三",
            "phone": "13900000001",
            "id_card": "******************",
            "address": "广州市天河区",
            "remark": "创始人股东"
        },
        {
            "name": "李四",
            "phone": "13900000002",
            "id_card": "******************",
            "address": "广州市海珠区",
            "remark": "投资人股东"
        },
        {
            "name": "王五",
            "phone": "13900000003",
            "id_card": "******************",
            "address": "广州市朝阳区",
            "remark": "技术股东"
        }
    ]

    shareholders = []
    for data in shareholders_data:
        shareholder = Shareholder(**data)
        db.add(shareholder)
        shareholders.append(shareholder)

    db.flush()
    return shareholders

def create_shareholder_structures(db: Session, stores, shareholders):
    """创建股权结构"""
    # 为每个门店创建股权结构
    for store in stores:
        # 总店：张三50%，李四30%，王五20%
        # 其他店：张三40%，李四35%，王五25%
        if store.name == "总店":
            shares = [50.0, 30.0, 20.0]
        else:
            shares = [40.0, 35.0, 25.0]

        for i, shareholder in enumerate(shareholders):
            structure = ShareholderStructure(
                store_id=store.id,
                shareholder_id=shareholder.id,
                share_percentage=Decimal(str(shares[i])),
                investment_amount=Decimal(str(shares[i] * 10000)),  # 假设总投资100万
                effective_date=datetime.now() - timedelta(days=365)
            )
            db.add(structure)

def create_performance_data(db: Session, stores, employees):
    """创建绩效数据"""
    # 为最近3个月创建员工绩效数据
    for month_offset in range(3):
        performance_date = datetime.now() - timedelta(days=30 * month_offset)

        for employee in employees:
            if employee.role in ['technician', 'manager']:
                performance = EmployeePerformance(
                    employee_id=employee.id,
                    store_id=employee.store_id,
                    period_start=performance_date.replace(day=1),
                    period_end=performance_date.replace(day=28),
                    service_count=random.randint(20, 80),
                    total_revenue=Decimal(str(random.uniform(5000, 25000))),
                    commission_amount=Decimal(str(random.uniform(500, 2500))),
                    customer_rating=Decimal(str(random.uniform(4.0, 5.0))),
                    attendance_rate=Decimal(str(random.uniform(0.85, 1.0)))
                )
                db.add(performance)

        # 创建门店绩效数据
        for store in stores:
            store_performance = StorePerformance(
                store_id=store.id,
                period_start=performance_date.replace(day=1),
                period_end=performance_date.replace(day=28),
                total_revenue=Decimal(str(random.uniform(50000, 150000))),
                total_orders=random.randint(200, 600),
                customer_count=random.randint(150, 400),
                average_order_value=Decimal(str(random.uniform(80, 200))),
                customer_satisfaction=Decimal(str(random.uniform(4.2, 4.9)))
            )
            db.add(store_performance)

def print_statistics(db: Session):
    """打印统计信息"""
    print("\n📊 数据统计:")
    print(f"门店数量: {db.query(Store).count()}")
    print(f"员工数量: {db.query(Employee).count()}")
    print(f"客户数量: {db.query(Customer).count()}")
    print(f"项目数量: {db.query(GroupItem).count()}")
    print(f"订单数量: {db.query(Order).count()}")
    print(f"股东数量: {db.query(Shareholder).count()}")

    # 计算总营收
    total_revenue = db.query(Order).with_entities(
        db.func.sum(Order.actual_amount)
    ).scalar() or 0
    print(f"总营收: ¥{total_revenue:,.2f}")

if __name__ == "__main__":
    create_demo_data()
