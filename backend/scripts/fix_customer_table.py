#!/usr/bin/env python3
"""
修复客户表结构脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.db.session import SessionLocal


def fix_customer_table():
    """修复客户表结构"""
    db = SessionLocal()
    
    try:
        print("检查customers表结构...")
        
        # 检查表是否存在tags列 (PostgreSQL)
        result = db.execute(text("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'customers' AND table_schema = 'public'
        """))
        columns = [row[0] for row in result]
        print(f"当前列: {columns}")

        # 如果没有tags列，添加它
        if 'tags' not in columns:
            print("添加tags列...")
            db.execute(text("ALTER TABLE customers ADD COLUMN tags JSON"))
            db.commit()
            print("tags列添加成功！")
        else:
            print("tags列已存在")
        
        print("客户表结构修复完成！")
        
    except Exception as e:
        print(f"修复客户表时出错: {e}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    fix_customer_table()
