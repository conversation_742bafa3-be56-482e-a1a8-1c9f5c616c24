#!/usr/bin/env python3
"""
通过API创建演示数据
"""

import requests
import json
from datetime import datetime, timedelta
import random

BASE_URL = "http://************:8000/api/v1"

def create_stores():
    """创建门店"""
    stores_data = [
        {
            "name": "总店",
            "address": "广州市天河区天河路123号",
            "phone": "020-12345678",
            "business_hours": "10:00-22:00, 周末: 10:00-23:00",
            "status": "active"
        },
        {
            "name": "朝阳店", 
            "address": "广州市朝阳区建国路456号",
            "phone": "020-87654321",
            "business_hours": "09:00-21:00",
            "status": "active"
        },
        {
            "name": "海珠店",
            "address": "广州市海珠区新港路789号", 
            "phone": "020-11223344",
            "business_hours": "10:00-22:00",
            "status": "active"
        }
    ]
    
    created_stores = []
    for store_data in stores_data:
        try:
            response = requests.post(f"{BASE_URL}/stores/", json=store_data)
            if response.status_code == 200:
                store = response.json()
                created_stores.append(store)
                print(f"✅ 创建门店成功: {store['name']} (ID: {store['id']})")
            else:
                print(f"❌ 创建门店失败: {store_data['name']} - {response.text}")
        except Exception as e:
            print(f"❌ 创建门店异常: {store_data['name']} - {e}")
    
    return created_stores

def create_employees(stores):
    """创建员工"""
    employees_data = [
        # 总店员工
        {"name": "张店长", "phone": "***********", "password": "123456", "role": "manager", "position": "店长"},
        {"name": "李技师", "phone": "***********", "password": "123456", "role": "technician", "position": "技师"},
        {"name": "王技师", "phone": "13800138003", "password": "123456", "role": "technician", "position": "技师"},
        {"name": "赵收银", "phone": "13800138004", "password": "123456", "role": "staff", "position": "收银"},
        
        # 朝阳店员工
        {"name": "黄永江", "phone": "17707071701", "password": "123456", "role": "manager", "position": "店长"},
        {"name": "陈技师", "phone": "13800138006", "password": "123456", "role": "technician", "position": "技师"},
        {"name": "刘助理", "phone": "13800138007", "password": "123456", "role": "staff", "position": "助理"},
        
        # 海珠店员工
        {"name": "吴店长", "phone": "13800138008", "password": "123456", "role": "manager", "position": "店长"},
        {"name": "周技师", "phone": "13800138009", "password": "123456", "role": "technician", "position": "技师"},
        {"name": "郑收银", "phone": "13800138010", "password": "123456", "role": "staff", "position": "收银"},
    ]
    
    # 为员工分配门店
    store_assignments = [0, 0, 0, 0, 1, 1, 1, 2, 2, 2]  # 对应门店索引
    
    created_employees = []
    for i, emp_data in enumerate(employees_data):
        if i < len(stores):
            emp_data["store_id"] = stores[store_assignments[i]]["id"]
            
        try:
            response = requests.post(f"{BASE_URL}/employees/", json=emp_data)
            if response.status_code == 200:
                employee = response.json()
                created_employees.append(employee)
                print(f"✅ 创建员工成功: {employee['name']} (ID: {employee['id']})")
            else:
                print(f"❌ 创建员工失败: {emp_data['name']} - {response.text}")
        except Exception as e:
            print(f"❌ 创建员工异常: {emp_data['name']} - {e}")
    
    return created_employees

def create_customers():
    """创建客户"""
    customers_data = []
    
    # 创建50个客户
    names = ["张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十", "郑十一", "王十二",
             "陈小明", "刘小红", "黄小华", "林小丽", "杨小强", "朱小美", "徐小刚", "马小芳", "胡小军", "郭小燕",
             "何小东", "高小西", "梁小南", "宋小北", "唐小中", "韩小外", "冯小内", "于小上", "董小下", "薛小左",
             "程小右", "曾小前", "彭小后", "吕小新", "苏小旧", "卢小大", "蒋小小", "蔡小多", "贾小少", "丁小好",
             "魏小坏", "薛小美", "叶小丑", "阎小高", "余小矮", "潘小胖", "杜小瘦", "戴小黑", "夏小白", "钟小灰"]
    
    for i in range(50):
        customer_data = {
            "name": names[i],
            "phone": f"138{8000+i:04d}",
            "gender": random.choice(["male", "female"]),
            "address": f"广州市天河区测试路{i+1}号",
            "source": random.choice(["微信", "朋友推荐", "网络广告", "路过", "老客户"]),
            "member_level": random.choice(["normal", "silver", "gold", "platinum"]),
            "status": "active"
        }
        customers_data.append(customer_data)
    
    created_customers = []
    for customer_data in customers_data:
        try:
            response = requests.post(f"{BASE_URL}/customers/", json=customer_data)
            if response.status_code == 200:
                customer = response.json()
                created_customers.append(customer)
                print(f"✅ 创建客户成功: {customer['name']} (ID: {customer['id']})")
            else:
                print(f"❌ 创建客户失败: {customer_data['name']} - {response.text}")
        except Exception as e:
            print(f"❌ 创建客户异常: {customer_data['name']} - {e}")
    
    return created_customers

def create_group_items():
    """创建集团项目"""
    items_data = [
        {"name": "全身按摩", "category": "服务", "description": "全身放松按摩", "group_price": 198.00},
        {"name": "足底按摩", "category": "服务", "description": "足底穴位按摩", "group_price": 128.00},
        {"name": "肩颈按摩", "category": "服务", "description": "肩颈放松按摩", "group_price": 98.00},
        {"name": "精油按摩", "category": "服务", "description": "精油芳疗按摩", "group_price": 228.00},
        {"name": "拔罐", "category": "服务", "description": "传统拔罐疗法", "group_price": 88.00},
        {"name": "刮痧", "category": "服务", "description": "传统刮痧疗法", "group_price": 68.00},
        {"name": "艾灸", "category": "服务", "description": "艾草灸疗", "group_price": 108.00},
        {"name": "推拿", "category": "服务", "description": "中医推拿", "group_price": 158.00},
    ]
    
    created_items = []
    for item_data in items_data:
        try:
            response = requests.post(f"{BASE_URL}/group-items/", json=item_data)
            if response.status_code == 200:
                item = response.json()
                created_items.append(item)
                print(f"✅ 创建集团项目成功: {item['name']} (ID: {item['id']})")
            else:
                print(f"❌ 创建集团项目失败: {item_data['name']} - {response.text}")
        except Exception as e:
            print(f"❌ 创建集团项目异常: {item_data['name']} - {e}")
    
    return created_items

def create_shareholders():
    """创建股东"""
    shareholders_data = [
        {
            "name": "张三",
            "phone": "13900000001",
            "id_card": "******************",
            "address": "广州市天河区",
            "remark": "创始人股东"
        },
        {
            "name": "李四",
            "phone": "13900000002", 
            "id_card": "******************",
            "address": "广州市海珠区",
            "remark": "投资人股东"
        },
        {
            "name": "王五",
            "phone": "13900000003",
            "id_card": "******************", 
            "address": "广州市朝阳区",
            "remark": "技术股东"
        }
    ]
    
    created_shareholders = []
    for shareholder_data in shareholders_data:
        try:
            response = requests.post(f"{BASE_URL}/shareholders/", json=shareholder_data)
            if response.status_code == 200:
                shareholder = response.json()
                created_shareholders.append(shareholder)
                print(f"✅ 创建股东成功: {shareholder['name']} (ID: {shareholder['id']})")
            else:
                print(f"❌ 创建股东失败: {shareholder_data['name']} - {response.text}")
        except Exception as e:
            print(f"❌ 创建股东异常: {shareholder_data['name']} - {e}")
    
    return created_shareholders

def main():
    """主函数"""
    print("🚀 开始创建演示数据...")
    
    # 1. 创建门店
    print("\n📍 创建门店...")
    stores = create_stores()
    
    # 2. 创建员工
    print("\n👥 创建员工...")
    employees = create_employees(stores)
    
    # 3. 创建客户
    print("\n👤 创建客户...")
    customers = create_customers()
    
    # 4. 创建集团项目
    print("\n🛍️ 创建集团项目...")
    group_items = create_group_items()
    
    # 5. 创建股东
    print("\n💰 创建股东...")
    shareholders = create_shareholders()
    
    print(f"\n✅ 演示数据创建完成!")
    print(f"📊 统计:")
    print(f"  门店: {len(stores)}")
    print(f"  员工: {len(employees)}")
    print(f"  客户: {len(customers)}")
    print(f"  项目: {len(group_items)}")
    print(f"  股东: {len(shareholders)}")

if __name__ == "__main__":
    main()
