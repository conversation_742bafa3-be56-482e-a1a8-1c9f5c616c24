#!/usr/bin/env python3
"""
生成测试数据脚本
用于为BI分析创建足够的历史订单数据
"""

import sys
import os
from datetime import datetime, timedelta
import random
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.operation import Order, OrderItem
from app.models.store import Store
from app.models.customer import Customer
from app.models.employee import Employee
from app.models.store_item import StoreItem
from app.models.group_item import GroupItem


def generate_order_number():
    """生成订单编号"""
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    random_suffix = random.randint(1000, 9999)
    return f"ORD{timestamp}{random_suffix}"


def create_test_orders(db: Session, num_orders: int = 100):
    """创建测试订单数据"""
    
    # 获取现有的门店、客户、员工和商品数据
    stores = db.query(Store).all()
    customers = db.query(Customer).all()
    employees = db.query(Employee).all()
    store_items = db.query(StoreItem).all()
    
    if not stores:
        print("错误：没有找到门店数据，请先创建门店")
        return
    
    if not employees:
        print("错误：没有找到员工数据，请先创建员工")
        return
    
    if not store_items:
        print("错误：没有找到商品数据，请先创建商品")
        return
    
    print(f"开始生成 {num_orders} 个测试订单...")
    
    # 定义服务项目和价格
    services = [
        {"name": "全身按摩", "price": 198.00},
        {"name": "足底按摩", "price": 128.00},
        {"name": "肩颈按摩", "price": 88.00},
        {"name": "头部按摩", "price": 68.00},
        {"name": "精油按摩", "price": 258.00},
        {"name": "中医推拿", "price": 168.00},
        {"name": "拔罐疗法", "price": 98.00},
        {"name": "艾灸治疗", "price": 118.00},
    ]
    
    # 支付方式
    payment_methods = ["cash", "wechat", "alipay", "card"]
    
    created_orders = 0
    
    for i in range(num_orders):
        try:
            # 随机选择时间（过去90天内）
            days_ago = random.randint(0, 90)
            hours_ago = random.randint(0, 23)
            minutes_ago = random.randint(0, 59)
            
            order_time = datetime.now() - timedelta(
                days=days_ago, 
                hours=hours_ago, 
                minutes=minutes_ago
            )
            
            # 随机选择门店、员工
            store = random.choice(stores)
            employee = random.choice(employees)
            
            # 随机选择客户（70%概率有客户，30%匿名）
            customer = random.choice(customers) if random.random() < 0.7 else None
            
            # 随机选择1-3个服务项目
            num_services = random.randint(1, 3)
            selected_services = random.sample(services, num_services)
            
            # 计算总金额
            total_amount = sum(service["price"] for service in selected_services)
            
            # 随机折扣（0-20%）
            discount_rate = random.uniform(0, 0.2)
            discount_amount = total_amount * discount_rate
            actual_amount = total_amount - discount_amount
            
            # 创建订单
            order = Order(
                store_id=store.id,
                customer_id=customer.id if customer else None,
                employee_id=employee.id,
                order_number=generate_order_number(),
                order_time=order_time,
                total_amount=Decimal(str(total_amount)),
                discount_amount=Decimal(str(discount_amount)),
                actual_amount=Decimal(str(actual_amount)),
                payment_method=random.choice(payment_methods),
                status="completed",
                payment_status="paid",
                total_paid=Decimal(str(actual_amount)),
                final_amount=Decimal(str(actual_amount)),
                created_at=order_time,
                updated_at=order_time
            )
            
            db.add(order)
            db.flush()  # 获取订单ID
            
            # 创建订单明细
            for service in selected_services:
                # 简化：直接使用第一个可用的商品
                store_item = store_items[0] if store_items else None
                
                if store_item:
                    order_item = OrderItem(
                        order_id=order.id,
                        store_item_id=store_item.id,
                        quantity=1,
                        unit_price=Decimal(str(service["price"])),
                        discount_price=Decimal("0"),
                        subtotal=Decimal(str(service["price"])),
                        created_at=order_time,
                        updated_at=order_time
                    )
                    db.add(order_item)
            
            created_orders += 1
            
            if created_orders % 10 == 0:
                print(f"已创建 {created_orders} 个订单...")
                
        except Exception as e:
            print(f"创建订单 {i+1} 时出错: {e}")
            db.rollback()
            continue
    
    try:
        db.commit()
        print(f"成功创建了 {created_orders} 个测试订单！")
    except Exception as e:
        print(f"提交数据时出错: {e}")
        db.rollback()


def create_test_customers(db: Session, num_customers: int = 20):
    """创建测试客户数据"""
    
    print(f"开始生成 {num_customers} 个测试客户...")
    
    # 客户姓名列表
    first_names = ["张", "李", "王", "刘", "陈", "杨", "赵", "黄", "周", "吴", "徐", "孙", "胡", "朱", "高"]
    last_names = ["伟", "芳", "娜", "秀英", "敏", "静", "丽", "强", "磊", "军", "洋", "勇", "艳", "杰", "涛", "明", "超", "秀兰"]
    
    created_customers = 0
    
    for i in range(num_customers):
        try:
            # 随机生成姓名
            name = random.choice(first_names) + random.choice(last_names)
            
            # 随机生成手机号
            phone = f"1{random.randint(3, 9)}{random.randint(0, 9):08d}"
            
            # 随机性别
            gender = random.choice(["male", "female"])
            
            # 随机注册时间（过去180天内）
            days_ago = random.randint(0, 180)
            created_at = datetime.now() - timedelta(days=days_ago)
            
            customer = Customer(
                name=name,
                phone=phone,
                gender=gender,
                created_at=created_at,
                updated_at=created_at
            )
            
            db.add(customer)
            created_customers += 1
            
        except Exception as e:
            print(f"创建客户 {i+1} 时出错: {e}")
            continue
    
    try:
        db.commit()
        print(f"成功创建了 {created_customers} 个测试客户！")
    except Exception as e:
        print(f"提交客户数据时出错: {e}")
        db.rollback()


def main():
    """主函数"""
    db = SessionLocal()
    
    try:
        print("=== 按摩推拿连锁管理系统 - 测试数据生成器 ===")
        print()
        
        # 创建测试客户
        create_test_customers(db, 30)
        print()
        
        # 创建测试订单
        create_test_orders(db, 200)
        print()
        
        print("=== 测试数据生成完成！===")
        print("现在可以测试BI分析功能了。")
        
    except Exception as e:
        print(f"生成测试数据时出错: {e}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    main()
