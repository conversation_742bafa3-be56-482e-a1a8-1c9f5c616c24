#!/usr/bin/env python3
"""
简化版演示数据脚本
"""

import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal
import random

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from sqlalchemy.orm import Session
    from app.db.session import SessionLocal
    from app.models.store import Store
    from app.models.employee import Employee
    from app.models.group_item import GroupItem
    from app.models.store_item import StoreItem
    from app.models.customer import Customer
    from app.models.operation import Order, OrderItem
    from app.models.shareholder import Shareholder
    from app.models.shareholder_structure import ShareholderStructure
    from app.models.performance import EmployeePerformance, StorePerformance
    print("✅ 模块导入成功")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    sys.exit(1)

def create_basic_data():
    """创建基础演示数据"""
    db = SessionLocal()
    
    try:
        print("🚀 开始创建基础演示数据...")
        
        # 检查是否已有数据
        existing_stores = db.query(Store).count()
        if existing_stores > 0:
            print(f"⚠️ 数据库中已有 {existing_stores} 个门店，跳过数据创建")
            return
        
        # 1. 创建门店
        print("📍 创建门店...")
        store1 = Store(
            name="总店",
            code="HQ001", 
            address="广州市天河区天河路123号",
            phone="020-12345678",
            status="active",
            business_hours="10:00-22:00, 周末: 10:00-23:00"
        )
        store2 = Store(
            name="朝阳店",
            code="CY001",
            address="广州市朝阳区建国路456号", 
            phone="020-87654321",
            status="active",
            business_hours="09:00-21:00"
        )
        
        db.add(store1)
        db.add(store2)
        db.flush()
        
        # 2. 创建员工
        print("👥 创建员工...")
        emp1 = Employee(
            name="张店长",
            phone="***********",
            password="123456",
            role="manager",
            position="店长",
            store_id=store1.id,
            entry_date=datetime.now() - timedelta(days=365)
        )
        emp2 = Employee(
            name="李技师", 
            phone="***********",
            password="123456",
            role="technician",
            position="技师",
            store_id=store1.id,
            entry_date=datetime.now() - timedelta(days=300)
        )
        emp3 = Employee(
            name="黄永江",
            phone="***********", 
            password="123456",
            role="manager",
            position="店长",
            store_id=store2.id,
            entry_date=datetime.now() - timedelta(days=200)
        )
        
        db.add(emp1)
        db.add(emp2) 
        db.add(emp3)
        db.flush()
        
        # 设置门店经理
        store1.manager_id = emp1.id
        store2.manager_id = emp3.id
        
        # 3. 创建项目
        print("🛍️ 创建项目...")
        item1 = GroupItem(
            name="全身按摩",
            category="服务",
            description="全身放松按摩",
            group_price=Decimal("198.00")
        )
        item2 = GroupItem(
            name="足底按摩",
            category="服务", 
            description="足底穴位按摩",
            group_price=Decimal("128.00")
        )
        item3 = GroupItem(
            name="肩颈按摩",
            category="服务",
            description="肩颈放松按摩", 
            group_price=Decimal("98.00")
        )
        
        db.add(item1)
        db.add(item2)
        db.add(item3)
        db.flush()
        
        # 4. 创建门店项目
        print("🏪 创建门店项目...")
        for store in [store1, store2]:
            for item in [item1, item2, item3]:
                store_item = StoreItem(
                    store_id=store.id,
                    group_item_id=item.id,
                    store_price=item.group_price,
                    member_price=item.group_price * Decimal('0.9'),
                    is_enabled=True
                )
                db.add(store_item)
        
        db.flush()
        
        # 5. 创建客户
        print("👤 创建客户...")
        customers = []
        for i in range(20):
            customer = Customer(
                name=f"客户{i+1}",
                phone=f"1380013{8000+i:04d}",
                gender=random.choice(['male', 'female']),
                birthday=datetime.now() - timedelta(days=random.randint(18*365, 60*365)),
                address=f"广州市天河区测试路{i+1}号",
                source=random.choice(['微信', '朋友推荐', '网络广告']),
                member_level=random.choice(['normal', 'silver', 'gold']),
                status='active'
            )
            db.add(customer)
            customers.append(customer)
        
        db.flush()
        
        # 6. 创建订单（最近30天）
        print("📋 创建订单...")
        store_items = db.query(StoreItem).all()
        
        order_count = 0
        for day in range(30):
            order_date = datetime.now() - timedelta(days=day)
            
            # 每天每个门店2-5个订单
            for store in [store1, store2]:
                daily_orders = random.randint(2, 5)
                
                for _ in range(daily_orders):
                    order_count += 1
                    customer = random.choice(customers)
                    cashier = emp1 if store.id == store1.id else emp3
                    
                    # 随机选择1-2个项目
                    selected_items = random.sample(
                        [si for si in store_items if si.store_id == store.id], 
                        random.randint(1, 2)
                    )
                    
                    total_amount = sum(item.store_price for item in selected_items)
                    actual_amount = total_amount * Decimal(str(random.uniform(0.8, 1.0)))
                    
                    order = Order(
                        store_id=store.id,
                        customer_id=customer.id,
                        employee_id=cashier.id,
                        order_number=f"ORD{order_date.strftime('%Y%m%d')}{order_count:04d}",
                        order_time=order_date + timedelta(hours=random.randint(9, 20)),
                        total_amount=total_amount,
                        discount_amount=total_amount - actual_amount,
                        actual_amount=actual_amount,
                        payment_method=random.choice(['wechat', 'alipay', 'cash']),
                        status='completed'
                    )
                    db.add(order)
                    db.flush()
                    
                    # 创建订单明细
                    for item in selected_items:
                        order_item = OrderItem(
                            order_id=order.id,
                            store_item_id=item.id,
                            service_employee_id=emp2.id if store.id == store1.id else emp3.id,
                            quantity=1,
                            unit_price=item.store_price,
                            discount_price=item.store_price,
                            subtotal=item.store_price
                        )
                        db.add(order_item)
        
        # 7. 创建股东
        print("💰 创建股东...")
        shareholder1 = Shareholder(
            name="张三",
            phone="13900000001", 
            id_card="******************",
            address="广州市天河区",
            remark="创始人"
        )
        shareholder2 = Shareholder(
            name="李四",
            phone="13900000002",
            id_card="******************", 
            address="广州市海珠区",
            remark="投资人"
        )
        
        db.add(shareholder1)
        db.add(shareholder2)
        db.flush()
        
        # 8. 创建股权结构
        print("📊 创建股权结构...")
        for store in [store1, store2]:
            structure1 = ShareholderStructure(
                store_id=store.id,
                shareholder_id=shareholder1.id,
                share_percentage=Decimal("60.0"),
                investment_amount=Decimal("600000"),
                effective_date=datetime.now() - timedelta(days=365)
            )
            structure2 = ShareholderStructure(
                store_id=store.id,
                shareholder_id=shareholder2.id,
                share_percentage=Decimal("40.0"),
                investment_amount=Decimal("400000"),
                effective_date=datetime.now() - timedelta(days=365)
            )
            db.add(structure1)
            db.add(structure2)
        
        db.commit()
        print("✅ 基础演示数据创建完成！")
        
        # 打印统计
        print(f"\n📊 数据统计:")
        print(f"门店: {db.query(Store).count()}")
        print(f"员工: {db.query(Employee).count()}")
        print(f"客户: {db.query(Customer).count()}")
        print(f"项目: {db.query(GroupItem).count()}")
        print(f"订单: {db.query(Order).count()}")
        print(f"股东: {db.query(Shareholder).count()}")
        
        total_revenue = db.query(Order).with_entities(
            db.func.sum(Order.actual_amount)
        ).scalar() or 0
        print(f"总营收: ¥{total_revenue:,.2f}")
        
    except Exception as e:
        print(f"❌ 创建数据失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    create_basic_data()
