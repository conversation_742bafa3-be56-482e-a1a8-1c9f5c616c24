#!/usr/bin/env python3
"""
简单的测试订单生成脚本
"""

import sys
import os
from datetime import datetime, timedelta
import random
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.operation import Order


def generate_order_number():
    """生成订单编号"""
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    random_suffix = random.randint(1000, 9999)
    return f"ORD{timestamp}{random_suffix}"


def create_simple_orders(db: Session, num_orders: int = 50):
    """创建简单的测试订单"""
    
    print(f"开始生成 {num_orders} 个简单测试订单...")
    
    # 支付方式
    payment_methods = ["cash", "wechat", "alipay", "card"]
    
    created_orders = 0
    
    for i in range(num_orders):
        try:
            # 随机选择时间（过去60天内）
            days_ago = random.randint(0, 60)
            hours_ago = random.randint(8, 22)  # 营业时间
            minutes_ago = random.randint(0, 59)
            
            order_time = datetime.now() - timedelta(
                days=days_ago, 
                hours=hours_ago, 
                minutes=minutes_ago
            )
            
            # 随机金额
            total_amount = random.uniform(50, 500)
            discount_amount = total_amount * random.uniform(0, 0.2)  # 0-20%折扣
            actual_amount = total_amount - discount_amount
            
            # 创建订单
            order = Order(
                store_id=1,  # 使用第一个门店
                customer_id=random.choice([1, 2, None, None]),  # 50%概率有客户
                employee_id=1,  # 使用第一个员工
                order_number=generate_order_number(),
                order_time=order_time,
                total_amount=Decimal(str(round(total_amount, 2))),
                discount_amount=Decimal(str(round(discount_amount, 2))),
                actual_amount=Decimal(str(round(actual_amount, 2))),
                payment_method=random.choice(payment_methods),
                status="completed",
                payment_status="paid",
                total_paid=Decimal(str(round(actual_amount, 2))),
                final_amount=Decimal(str(round(actual_amount, 2))),
                created_at=order_time,
                updated_at=order_time
            )
            
            db.add(order)
            created_orders += 1
            
            if created_orders % 10 == 0:
                print(f"已创建 {created_orders} 个订单...")
                
        except Exception as e:
            print(f"创建订单 {i+1} 时出错: {e}")
            db.rollback()
            continue
    
    try:
        db.commit()
        print(f"成功创建了 {created_orders} 个测试订单！")
    except Exception as e:
        print(f"提交数据时出错: {e}")
        db.rollback()


def main():
    """主函数"""
    db = SessionLocal()
    
    try:
        print("=== 简单测试订单生成器 ===")
        print()
        
        # 创建测试订单
        create_simple_orders(db, 100)
        print()
        
        print("=== 测试订单生成完成！===")
        print("现在可以测试BI分析功能了。")
        
    except Exception as e:
        print(f"生成测试数据时出错: {e}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    main()
