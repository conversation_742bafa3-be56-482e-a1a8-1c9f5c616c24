2025-07-28 12:52:27,131 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 12:52:27,132 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 12:52:28,720 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 12:52:28,722 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 12:52:28,722 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 12:52:28,722 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 12:52:28,723 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 12:52:28,723 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 12:52:28,723 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 12:52:29,412 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 12:52:29,412 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 12:52:29,413 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 12:52:29,413 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 12:52:29,419 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 12:52:30,259 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.85秒
2025-07-28 12:53:35,087 - app.services.monitoring_service - ERROR - 错误记录: ValueError - 这是一个测试错误，用于验证错误监控功能
2025-07-28 12:57:19,417 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 12:58:03,558 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:07:43,852 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:07:44,627 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:07:46,282 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:07:46,287 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 13:07:46,287 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 13:07:46,288 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 13:07:46,289 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:07:46,290 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 13:07:46,290 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 13:07:47,009 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 13:07:47,009 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 13:07:47,010 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 13:07:47,010 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 13:07:47,015 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 13:07:47,878 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.87秒
2025-07-28 13:09:37,423 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:09:38,235 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:09:39,867 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:09:39,872 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 13:09:39,872 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 13:09:39,873 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 13:09:39,875 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:09:39,875 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 13:09:39,876 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 13:09:40,596 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 13:09:40,596 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 13:09:40,597 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 13:09:40,597 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 13:09:40,607 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 13:09:41,432 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.83秒
2025-07-28 13:14:53,483 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:15:34,196 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:20:07,326 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:20:08,114 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:20:09,767 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:20:09,774 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 13:20:09,775 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 13:20:09,775 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 13:20:09,777 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:20:09,778 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 13:20:09,778 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 13:20:10,525 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 13:20:10,525 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 13:20:10,525 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 13:20:10,526 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 13:20:10,536 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 13:20:11,393 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.87秒
2025-07-28 13:21:06,038 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:21:06,850 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:21:08,456 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:21:08,461 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 13:21:08,461 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 13:21:08,462 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 13:21:08,464 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:21:08,464 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 13:21:08,464 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 13:21:09,211 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 13:21:09,211 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 13:21:09,212 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 13:21:09,212 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 13:21:09,222 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 13:21:10,019 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.81秒
2025-07-28 13:21:22,331 - app.api.api_v1.endpoints.business_intelligence - ERROR - 测试权限管理失败: name 'Role' is not defined
2025-07-28 13:21:47,210 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:21:47,988 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:21:49,647 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:21:49,649 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 13:21:49,649 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 13:21:49,649 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 13:21:49,650 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:21:49,650 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 13:21:49,650 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 13:21:50,377 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 13:21:50,377 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 13:21:50,377 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 13:21:50,377 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 13:21:50,388 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 13:21:51,179 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.80秒
2025-07-28 13:28:54,572 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:28:55,385 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:28:55,386 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:28:57,065 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:28:57,067 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 13:28:57,067 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 13:28:57,067 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 13:28:57,068 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:28:57,068 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 13:28:57,068 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 13:28:57,804 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 13:28:57,804 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 13:28:57,805 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 13:28:57,805 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 13:28:57,811 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 13:28:58,687 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.88秒
2025-07-28 13:29:38,149 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:29:38,948 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:29:38,949 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:29:40,653 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:29:40,655 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 13:29:40,655 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 13:29:40,656 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 13:29:40,657 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:29:40,657 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 13:29:40,657 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 13:29:41,391 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 13:29:41,391 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 13:29:41,391 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 13:29:41,391 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 13:29:41,402 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 13:29:42,298 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.91秒
2025-07-28 13:30:44,849 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:30:45,611 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:30:45,612 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:30:47,265 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:30:47,267 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 13:30:47,267 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 13:30:47,267 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 13:30:47,268 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:30:47,268 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 13:30:47,268 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 13:30:47,995 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 13:30:47,995 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 13:30:47,995 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 13:30:47,995 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 13:30:48,001 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 13:30:48,821 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.83秒
2025-07-28 13:48:24,987 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:48:27,028 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:48:27,030 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:48:28,676 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:48:28,678 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 13:48:28,678 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 13:48:28,678 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 13:48:28,679 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:48:28,680 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 13:48:28,680 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 13:48:29,397 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 13:48:29,397 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 13:48:29,397 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 13:48:29,397 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 13:48:29,397 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 13:48:30,333 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.93秒
2025-07-28 14:18:30,347 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 14:18:30,836 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.49秒
2025-07-28 14:32:43,022 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:32:43,797 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:32:43,798 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:32:45,445 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:32:45,447 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 14:32:45,447 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 14:32:45,447 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 14:32:45,448 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:32:45,448 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 14:32:45,448 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 14:32:46,189 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 14:32:46,189 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 14:32:46,189 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 14:32:46,189 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 14:32:46,195 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 14:32:47,037 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.85秒
2025-07-28 14:33:18,554 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:33:19,304 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:33:19,305 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:33:20,900 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:33:20,902 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 14:33:20,903 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 14:33:20,903 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 14:33:20,904 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:33:20,904 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 14:33:20,904 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 14:33:21,627 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 14:33:21,627 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 14:33:21,628 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 14:33:21,628 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 14:33:21,638 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 14:33:22,482 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.85秒
2025-07-28 14:46:26,452 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:46:27,223 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:46:27,224 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:46:28,906 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:46:28,908 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 14:46:28,908 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 14:46:28,908 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 14:46:28,909 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:46:28,909 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 14:46:28,909 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 14:46:29,631 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 14:46:29,631 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 14:46:29,632 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 14:46:29,632 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 14:46:29,643 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 14:46:30,487 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.85秒
2025-07-28 15:25:22,940 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:25:23,708 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:25:23,709 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:25:25,308 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:25:25,310 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 15:25:25,310 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 15:25:25,310 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 15:25:25,311 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:25:25,311 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 15:25:25,311 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 15:25:26,020 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 15:25:26,021 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 15:25:26,021 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 15:25:26,021 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 15:25:26,026 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 15:25:26,836 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.81秒
2025-07-28 15:31:30,377 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:32:55,201 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:32:56,244 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:32:56,247 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:33:57,886 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:33:58,660 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:33:58,661 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:34:00,279 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:34:54,350 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:34:55,125 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:34:55,126 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:34:56,738 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:34:56,740 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 15:34:56,740 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 15:34:56,741 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 15:34:56,742 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:34:56,742 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 15:34:56,742 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 15:34:57,461 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 15:34:57,462 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 15:34:57,462 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 15:34:57,462 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 15:34:57,473 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 15:34:58,509 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 1.05秒
2025-07-28 16:08:14,747 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:08:16,753 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:08:16,754 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:08:18,452 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:08:18,454 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 16:08:18,454 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 16:08:18,454 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 16:08:18,455 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:08:18,455 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 16:08:18,455 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 16:08:19,170 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 16:08:19,170 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 16:08:19,170 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 16:08:19,170 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 16:08:19,177 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 16:08:20,128 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.96秒
2025-07-28 16:12:34,296 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:12:35,051 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:12:35,052 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:12:36,604 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:12:36,606 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 16:12:36,606 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 16:12:36,606 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 16:12:36,607 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:12:36,607 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 16:12:36,607 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 16:12:37,319 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 16:12:37,320 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 16:12:37,320 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 16:12:37,320 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 16:12:37,325 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 16:12:38,172 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.85秒
2025-07-28 16:28:07,738 - app.middleware.rate_limit_middleware - WARNING - 自适应限流: ip:*********, 动态限制: 5, 负载因子: 1.00
2025-07-28 16:28:12,821 - app.middleware.rate_limit_middleware - WARNING - 自适应限流: ip:*********, 动态限制: 5, 负载因子: 1.00
2025-07-28 16:32:47,276 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/root/vip/backend/venv/lib/python3.12/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-07-28 16:34:28,736 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:34:29,533 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:34:29,535 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:34:31,242 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:34:31,244 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 16:34:31,244 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 16:34:31,244 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 16:34:31,245 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:34:31,245 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 16:34:31,245 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 16:34:31,976 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 16:34:31,976 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 16:34:31,977 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 16:34:31,977 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 16:34:31,982 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 16:34:32,836 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.86秒
2025-07-28 16:49:02,080 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:49:02,914 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:49:02,915 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:49:04,519 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:49:04,521 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 16:49:04,522 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:49:04,522 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 16:49:04,522 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 16:49:05,242 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 16:49:05,242 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 16:49:05,243 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 16:49:05,243 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 16:49:05,248 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 16:49:07,088 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 1.84秒
2025-07-28 16:58:05,038 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:58:05,817 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:58:05,819 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:58:07,442 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:58:07,447 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 16:58:07,449 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:58:07,449 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 16:58:07,449 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 16:58:08,177 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 16:58:08,178 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 16:58:08,178 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 16:58:08,178 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 16:58:08,189 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 16:58:09,031 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.85秒
2025-07-28 17:28:09,043 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 17:28:09,549 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.51秒
2025-07-28 17:58:09,562 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 17:58:10,072 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.51秒
2025-07-28 18:28:10,083 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 18:28:10,593 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.51秒
2025-07-28 18:47:30,393 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/root/vip/backend/venv/lib/python3.12/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-07-28 18:52:02,207 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 18:52:02,963 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 18:52:02,964 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 18:52:04,556 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 18:52:04,558 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 18:52:04,559 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 18:52:04,559 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 18:52:04,559 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 18:52:05,293 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 18:52:05,293 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 18:52:05,293 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 18:52:05,293 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 18:52:05,298 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 18:52:06,132 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.84秒
2025-07-28 18:52:24,147 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 18:52:24,929 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 18:52:24,930 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 18:52:26,585 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 18:52:26,587 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 18:52:26,588 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 18:52:26,588 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 18:52:26,588 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 18:52:27,312 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 18:52:27,312 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 18:52:27,312 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 18:52:27,313 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 18:52:27,318 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 18:52:28,162 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.85秒
2025-07-28 18:55:53,977 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 18:55:54,744 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 18:55:54,747 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 18:55:56,379 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 18:55:56,384 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 18:55:56,386 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 18:55:56,386 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 18:55:56,387 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 18:55:57,121 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 18:55:57,121 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 18:55:57,121 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 18:55:57,122 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 18:55:57,127 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 18:55:57,977 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.86秒
2025-07-28 18:56:49,415 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 18:56:50,200 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 18:56:50,201 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 18:56:51,812 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 18:56:51,813 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 18:56:51,814 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 18:56:51,815 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 18:56:51,815 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 18:56:52,537 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 18:56:52,538 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 18:56:52,538 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 18:56:52,538 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 18:56:52,548 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 18:56:53,603 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 1.06秒
2025-07-28 18:57:46,732 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/root/vip/backend/venv/lib/python3.12/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-07-28 19:00:35,186 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:00:35,946 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:00:35,947 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:00:37,526 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:00:37,528 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 19:00:37,529 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:00:37,529 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 19:00:37,529 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 19:00:38,290 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 19:00:38,290 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 19:00:38,291 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 19:00:38,291 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 19:00:38,302 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 19:00:39,170 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.88秒
2025-07-28 19:00:58,823 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:00:59,603 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:00:59,604 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:01:01,273 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:01:01,275 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 19:01:01,276 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:01:01,276 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 19:01:01,276 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 19:01:02,005 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 19:01:02,005 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 19:01:02,006 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 19:01:02,006 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 19:01:02,017 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 19:01:02,838 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.83秒
2025-07-28 19:06:04,052 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/root/vip/backend/venv/lib/python3.12/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-07-28 19:06:41,997 - app.middleware.rate_limit_middleware - WARNING - 自适应限流: ip:********, 动态限制: 6, 负载因子: 1.20
2025-07-28 19:22:51,501 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:22:52,267 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:22:52,268 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:22:53,860 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:22:53,863 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 19:22:53,864 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:22:53,864 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 19:22:53,864 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 19:22:54,620 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 19:22:54,620 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 19:22:54,621 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 19:22:54,621 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 19:22:54,621 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 19:22:55,474 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.85秒
2025-07-28 19:23:11,716 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:23:12,491 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:23:12,492 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:23:14,122 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:23:14,124 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 19:23:14,125 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:23:14,125 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 19:23:14,125 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 19:23:14,846 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 19:23:14,846 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 19:23:14,846 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 19:23:14,846 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 19:23:14,851 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 19:23:15,720 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.87秒
2025-07-28 19:25:39,470 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/root/vip/backend/venv/lib/python3.12/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-07-28 19:26:51,448 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:26:52,210 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:26:52,210 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:27:12,600 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:27:13,352 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:27:13,353 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:27:33,437 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:27:34,263 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:27:34,264 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:29:10,806 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:29:11,574 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:29:11,575 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:29:13,161 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:29:13,163 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 19:29:13,164 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:29:13,164 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 19:29:13,164 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 19:29:13,904 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 19:29:13,904 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 19:29:13,909 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 19:29:13,921 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 19:29:13,931 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 19:29:14,996 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 1.09秒
2025-07-28 19:29:34,547 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:29:35,318 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:29:35,320 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:29:36,911 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:29:36,913 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 19:29:36,914 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:29:36,914 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 19:29:36,914 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 19:29:37,635 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 19:29:37,635 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 19:29:37,635 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 19:29:37,635 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 19:29:37,646 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 19:29:38,706 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 1.07秒
2025-07-28 19:32:22,078 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:32:22,841 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:32:22,843 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:32:24,450 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:32:24,454 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 19:32:24,456 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:32:24,456 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 19:32:24,457 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 19:32:25,187 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 19:32:25,188 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 19:32:25,188 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 19:32:25,188 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 19:32:25,199 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 19:32:26,308 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 1.12秒
2025-07-28 19:32:43,616 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:32:44,411 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:32:44,412 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:32:45,967 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:32:45,972 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 19:32:45,974 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:32:45,974 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 19:32:45,974 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 19:32:46,718 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 19:32:46,719 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 19:32:46,719 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 19:32:46,719 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 19:32:46,724 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 19:32:47,564 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.84秒
2025-07-28 19:33:58,357 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:33:59,142 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:33:59,143 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:34:00,754 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:34:00,756 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 19:34:00,757 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:34:00,757 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 19:34:00,757 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 19:34:01,481 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 19:34:01,481 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 19:34:01,481 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 19:34:01,482 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 19:34:01,487 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 19:34:02,529 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 1.05秒
2025-07-28 19:34:20,876 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:34:21,629 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:34:21,630 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:34:23,186 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:34:23,188 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 19:34:23,189 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:34:23,189 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 19:34:23,189 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 19:34:23,920 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 19:34:23,920 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 19:34:23,920 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 19:34:23,920 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 19:34:23,926 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 19:34:24,770 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.85秒
2025-07-28 19:35:20,229 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/root/vip/backend/venv/lib/python3.12/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-07-28 19:37:06,313 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:37:07,075 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:37:07,076 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:37:08,728 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:37:08,733 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 19:37:08,735 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:37:08,736 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 19:37:08,736 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 19:37:09,473 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 19:37:09,473 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 19:37:09,485 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 19:37:09,485 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 19:37:09,490 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 19:37:10,779 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 1.29秒
2025-07-28 19:37:35,590 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:37:36,355 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:37:36,356 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:37:38,033 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:37:38,035 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 19:37:38,036 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:37:38,036 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 19:37:38,036 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 19:37:38,805 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 19:37:38,806 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 19:37:38,806 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 19:37:38,806 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 19:37:38,811 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 19:37:39,652 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.85秒
2025-07-28 19:37:56,698 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:37:57,472 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:37:57,473 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:37:59,159 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:37:59,161 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 19:37:59,162 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:37:59,162 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 19:37:59,162 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 19:37:59,879 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 19:37:59,879 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 19:37:59,879 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 19:37:59,880 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 19:37:59,885 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 19:38:00,720 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.84秒
2025-07-28 19:38:56,736 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/root/vip/backend/venv/lib/python3.12/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-07-28 19:46:31,201 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:46:31,980 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:46:31,981 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:46:33,566 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:46:33,568 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 19:46:33,569 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:46:33,569 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 19:46:33,569 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 19:46:34,327 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 19:46:34,327 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 19:46:34,327 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 19:46:34,327 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 19:46:34,327 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 19:46:35,185 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.86秒
2025-07-28 19:46:55,070 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:46:55,873 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:46:55,874 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:46:57,483 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:46:57,485 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 19:46:57,485 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:46:57,486 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 19:46:57,486 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 19:46:58,222 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 19:46:58,222 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 19:46:58,222 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 19:46:58,222 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 19:46:58,228 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 19:46:59,086 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.86秒
2025-07-28 19:47:15,079 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:47:15,852 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:47:15,853 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:47:17,516 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:47:17,519 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 19:47:17,520 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:47:17,520 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 19:47:17,521 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 19:47:18,273 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 19:47:18,274 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 19:47:18,274 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 19:47:18,274 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 19:47:18,279 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 19:47:19,142 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.87秒
2025-07-28 19:47:32,593 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:47:33,377 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:47:33,379 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:47:34,985 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:47:34,986 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 19:47:34,987 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:47:34,987 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 19:47:34,988 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 19:47:35,737 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 19:47:35,738 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 19:47:35,738 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 19:47:35,738 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 19:47:35,743 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 19:47:36,622 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.88秒
2025-07-28 19:47:55,061 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:47:55,829 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:47:55,830 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:47:57,497 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:47:57,499 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 19:47:57,500 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:47:57,500 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 19:47:57,500 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 19:47:58,224 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 19:47:58,224 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 19:47:58,224 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 19:47:58,225 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 19:47:58,235 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 19:47:59,288 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 1.06秒
2025-07-28 19:49:20,817 - app.middleware.rate_limit_middleware - WARNING - 自适应限流: ip:********, 动态限制: 6, 负载因子: 1.20
2025-07-28 19:50:50,948 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/root/vip/backend/venv/lib/python3.12/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-07-28 20:17:59,305 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 20:17:59,860 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 0.56秒
2025-07-28 20:27:26,846 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:27:27,623 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:27:27,624 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:27:29,219 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:27:29,220 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 20:27:29,221 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:27:29,221 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 20:27:29,222 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 20:27:29,963 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 20:27:29,963 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 20:27:29,963 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 20:27:29,963 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 20:27:29,968 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 20:27:31,180 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 1.22秒
2025-07-28 20:28:04,570 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:28:05,379 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:28:05,380 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:28:07,040 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:28:07,041 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 20:28:07,042 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:28:07,043 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 20:28:07,043 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 20:28:07,822 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 20:28:07,822 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 20:28:07,823 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 20:28:07,823 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 20:28:07,828 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 20:28:08,790 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 0.97秒
2025-07-28 20:29:36,364 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:29:37,146 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:29:37,147 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:29:38,797 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:29:38,810 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 20:29:38,812 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:29:38,812 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 20:29:38,813 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 20:29:39,566 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 20:29:39,567 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 20:29:39,567 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 20:29:39,567 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 20:29:39,572 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 20:29:40,567 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 1.00秒
2025-07-28 20:30:10,999 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:30:11,762 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:30:11,764 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:30:13,416 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:30:13,418 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 20:30:13,419 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:30:13,419 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 20:30:13,419 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 20:30:14,174 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 20:30:14,174 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 20:30:14,174 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 20:30:14,174 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 20:30:14,180 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 20:30:15,101 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 0.93秒
2025-07-28 20:30:32,407 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:30:33,182 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:30:33,183 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:30:34,815 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:30:34,817 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 20:30:34,817 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:30:34,818 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 20:30:34,818 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 20:30:35,559 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 20:30:35,559 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 20:30:35,559 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 20:30:35,560 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 20:30:35,565 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 20:30:36,459 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 0.90秒
2025-07-28 20:54:56,800 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:54:57,584 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:54:57,585 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:54:59,185 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:54:59,187 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 20:54:59,188 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:54:59,188 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 20:54:59,188 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 20:54:59,917 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 20:54:59,918 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 20:54:59,918 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 20:54:59,918 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 20:54:59,929 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 20:55:01,073 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 1.15秒
2025-07-28 20:55:36,546 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:55:37,325 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:55:37,326 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:55:38,983 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:55:38,984 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 20:55:38,985 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:55:38,985 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 20:55:38,985 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 20:55:39,716 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 20:55:39,717 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 20:55:39,717 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 20:55:39,717 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 20:55:39,722 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 20:55:41,702 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 1.98秒
2025-07-28 20:56:04,989 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:56:05,840 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:56:05,841 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:56:07,430 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:56:07,436 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 20:56:07,439 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:56:07,439 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 20:56:07,439 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 20:56:08,208 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 20:56:08,208 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 20:56:08,208 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 20:56:08,208 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 20:56:08,214 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 20:56:09,120 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 0.91秒
2025-07-28 20:57:43,351 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:57:44,123 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:57:44,124 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:57:45,793 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:57:45,794 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 20:57:45,795 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:57:45,795 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 20:57:45,795 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 20:57:46,537 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 20:57:46,537 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 20:57:46,538 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 20:57:46,538 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 20:57:46,543 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 20:57:47,673 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 1.14秒
2025-07-28 20:58:05,282 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:58:06,065 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:58:06,066 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:58:07,758 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:58:07,759 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 20:58:07,760 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:58:07,760 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 20:58:07,761 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 20:58:08,522 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 20:58:08,523 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 20:58:08,523 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 20:58:08,523 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 20:58:08,533 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 20:58:09,467 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 0.94秒
2025-07-28 20:58:30,545 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:58:31,391 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:58:31,393 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:58:33,113 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:58:33,118 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 20:58:33,120 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 20:58:33,120 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 20:58:33,121 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 20:58:33,870 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 20:58:33,870 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 20:58:33,870 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 20:58:33,870 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 20:58:33,881 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 20:58:35,036 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 1.17秒
2025-07-28 21:00:01,884 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 21:00:02,676 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 21:00:02,677 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 21:00:04,271 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 21:00:04,272 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 21:00:04,274 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 21:00:04,274 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 21:00:04,274 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 21:00:05,009 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 21:00:05,009 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 21:00:05,009 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 21:00:05,009 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 21:00:05,020 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 21:00:05,947 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 0.94秒
2025-07-28 21:00:47,715 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 21:00:48,537 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 21:00:48,538 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 21:00:50,209 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 21:00:50,211 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 21:00:50,211 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 21:00:50,211 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 21:00:50,212 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 21:00:50,942 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 21:00:50,942 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 21:00:50,942 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 21:00:50,942 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 21:00:50,953 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 21:00:51,857 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 0.91秒
2025-07-28 21:29:10,181 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/root/vip/backend/venv/lib/python3.12/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-07-28 21:30:51,869 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 21:30:52,429 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 0.56秒
