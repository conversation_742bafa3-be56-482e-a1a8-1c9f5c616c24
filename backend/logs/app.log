2025-07-28 12:52:27,131 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 12:52:27,132 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 12:52:28,720 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 12:52:28,722 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 12:52:28,722 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 12:52:28,722 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 12:52:28,723 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 12:52:28,723 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 12:52:28,723 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 12:52:29,412 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 12:52:29,412 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 12:52:29,413 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 12:52:29,413 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 12:52:29,419 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 12:52:30,259 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.85秒
2025-07-28 12:53:35,087 - app.services.monitoring_service - ERROR - 错误记录: ValueError - 这是一个测试错误，用于验证错误监控功能
2025-07-28 12:57:19,417 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 12:58:03,558 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:07:43,852 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:07:44,627 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:07:46,282 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:07:46,287 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 13:07:46,287 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 13:07:46,288 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 13:07:46,289 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:07:46,290 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 13:07:46,290 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 13:07:47,009 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 13:07:47,009 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 13:07:47,010 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 13:07:47,010 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 13:07:47,015 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 13:07:47,878 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.87秒
2025-07-28 13:09:37,423 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:09:38,235 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:09:39,867 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:09:39,872 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 13:09:39,872 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 13:09:39,873 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 13:09:39,875 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:09:39,875 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 13:09:39,876 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 13:09:40,596 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 13:09:40,596 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 13:09:40,597 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 13:09:40,597 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 13:09:40,607 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 13:09:41,432 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.83秒
2025-07-28 13:14:53,483 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:15:34,196 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:20:07,326 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:20:08,114 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:20:09,767 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:20:09,774 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 13:20:09,775 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 13:20:09,775 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 13:20:09,777 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:20:09,778 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 13:20:09,778 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 13:20:10,525 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 13:20:10,525 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 13:20:10,525 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 13:20:10,526 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 13:20:10,536 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 13:20:11,393 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.87秒
2025-07-28 13:21:06,038 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:21:06,850 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:21:08,456 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:21:08,461 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 13:21:08,461 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 13:21:08,462 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 13:21:08,464 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:21:08,464 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 13:21:08,464 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 13:21:09,211 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 13:21:09,211 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 13:21:09,212 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 13:21:09,212 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 13:21:09,222 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 13:21:10,019 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.81秒
2025-07-28 13:21:22,331 - app.api.api_v1.endpoints.business_intelligence - ERROR - 测试权限管理失败: name 'Role' is not defined
2025-07-28 13:21:47,210 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:21:47,988 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:21:49,647 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:21:49,649 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 13:21:49,649 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 13:21:49,649 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 13:21:49,650 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:21:49,650 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 13:21:49,650 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 13:21:50,377 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 13:21:50,377 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 13:21:50,377 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 13:21:50,377 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 13:21:50,388 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 13:21:51,179 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.80秒
2025-07-28 13:28:54,572 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:28:55,385 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:28:55,386 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:28:57,065 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:28:57,067 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 13:28:57,067 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 13:28:57,067 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 13:28:57,068 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:28:57,068 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 13:28:57,068 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 13:28:57,804 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 13:28:57,804 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 13:28:57,805 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 13:28:57,805 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 13:28:57,811 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 13:28:58,687 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.88秒
2025-07-28 13:29:38,149 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:29:38,948 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:29:38,949 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:29:40,653 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:29:40,655 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 13:29:40,655 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 13:29:40,656 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 13:29:40,657 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:29:40,657 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 13:29:40,657 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 13:29:41,391 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 13:29:41,391 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 13:29:41,391 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 13:29:41,391 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 13:29:41,402 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 13:29:42,298 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.91秒
2025-07-28 13:30:44,849 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:30:45,611 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:30:45,612 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:30:47,265 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:30:47,267 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 13:30:47,267 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 13:30:47,267 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 13:30:47,268 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:30:47,268 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 13:30:47,268 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 13:30:47,995 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 13:30:47,995 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 13:30:47,995 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 13:30:47,995 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 13:30:48,001 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 13:30:48,821 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.83秒
2025-07-28 13:48:24,987 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:48:27,028 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:48:27,030 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:48:28,676 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:48:28,678 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 13:48:28,678 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 13:48:28,678 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 13:48:28,679 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 13:48:28,680 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 13:48:28,680 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 13:48:29,397 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 13:48:29,397 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 13:48:29,397 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 13:48:29,397 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 13:48:29,397 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 13:48:30,333 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.93秒
2025-07-28 14:18:30,347 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 14:18:30,836 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.49秒
2025-07-28 14:32:43,022 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:32:43,797 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:32:43,798 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:32:45,445 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:32:45,447 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 14:32:45,447 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 14:32:45,447 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 14:32:45,448 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:32:45,448 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 14:32:45,448 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 14:32:46,189 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 14:32:46,189 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 14:32:46,189 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 14:32:46,189 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 14:32:46,195 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 14:32:47,037 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.85秒
2025-07-28 14:33:18,554 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:33:19,304 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:33:19,305 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:33:20,900 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:33:20,902 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 14:33:20,903 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 14:33:20,903 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 14:33:20,904 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:33:20,904 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 14:33:20,904 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 14:33:21,627 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 14:33:21,627 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 14:33:21,628 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 14:33:21,628 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 14:33:21,638 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 14:33:22,482 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.85秒
2025-07-28 14:46:26,452 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:46:27,223 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:46:27,224 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:46:28,906 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:46:28,908 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 14:46:28,908 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 14:46:28,908 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 14:46:28,909 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 14:46:28,909 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 14:46:28,909 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 14:46:29,631 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 14:46:29,631 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 14:46:29,632 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 14:46:29,632 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 14:46:29,643 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 14:46:30,487 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.85秒
