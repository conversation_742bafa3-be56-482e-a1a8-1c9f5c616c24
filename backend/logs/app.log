2025-07-28 12:52:27,131 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 12:52:27,132 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 12:52:28,720 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 12:52:28,722 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 12:52:28,722 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 12:52:28,722 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 12:52:28,723 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 12:52:28,723 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 12:52:28,723 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
2025-07-28 12:52:29,412 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 12:52:29,412 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 12:52:29,413 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 12:52:29,413 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 12:52:29,419 - app.services.monitoring_service - INFO - 监控系统已初始化
2025-07-28 12:52:30,259 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.85秒
2025-07-28 12:53:35,087 - app.services.monitoring_service - ERROR - 错误记录: ValueError - 这是一个测试错误，用于验证错误监控功能
2025-07-28 12:57:19,417 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 12:58:03,558 - app.utils.redis_cache - INFO - Redis缓存连接成功
