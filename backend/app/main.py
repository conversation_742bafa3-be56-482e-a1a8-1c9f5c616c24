from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi.staticfiles import StaticFiles
import os

from app.api.api_v1.api import api_router
from app.core.config import settings
from app.services.data_preloader import start_data_preloading, get_preloader_status, preloader
from app.middleware.access_pattern_middleware import setup_access_pattern_tracking
from app.middleware.compression_middleware import setup_compression_middleware
from app.middleware.rate_limit_middleware import setup_rate_limiting
from app.services.monitoring_service import setup_monitoring


app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.PROJECT_VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

# 设置响应压缩中间件（在CORS之前）
# 临时禁用压缩中间件进行测试
# setup_compression_middleware(
#     app,
#     compression_type="smart",
#     minimum_size=300,  # 300字节以上才压缩
#     compression_level=6
# )

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建上传目录
UPLOAD_DIR = "uploads"
if not os.path.exists(UPLOAD_DIR):
    os.makedirs(UPLOAD_DIR)

# 挂载静态文件目录
app.mount("/uploads", StaticFiles(directory=UPLOAD_DIR), name="uploads")

# 设置访问模式跟踪中间件
setup_access_pattern_tracking(app, preloader)

# 设置API限流中间件
try:
    from app.utils.redis_cache import RedisCache
    redis_cache = RedisCache()
    if redis_cache.use_redis:
        setup_rate_limiting(app, redis_client=redis_cache.redis_client, rate_limit_type="adaptive")
    else:
        setup_rate_limiting(app, rate_limit_type="adaptive")
except Exception as e:
    # Redis不可用时使用内存限流
    setup_rate_limiting(app, rate_limit_type="adaptive")

# 注册API路由
app.include_router(api_router, prefix=settings.API_V1_STR)


# 应用启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化操作"""
    # 启动数据预加载
    start_data_preloading()

    # 启动监控系统
    setup_monitoring()


@app.get("/")
def root():
    return {"message": "欢迎使用按摩推拿连锁门店管理系统API"}


@app.get("/health")
def health_check():
    return {"status": "ok"}


@app.get("/preloader/status")
def preloader_status():
    """获取数据预加载器状态"""
    return get_preloader_status()