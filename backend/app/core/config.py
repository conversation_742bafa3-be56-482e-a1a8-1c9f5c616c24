from pydantic import Field
from pydantic_settings import BaseSettings
from typing import Optional, Dict, Any, List
import secrets
import os


class Settings(BaseSettings):
    PROJECT_NAME: str = "按摩推拿连锁门店管理系统"
    PROJECT_VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    
    # 数据库配置
    # 设置为使用PostgreSQL
    USE_SQLITE: bool = False
    DATABASE_URL: Optional[str] = None

    # PostgreSQL配置（当USE_SQLITE=False时使用）
    POSTGRES_SERVER: str = "*************"
    POSTGRES_PORT: str = "5432"
    POSTGRES_USER: str = "vip"
    POSTGRES_PASSWORD: str = "Aqyx2019"
    POSTGRES_DB: str = "vip"
    
    # JWT配置
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24  # 1天
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7  # 7天
    ALGORITHM: str = "HS256"  # JWT加密算法
    
    # CORS配置
    BACKEND_CORS_ORIGINS: List[str] = ["*"]
    
    # Redis配置
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None

    # 缓存配置
    CACHE_EXPIRE_TIME: Dict[str, int] = {
        "business_indicators": 1800,      # 业务指标缓存30分钟
        "multidimensional_report": 3600,  # 多维度报表缓存1小时
        "trend_prediction": 7200,         # 趋势预测缓存2小时
        "shared_reports": 86400,          # 共享报表缓存24小时
    }
    
    class Config:
        case_sensitive = True
        env_file = ".env"


settings = Settings()

# 动态设置DATABASE_URL
if not settings.DATABASE_URL:
    if settings.USE_SQLITE:
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        db_path = os.path.join(base_dir, "app.db")
        settings.DATABASE_URL = f"sqlite:///{db_path}"
    else:
        settings.DATABASE_URL = f"postgresql://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_SERVER}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}" 