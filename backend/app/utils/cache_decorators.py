"""
缓存装饰器模块
提供各种缓存装饰器来简化缓存的使用
"""

import functools
import json
import hashlib
import time
from typing import Any, Callable, Dict, Optional, Union
from app.utils.redis_cache import RedisCache
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

# 全局缓存实例
cache_instance = RedisCache()


def cache_bi_result(
    cache_type: str,
    expire_seconds: Optional[int] = None,
    key_prefix: Optional[str] = None,
    exclude_params: Optional[list] = None
):
    """
    BI结果缓存装饰器
    
    Args:
        cache_type: 缓存类型（如 'business_indicators', 'multidimensional_report'）
        expire_seconds: 过期时间（秒），如果不指定则使用默认配置
        key_prefix: 缓存键前缀
        exclude_params: 排除的参数列表（这些参数不会影响缓存键）
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 生成缓存键参数
            cache_params = _extract_cache_params(args, kwargs, exclude_params or [])
            
            # 添加函数名到缓存键
            if key_prefix:
                cache_key = f"{key_prefix}_{func.__name__}"
            else:
                cache_key = func.__name__
            
            # 尝试从缓存获取
            cached_result = cache_instance.get_bi_data(cache_key, cache_params)
            if cached_result is not None:
                logger.debug(f"缓存命中: {cache_key}")
                return cached_result
            
            # 缓存未命中，执行函数
            logger.debug(f"缓存未命中，执行函数: {cache_key}")
            result = await func(*args, **kwargs)
            
            # 设置缓存
            expire_time = expire_seconds or settings.CACHE_EXPIRE_TIME.get(cache_type, 3600)
            cache_instance.set_bi_data(cache_key, cache_params, result, expire_time)
            
            return result
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            # 生成缓存键参数
            cache_params = _extract_cache_params(args, kwargs, exclude_params or [])
            
            # 添加函数名到缓存键
            if key_prefix:
                cache_key = f"{key_prefix}_{func.__name__}"
            else:
                cache_key = func.__name__
            
            # 尝试从缓存获取
            cached_result = cache_instance.get_bi_data(cache_key, cache_params)
            if cached_result is not None:
                logger.debug(f"缓存命中: {cache_key}")
                return cached_result
            
            # 缓存未命中，执行函数
            logger.debug(f"缓存未命中，执行函数: {cache_key}")
            result = func(*args, **kwargs)
            
            # 设置缓存
            expire_time = expire_seconds or settings.CACHE_EXPIRE_TIME.get(cache_type, 3600)
            cache_instance.set_bi_data(cache_key, cache_params, result, expire_time)
            
            return result
        
        # 根据函数是否为协程选择包装器
        if hasattr(func, '__code__') and func.__code__.co_flags & 0x80:  # CO_COROUTINE
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def invalidate_cache(cache_type: str, **params):
    """
    缓存失效装饰器
    在函数执行后使相关缓存失效
    
    Args:
        cache_type: 缓存类型
        **params: 缓存参数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            result = await func(*args, **kwargs)
            
            # 使缓存失效
            try:
                cache_instance.delete_bi_data(cache_type, params)
                logger.debug(f"缓存已失效: {cache_type}")
            except Exception as e:
                logger.error(f"缓存失效失败: {str(e)}")
            
            return result
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            
            # 使缓存失效
            try:
                cache_instance.delete_bi_data(cache_type, params)
                logger.debug(f"缓存已失效: {cache_type}")
            except Exception as e:
                logger.error(f"缓存失效失败: {str(e)}")
            
            return result
        
        # 根据函数是否为协程选择包装器
        if hasattr(func, '__code__') and func.__code__.co_flags & 0x80:  # CO_COROUTINE
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def cache_with_timeout(timeout_seconds: int = 3600):
    """
    简单的超时缓存装饰器
    
    Args:
        timeout_seconds: 超时时间（秒）
    """
    def decorator(func: Callable) -> Callable:
        cache = {}
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            key = _generate_simple_key(func.__name__, args, kwargs)
            
            # 检查缓存
            if key in cache:
                cached_item = cache[key]
                if time.time() - cached_item['timestamp'] < timeout_seconds:
                    logger.debug(f"简单缓存命中: {func.__name__}")
                    return cached_item['result']
                else:
                    # 过期删除
                    del cache[key]
            
            # 执行函数
            result = func(*args, **kwargs)
            
            # 存储到缓存
            cache[key] = {
                'result': result,
                'timestamp': time.time()
            }
            
            return result
        
        return wrapper
    
    return decorator


def _extract_cache_params(args: tuple, kwargs: dict, exclude_params: list) -> Dict[str, Any]:
    """提取缓存参数"""
    cache_params = {}
    
    # 处理kwargs
    for key, value in kwargs.items():
        if key not in exclude_params:
            if hasattr(value, 'dict'):  # Pydantic模型
                cache_params[key] = value.dict()
            elif hasattr(value, '__dict__'):  # 普通对象
                cache_params[key] = value.__dict__
            else:
                cache_params[key] = value
    
    # 处理args（简化处理）
    if args:
        cache_params['_args'] = str(args)
    
    return cache_params


def _generate_simple_key(func_name: str, args: tuple, kwargs: dict) -> str:
    """生成简单缓存键"""
    try:
        key_data = {
            'func': func_name,
            'args': str(args),
            'kwargs': sorted(kwargs.items())
        }
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()
    except Exception:
        return f"{func_name}_{int(time.time())}"


# 预定义的缓存装饰器
business_indicators_cache = functools.partial(
    cache_bi_result,
    cache_type="business_indicators",
    expire_seconds=1800  # 30分钟
)

multidimensional_cache = functools.partial(
    cache_bi_result,
    cache_type="multidimensional_report",
    expire_seconds=3600  # 1小时
)

trend_prediction_cache = functools.partial(
    cache_bi_result,
    cache_type="trend_prediction",
    expire_seconds=7200  # 2小时
)

shared_reports_cache = functools.partial(
    cache_bi_result,
    cache_type="shared_reports",
    expire_seconds=86400  # 24小时
)
