import json
import hashlib
import redis
import pickle
import time
from typing import Dict, Any, Optional, Union
from datetime import datetime, timedelta
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

class RedisCache:
    """Redis缓存工具类，用于缓存BI报表数据"""

    def __init__(self):
        """初始化Redis缓存"""
        try:
            # 尝试连接Redis
            self.redis_client = redis.Redis(
                host=getattr(settings, 'REDIS_HOST', 'localhost'),
                port=getattr(settings, 'REDIS_PORT', 6379),
                db=getattr(settings, 'REDIS_DB', 0),
                password=getattr(settings, 'REDIS_PASSWORD', None),
                decode_responses=False,  # 使用二进制模式以支持pickle
                socket_timeout=5,
                socket_connect_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )

            # 测试连接
            self.redis_client.ping()
            self.use_redis = True
            logger.info("Redis缓存连接成功")

        except Exception as e:
            logger.warning(f"Redis连接失败，使用内存缓存: {str(e)}")
            # 回退到内存字典模拟
            self.cache = {}
            self.use_redis = False
    
    def _generate_key(self, prefix: str, params: Dict[str, Any]) -> str:
        """生成缓存键"""
        try:
            # 将参数转换为排序后的字符串，然后计算MD5
            param_str = json.dumps(params, sort_keys=True, default=str)
            key = f"bi_cache:{prefix}:{hashlib.md5(param_str.encode()).hexdigest()}"
            return key
        except Exception as e:
            logger.error(f"生成缓存键失败: {str(e)}")
            return f"bi_cache:{prefix}:{int(time.time())}"

    def get_bi_data(self, report_type: str, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """获取BI报表数据"""
        try:
            key = self._generate_key(report_type, params)

            if self.use_redis:
                # 使用Redis
                cached_data = self.redis_client.get(key)
                if cached_data:
                    return pickle.loads(cached_data)
                return None
            else:
                # 使用内存缓存
                cache_item = self.cache.get(key)
                if cache_item:
                    # 检查是否过期
                    if time.time() < cache_item.get('expire_time', 0):
                        return cache_item['data']
                    else:
                        # 过期删除
                        del self.cache[key]
                return None

        except Exception as e:
            logger.error(f"获取缓存数据失败: {str(e)}")
            return None

    def set_bi_data(self, report_type: str, params: Dict[str, Any], data: Dict[str, Any], expire_seconds: int = 3600) -> None:
        """设置BI报表数据"""
        try:
            key = self._generate_key(report_type, params)

            if self.use_redis:
                # 使用Redis
                serialized_data = pickle.dumps(data)
                self.redis_client.setex(key, expire_seconds, serialized_data)
                logger.debug(f"数据已缓存到Redis: {key}, 过期时间: {expire_seconds}秒")
            else:
                # 使用内存缓存
                expire_time = time.time() + expire_seconds
                self.cache[key] = {
                    'data': data,
                    'expire_time': expire_time,
                    'created_at': time.time()
                }
                logger.debug(f"数据已缓存到内存: {key}, 过期时间: {expire_seconds}秒")

        except Exception as e:
            logger.error(f"设置缓存数据失败: {str(e)}")

    def delete_bi_data(self, report_type: str, params: Dict[str, Any]) -> None:
        """删除BI报表数据"""
        try:
            key = self._generate_key(report_type, params)

            if self.use_redis:
                # 使用Redis
                self.redis_client.delete(key)
                logger.debug(f"已从Redis删除缓存: {key}")
            else:
                # 使用内存缓存
                if key in self.cache:
                    del self.cache[key]
                    logger.debug(f"已从内存删除缓存: {key}")

        except Exception as e:
            logger.error(f"删除缓存数据失败: {str(e)}")

    def clear_all_cache(self) -> None:
        """清除所有BI缓存"""
        try:
            if self.use_redis:
                # 使用Redis
                pattern = "bi_cache:*"
                keys = self.redis_client.keys(pattern)
                if keys:
                    self.redis_client.delete(*keys)
                    logger.info(f"已清除{len(keys)}个Redis缓存项")
            else:
                # 使用内存缓存
                cache_count = len(self.cache)
                self.cache.clear()
                logger.info(f"已清除{cache_count}个内存缓存项")

        except Exception as e:
            logger.error(f"清除所有缓存失败: {str(e)}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            if self.use_redis:
                # Redis统计
                info = self.redis_client.info()
                pattern = "bi_cache:*"
                keys = self.redis_client.keys(pattern)

                return {
                    "cache_type": "redis",
                    "total_keys": len(keys),
                    "redis_memory_used": info.get('used_memory_human', 'N/A'),
                    "redis_connected_clients": info.get('connected_clients', 0),
                    "redis_uptime": info.get('uptime_in_seconds', 0)
                }
            else:
                # 内存缓存统计
                total_keys = len(self.cache)
                expired_keys = 0
                current_time = time.time()

                for key, cache_item in self.cache.items():
                    if current_time >= cache_item.get('expire_time', 0):
                        expired_keys += 1

                return {
                    "cache_type": "memory",
                    "total_keys": total_keys,
                    "expired_keys": expired_keys,
                    "valid_keys": total_keys - expired_keys
                }

        except Exception as e:
            logger.error(f"获取缓存统计失败: {str(e)}")
            return {"error": str(e)}

    def cleanup_expired_cache(self) -> int:
        """清理过期缓存（仅适用于内存缓存）"""
        if self.use_redis:
            # Redis自动处理过期
            return 0

        try:
            current_time = time.time()
            expired_keys = []

            for key, cache_item in self.cache.items():
                if current_time >= cache_item.get('expire_time', 0):
                    expired_keys.append(key)

            for key in expired_keys:
                del self.cache[key]

            if expired_keys:
                logger.info(f"清理了{len(expired_keys)}个过期缓存项")

            return len(expired_keys)

        except Exception as e:
            logger.error(f"清理过期缓存失败: {str(e)}")
            return 0

    def exists(self, report_type: str, params: Dict[str, Any]) -> bool:
        """检查缓存是否存在"""
        try:
            key = self._generate_key(report_type, params)

            if self.use_redis:
                return bool(self.redis_client.exists(key))
            else:
                cache_item = self.cache.get(key)
                if cache_item:
                    return time.time() < cache_item.get('expire_time', 0)
                return False

        except Exception as e:
            logger.error(f"检查缓存存在性失败: {str(e)}")
            return False

    def get_ttl(self, report_type: str, params: Dict[str, Any]) -> int:
        """获取缓存剩余生存时间（秒）"""
        try:
            key = self._generate_key(report_type, params)

            if self.use_redis:
                return self.redis_client.ttl(key)
            else:
                cache_item = self.cache.get(key)
                if cache_item:
                    remaining = cache_item.get('expire_time', 0) - time.time()
                    return max(0, int(remaining))
                return -1

        except Exception as e:
            logger.error(f"获取缓存TTL失败: {str(e)}")
            return -1