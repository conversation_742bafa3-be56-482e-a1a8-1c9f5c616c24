"""
权限管理服务
提供细粒度的权限控制和角色管理
"""

from typing import Dict, List, Set, Optional, Any
from enum import Enum
from dataclasses import dataclass
from sqlalchemy.orm import Session
from app.models.employee import Employee
from app.schemas.employee import Employee as EmployeeSchema
import logging

logger = logging.getLogger(__name__)


class Permission(Enum):
    """权限枚举"""
    # 系统管理权限
    SYSTEM_ADMIN = "system:admin"
    SYSTEM_CONFIG = "system:config"
    SYSTEM_MONITOR = "system:monitor"
    
    # 用户管理权限
    USER_CREATE = "user:create"
    USER_READ = "user:read"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    USER_MANAGE_ROLES = "user:manage_roles"
    
    # 门店管理权限
    STORE_CREATE = "store:create"
    STORE_READ = "store:read"
    STORE_UPDATE = "store:update"
    STORE_DELETE = "store:delete"
    STORE_MANAGE = "store:manage"
    
    # 客户管理权限
    CUSTOMER_CREATE = "customer:create"
    CUSTOMER_READ = "customer:read"
    CUSTOMER_UPDATE = "customer:update"
    CUSTOMER_DELETE = "customer:delete"
    CUSTOMER_EXPORT = "customer:export"
    
    # 订单管理权限
    ORDER_CREATE = "order:create"
    ORDER_READ = "order:read"
    ORDER_UPDATE = "order:update"
    ORDER_DELETE = "order:delete"
    ORDER_REFUND = "order:refund"
    ORDER_EXPORT = "order:export"
    
    # 项目管理权限
    ITEM_CREATE = "item:create"
    ITEM_READ = "item:read"
    ITEM_UPDATE = "item:update"
    ITEM_DELETE = "item:delete"
    ITEM_PRICING = "item:pricing"
    
    # BI分析权限
    BI_VIEW_BASIC = "bi:view_basic"
    BI_VIEW_ADVANCED = "bi:view_advanced"
    BI_EXPORT = "bi:export"
    BI_MANAGE_REPORTS = "bi:manage_reports"
    
    # 财务权限
    FINANCE_VIEW = "finance:view"
    FINANCE_MANAGE = "finance:manage"
    FINANCE_AUDIT = "finance:audit"
    
    # 提成权限
    COMMISSION_VIEW = "commission:view"
    COMMISSION_MANAGE = "commission:manage"
    COMMISSION_CALCULATE = "commission:calculate"


class Role(Enum):
    """角色枚举"""
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    STORE_MANAGER = "store_manager"
    CASHIER = "cashier"
    THERAPIST = "therapist"
    FINANCE = "finance"
    ANALYST = "analyst"
    VIEWER = "viewer"


@dataclass
class RolePermissions:
    """角色权限配置"""
    role: Role
    permissions: Set[Permission]
    description: str


class PermissionService:
    """权限管理服务"""
    
    def __init__(self):
        self.role_permissions = self._initialize_role_permissions()
    
    def _initialize_role_permissions(self) -> Dict[Role, RolePermissions]:
        """初始化角色权限配置"""
        return {
            Role.SUPER_ADMIN: RolePermissions(
                role=Role.SUPER_ADMIN,
                permissions=set(Permission),  # 所有权限
                description="超级管理员，拥有所有权限"
            ),
            
            Role.ADMIN: RolePermissions(
                role=Role.ADMIN,
                permissions={
                    Permission.SYSTEM_CONFIG,
                    Permission.SYSTEM_MONITOR,
                    Permission.USER_CREATE,
                    Permission.USER_READ,
                    Permission.USER_UPDATE,
                    Permission.USER_MANAGE_ROLES,
                    Permission.STORE_CREATE,
                    Permission.STORE_READ,
                    Permission.STORE_UPDATE,
                    Permission.STORE_MANAGE,
                    Permission.CUSTOMER_CREATE,
                    Permission.CUSTOMER_READ,
                    Permission.CUSTOMER_UPDATE,
                    Permission.CUSTOMER_EXPORT,
                    Permission.ORDER_CREATE,
                    Permission.ORDER_READ,
                    Permission.ORDER_UPDATE,
                    Permission.ORDER_REFUND,
                    Permission.ORDER_EXPORT,
                    Permission.ITEM_CREATE,
                    Permission.ITEM_READ,
                    Permission.ITEM_UPDATE,
                    Permission.ITEM_PRICING,
                    Permission.BI_VIEW_BASIC,
                    Permission.BI_VIEW_ADVANCED,
                    Permission.BI_EXPORT,
                    Permission.BI_MANAGE_REPORTS,
                    Permission.FINANCE_VIEW,
                    Permission.FINANCE_MANAGE,
                    Permission.COMMISSION_VIEW,
                    Permission.COMMISSION_MANAGE,
                    Permission.COMMISSION_CALCULATE
                },
                description="管理员，拥有大部分管理权限"
            ),
            
            Role.STORE_MANAGER: RolePermissions(
                role=Role.STORE_MANAGER,
                permissions={
                    Permission.STORE_READ,
                    Permission.STORE_UPDATE,
                    Permission.CUSTOMER_CREATE,
                    Permission.CUSTOMER_READ,
                    Permission.CUSTOMER_UPDATE,
                    Permission.ORDER_CREATE,
                    Permission.ORDER_READ,
                    Permission.ORDER_UPDATE,
                    Permission.ORDER_REFUND,
                    Permission.ITEM_READ,
                    Permission.ITEM_UPDATE,
                    Permission.BI_VIEW_BASIC,
                    Permission.BI_EXPORT,
                    Permission.FINANCE_VIEW,
                    Permission.COMMISSION_VIEW
                },
                description="门店经理，管理单个门店的日常运营"
            ),
            
            Role.CASHIER: RolePermissions(
                role=Role.CASHIER,
                permissions={
                    Permission.CUSTOMER_CREATE,
                    Permission.CUSTOMER_READ,
                    Permission.CUSTOMER_UPDATE,
                    Permission.ORDER_CREATE,
                    Permission.ORDER_READ,
                    Permission.ORDER_UPDATE,
                    Permission.ITEM_READ
                },
                description="收银员，处理订单和客户信息"
            ),
            
            Role.THERAPIST: RolePermissions(
                role=Role.THERAPIST,
                permissions={
                    Permission.CUSTOMER_READ,
                    Permission.ORDER_READ,
                    Permission.ITEM_READ,
                    Permission.COMMISSION_VIEW
                },
                description="技师，查看客户和订单信息，查看个人提成"
            ),
            
            Role.FINANCE: RolePermissions(
                role=Role.FINANCE,
                permissions={
                    Permission.ORDER_READ,
                    Permission.ORDER_EXPORT,
                    Permission.BI_VIEW_BASIC,
                    Permission.BI_VIEW_ADVANCED,
                    Permission.BI_EXPORT,
                    Permission.FINANCE_VIEW,
                    Permission.FINANCE_MANAGE,
                    Permission.FINANCE_AUDIT,
                    Permission.COMMISSION_VIEW,
                    Permission.COMMISSION_CALCULATE
                },
                description="财务人员，管理财务相关功能"
            ),
            
            Role.ANALYST: RolePermissions(
                role=Role.ANALYST,
                permissions={
                    Permission.CUSTOMER_READ,
                    Permission.ORDER_READ,
                    Permission.ITEM_READ,
                    Permission.BI_VIEW_BASIC,
                    Permission.BI_VIEW_ADVANCED,
                    Permission.BI_EXPORT,
                    Permission.BI_MANAGE_REPORTS
                },
                description="数据分析师，专注于数据分析和报表"
            ),
            
            Role.VIEWER: RolePermissions(
                role=Role.VIEWER,
                permissions={
                    Permission.CUSTOMER_READ,
                    Permission.ORDER_READ,
                    Permission.ITEM_READ,
                    Permission.BI_VIEW_BASIC
                },
                description="查看者，只能查看基础信息"
            )
        }
    
    def get_user_permissions(self, user: EmployeeSchema) -> Set[Permission]:
        """获取用户权限"""
        try:
            # 将字符串角色转换为Role枚举
            user_role = self._get_role_from_string(user.role)
            if user_role and user_role in self.role_permissions:
                return self.role_permissions[user_role].permissions
            return set()
        except Exception as e:
            logger.error(f"获取用户权限失败: {str(e)}")
            return set()
    
    def has_permission(self, user: EmployeeSchema, permission: Permission) -> bool:
        """检查用户是否有指定权限"""
        try:
            user_permissions = self.get_user_permissions(user)
            return permission in user_permissions
        except Exception as e:
            logger.error(f"检查权限失败: {str(e)}")
            return False
    
    def has_any_permission(self, user: EmployeeSchema, permissions: List[Permission]) -> bool:
        """检查用户是否有任一权限"""
        try:
            user_permissions = self.get_user_permissions(user)
            return any(perm in user_permissions for perm in permissions)
        except Exception as e:
            logger.error(f"检查权限失败: {str(e)}")
            return False
    
    def has_all_permissions(self, user: EmployeeSchema, permissions: List[Permission]) -> bool:
        """检查用户是否有所有权限"""
        try:
            user_permissions = self.get_user_permissions(user)
            return all(perm in user_permissions for perm in permissions)
        except Exception as e:
            logger.error(f"检查权限失败: {str(e)}")
            return False
    
    def can_access_store(self, user: EmployeeSchema, store_id: int) -> bool:
        """检查用户是否可以访问指定门店"""
        try:
            # 超级管理员和管理员可以访问所有门店
            if user.role in ["super_admin", "admin"]:
                return True
            
            # 其他角色只能访问自己所属的门店
            return user.store_id == store_id
        except Exception as e:
            logger.error(f"检查门店访问权限失败: {str(e)}")
            return False
    
    def can_manage_user(self, manager: EmployeeSchema, target_user: EmployeeSchema) -> bool:
        """检查是否可以管理目标用户"""
        try:
            # 不能管理自己
            if manager.id == target_user.id:
                return False
            
            # 超级管理员可以管理所有人
            if manager.role == "super_admin":
                return True
            
            # 管理员可以管理非超级管理员
            if manager.role == "admin" and target_user.role != "super_admin":
                return True
            
            # 门店经理只能管理同门店的非管理员用户
            if (manager.role == "store_manager" and 
                manager.store_id == target_user.store_id and
                target_user.role not in ["super_admin", "admin"]):
                return True
            
            return False
        except Exception as e:
            logger.error(f"检查用户管理权限失败: {str(e)}")
            return False
    
    def get_accessible_stores(self, user: EmployeeSchema, db: Session) -> List[int]:
        """获取用户可访问的门店列表"""
        try:
            from app.models.store import Store
            
            # 超级管理员和管理员可以访问所有门店
            if user.role in ["super_admin", "admin"]:
                stores = db.query(Store).filter(Store.is_deleted == False).all()
                return [store.id for store in stores]
            
            # 其他角色只能访问自己所属的门店
            if user.store_id:
                return [user.store_id]
            
            return []
        except Exception as e:
            logger.error(f"获取可访问门店失败: {str(e)}")
            return []
    
    def get_role_info(self, role_name: str) -> Optional[RolePermissions]:
        """获取角色信息"""
        try:
            role = self._get_role_from_string(role_name)
            if role and role in self.role_permissions:
                return self.role_permissions[role]
            return None
        except Exception as e:
            logger.error(f"获取角色信息失败: {str(e)}")
            return None
    
    def get_all_roles(self) -> List[Dict[str, Any]]:
        """获取所有角色信息"""
        try:
            roles = []
            for role, role_perms in self.role_permissions.items():
                roles.append({
                    "role": role.value,
                    "description": role_perms.description,
                    "permissions": [perm.value for perm in role_perms.permissions]
                })
            return roles
        except Exception as e:
            logger.error(f"获取所有角色失败: {str(e)}")
            return []
    
    def _get_role_from_string(self, role_str: str) -> Optional[Role]:
        """从字符串获取Role枚举"""
        try:
            role_mapping = {
                "super_admin": Role.SUPER_ADMIN,
                "admin": Role.ADMIN,
                "store_manager": Role.STORE_MANAGER,
                "cashier": Role.CASHIER,
                "therapist": Role.THERAPIST,
                "finance": Role.FINANCE,
                "analyst": Role.ANALYST,
                "viewer": Role.VIEWER
            }
            return role_mapping.get(role_str)
        except Exception as e:
            logger.error(f"角色字符串转换失败: {str(e)}")
            return None
    
    def validate_role_assignment(self, assigner: EmployeeSchema, target_role: str) -> bool:
        """验证角色分配是否合法"""
        try:
            # 超级管理员可以分配任何角色
            if assigner.role == "super_admin":
                return True
            
            # 管理员不能分配超级管理员角色
            if assigner.role == "admin" and target_role != "super_admin":
                return True
            
            # 门店经理只能分配基础角色
            if (assigner.role == "store_manager" and 
                target_role in ["cashier", "therapist", "viewer"]):
                return True
            
            return False
        except Exception as e:
            logger.error(f"验证角色分配失败: {str(e)}")
            return False


# 全局权限服务实例
permission_service = PermissionService()


def require_permission(permission: Permission):
    """权限装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 从kwargs中获取current_employee
            current_employee = kwargs.get('current_employee')
            if not current_employee:
                raise Exception("未找到当前用户信息")
            
            if not permission_service.has_permission(current_employee, permission):
                raise Exception(f"权限不足，需要权限: {permission.value}")
            
            return func(*args, **kwargs)
        return wrapper
    return decorator


def require_any_permission(permissions: List[Permission]):
    """需要任一权限的装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            current_employee = kwargs.get('current_employee')
            if not current_employee:
                raise Exception("未找到当前用户信息")
            
            if not permission_service.has_any_permission(current_employee, permissions):
                perm_names = [p.value for p in permissions]
                raise Exception(f"权限不足，需要以下任一权限: {perm_names}")
            
            return func(*args, **kwargs)
        return wrapper
    return decorator
