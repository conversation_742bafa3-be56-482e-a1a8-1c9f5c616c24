"""
报表生成服务
支持自定义报表模板、定时报表生成、报表订阅和分发
"""

import os
import json
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.services.export_service import export_service
from app.services.query_optimizer import QueryOptimizer
from app.services.advanced_analytics import AdvancedAnalyticsService
from app.utils.redis_cache import RedisCache
import logging

logger = logging.getLogger(__name__)


class ReportType(Enum):
    """报表类型"""
    DAILY_SUMMARY = "daily_summary"
    WEEKLY_SUMMARY = "weekly_summary"
    MONTHLY_SUMMARY = "monthly_summary"
    SALES_ANALYSIS = "sales_analysis"
    CUSTOMER_ANALYSIS = "customer_analysis"
    STORE_PERFORMANCE = "store_performance"
    FINANCIAL_REPORT = "financial_report"
    CUSTOM = "custom"


class ReportFormat(Enum):
    """报表格式"""
    PDF = "pdf"
    EXCEL = "excel"
    CSV = "csv"
    JSON = "json"


class ReportFrequency(Enum):
    """报表频率"""
    ONCE = "once"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"


@dataclass
class ReportTemplate:
    """报表模板"""
    id: str
    name: str
    description: str
    report_type: ReportType
    template_config: Dict[str, Any]
    default_params: Dict[str, Any]
    created_at: datetime
    updated_at: datetime


@dataclass
class ReportTask:
    """报表任务"""
    id: str
    template_id: str
    name: str
    description: str
    frequency: ReportFrequency
    format: ReportFormat
    recipients: List[str]
    params: Dict[str, Any]
    next_run_time: datetime
    last_run_time: Optional[datetime]
    is_active: bool
    created_by: str
    created_at: datetime


class ReportGenerator:
    """报表生成器"""
    
    def __init__(self):
        self.cache = RedisCache()
        self.templates = self._load_default_templates()
        self.tasks = {}
        self.report_history = []
        
        # 确保报表目录存在
        os.makedirs('reports', exist_ok=True)
    
    def _load_default_templates(self) -> Dict[str, ReportTemplate]:
        """加载默认报表模板"""
        templates = {}
        
        # 日报模板
        templates["daily_summary"] = ReportTemplate(
            id="daily_summary",
            name="日营业报表",
            description="每日营业数据汇总报表",
            report_type=ReportType.DAILY_SUMMARY,
            template_config={
                "sections": [
                    {
                        "name": "营业概况",
                        "type": "summary",
                        "metrics": ["total_orders", "total_revenue", "avg_order_value", "customer_count"]
                    },
                    {
                        "name": "门店表现",
                        "type": "table",
                        "data_source": "store_performance"
                    },
                    {
                        "name": "热销项目",
                        "type": "chart",
                        "chart_type": "bar",
                        "data_source": "top_items"
                    }
                ]
            },
            default_params={
                "date_range": "today",
                "include_charts": True,
                "include_details": True
            },
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # 周报模板
        templates["weekly_summary"] = ReportTemplate(
            id="weekly_summary",
            name="周营业报表",
            description="每周营业数据分析报表",
            report_type=ReportType.WEEKLY_SUMMARY,
            template_config={
                "sections": [
                    {
                        "name": "周度概况",
                        "type": "summary",
                        "metrics": ["total_orders", "total_revenue", "growth_rate", "customer_retention"]
                    },
                    {
                        "name": "趋势分析",
                        "type": "chart",
                        "chart_type": "line",
                        "data_source": "daily_trends"
                    },
                    {
                        "name": "同比分析",
                        "type": "comparison",
                        "comparison_type": "year_over_year"
                    }
                ]
            },
            default_params={
                "date_range": "this_week",
                "include_comparison": True,
                "include_forecast": True
            },
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # 月报模板
        templates["monthly_summary"] = ReportTemplate(
            id="monthly_summary",
            name="月度经营报表",
            description="月度经营数据深度分析报表",
            report_type=ReportType.MONTHLY_SUMMARY,
            template_config={
                "sections": [
                    {
                        "name": "月度总结",
                        "type": "executive_summary"
                    },
                    {
                        "name": "财务分析",
                        "type": "financial_analysis"
                    },
                    {
                        "name": "客户分析",
                        "type": "customer_analysis"
                    },
                    {
                        "name": "运营指标",
                        "type": "operational_metrics"
                    }
                ]
            },
            default_params={
                "date_range": "this_month",
                "include_advanced_analytics": True,
                "include_recommendations": True
            },
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        return templates
    
    async def generate_report(
        self,
        template_id: str,
        params: Dict[str, Any],
        format: ReportFormat = ReportFormat.PDF,
        db: Session = None
    ) -> Dict[str, Any]:
        """生成报表"""
        try:
            if not db:
                db = SessionLocal()
            
            template = self.templates.get(template_id)
            if not template:
                raise ValueError(f"报表模板不存在: {template_id}")
            
            # 合并参数
            merged_params = {**template.default_params, **params}
            
            # 解析日期范围
            start_date, end_date = self._parse_date_range(merged_params.get("date_range", "today"))
            
            # 收集报表数据
            report_data = await self._collect_report_data(template, start_date, end_date, merged_params, db)
            
            # 生成报表内容
            report_content = await self._generate_report_content(template, report_data, merged_params)
            
            # 导出报表文件
            file_data = await self._export_report(report_content, format, template.name)
            
            # 保存报表记录
            report_record = {
                "id": f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "template_id": template_id,
                "template_name": template.name,
                "format": format.value,
                "params": merged_params,
                "generated_at": datetime.now().isoformat(),
                "file_size": len(file_data) if file_data else 0,
                "status": "success"
            }
            
            self.report_history.append(report_record)
            
            return {
                "status": "success",
                "report_id": report_record["id"],
                "template_name": template.name,
                "format": format.value,
                "file_data": file_data,
                "file_size": len(file_data) if file_data else 0,
                "generated_at": report_record["generated_at"]
            }
            
        except Exception as e:
            logger.error(f"生成报表失败: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    async def _collect_report_data(
        self,
        template: ReportTemplate,
        start_date: datetime,
        end_date: datetime,
        params: Dict[str, Any],
        db: Session
    ) -> Dict[str, Any]:
        """收集报表数据"""
        try:
            optimizer = QueryOptimizer(db)
            analytics = AdvancedAnalyticsService(db)
            
            data = {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "days": (end_date - start_date).days + 1
                }
            }
            
            # 基础营业数据
            sales_data = optimizer.get_aggregated_sales_data(
                start_date=start_date,
                end_date=end_date,
                group_by="day"
            )
            
            # 汇总指标
            total_orders = sum(item['order_count'] for item in sales_data)
            total_revenue = sum(item['total_sales'] for item in sales_data)
            avg_order_value = total_revenue / total_orders if total_orders > 0 else 0
            
            data["summary"] = {
                "total_orders": total_orders,
                "total_revenue": total_revenue,
                "avg_order_value": avg_order_value,
                "daily_average": total_revenue / len(sales_data) if sales_data else 0
            }
            
            # 趋势数据
            data["trends"] = sales_data
            
            # 客户分析
            customer_data = optimizer.get_customer_analytics(
                start_date=start_date,
                end_date=end_date
            )
            data["customer_analysis"] = customer_data
            
            # 如果需要高级分析
            if params.get("include_advanced_analytics"):
                # 同比分析
                yoy_analysis = analytics.year_over_year_analysis(
                    current_start=start_date,
                    current_end=end_date
                )
                data["year_over_year"] = yoy_analysis
                
                # 趋势分析
                trend_analysis = analytics.trend_analysis(
                    start_date=start_date,
                    end_date=end_date,
                    metric="total_revenue"
                )
                data["trend_analysis"] = trend_analysis
            
            return data
            
        except Exception as e:
            logger.error(f"收集报表数据失败: {str(e)}")
            raise
    
    async def _generate_report_content(
        self,
        template: ReportTemplate,
        data: Dict[str, Any],
        params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成报表内容"""
        try:
            content = {
                "title": template.name,
                "subtitle": f"报表期间: {data['period']['start_date']} 至 {data['period']['end_date']}",
                "generated_at": datetime.now().isoformat(),
                "sections": []
            }
            
            # 根据模板配置生成各个部分
            for section_config in template.template_config.get("sections", []):
                section = await self._generate_section(section_config, data, params)
                content["sections"].append(section)
            
            return content
            
        except Exception as e:
            logger.error(f"生成报表内容失败: {str(e)}")
            raise
    
    async def _generate_section(
        self,
        section_config: Dict[str, Any],
        data: Dict[str, Any],
        params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成报表章节"""
        try:
            section = {
                "name": section_config["name"],
                "type": section_config["type"],
                "content": {}
            }
            
            section_type = section_config["type"]
            
            if section_type == "summary":
                # 汇总数据
                metrics = section_config.get("metrics", [])
                summary_data = {}
                for metric in metrics:
                    if metric in data.get("summary", {}):
                        summary_data[metric] = data["summary"][metric]
                section["content"] = summary_data
                
            elif section_type == "table":
                # 表格数据
                data_source = section_config.get("data_source")
                if data_source in data:
                    section["content"] = data[data_source]
                    
            elif section_type == "chart":
                # 图表数据
                chart_type = section_config.get("chart_type", "bar")
                data_source = section_config.get("data_source", "trends")
                
                if data_source in data:
                    section["content"] = {
                        "chart_type": chart_type,
                        "data": data[data_source]
                    }
                    
            elif section_type == "comparison":
                # 对比分析
                comparison_type = section_config.get("comparison_type")
                if comparison_type == "year_over_year" and "year_over_year" in data:
                    section["content"] = data["year_over_year"]
                    
            elif section_type == "executive_summary":
                # 执行摘要
                section["content"] = self._generate_executive_summary(data)
                
            elif section_type == "financial_analysis":
                # 财务分析
                section["content"] = self._generate_financial_analysis(data)
                
            elif section_type == "customer_analysis":
                # 客户分析
                section["content"] = data.get("customer_analysis", {})
                
            elif section_type == "operational_metrics":
                # 运营指标
                section["content"] = self._generate_operational_metrics(data)
            
            return section
            
        except Exception as e:
            logger.error(f"生成报表章节失败: {str(e)}")
            return {"name": section_config["name"], "type": "error", "content": {"error": str(e)}}
    
    def _generate_executive_summary(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """生成执行摘要"""
        summary = data.get("summary", {})
        period = data.get("period", {})
        
        insights = []
        
        # 营收洞察
        total_revenue = summary.get("total_revenue", 0)
        daily_average = summary.get("daily_average", 0)
        
        if total_revenue > 0:
            insights.append(f"报表期间总营收为 ¥{total_revenue:,.2f}，日均营收 ¥{daily_average:,.2f}")
        
        # 订单洞察
        total_orders = summary.get("total_orders", 0)
        avg_order_value = summary.get("avg_order_value", 0)
        
        if total_orders > 0:
            insights.append(f"共处理 {total_orders} 笔订单，平均订单价值 ¥{avg_order_value:.2f}")
        
        # 趋势洞察
        trends = data.get("trends", [])
        if len(trends) >= 2:
            first_day = trends[0]['total_sales']
            last_day = trends[-1]['total_sales']
            if first_day > 0:
                growth = ((last_day - first_day) / first_day) * 100
                trend_desc = "上升" if growth > 0 else "下降"
                insights.append(f"期间营收呈{trend_desc}趋势，变化幅度 {abs(growth):.1f}%")
        
        return {
            "key_insights": insights,
            "performance_summary": summary,
            "period_info": period
        }
    
    def _generate_financial_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """生成财务分析"""
        summary = data.get("summary", {})
        
        return {
            "revenue_analysis": {
                "total_revenue": summary.get("total_revenue", 0),
                "daily_average": summary.get("daily_average", 0),
                "growth_trend": "stable"  # 简化处理
            },
            "cost_analysis": {
                "estimated_costs": summary.get("total_revenue", 0) * 0.7,  # 假设成本率70%
                "profit_margin": 0.3  # 假设利润率30%
            },
            "recommendations": [
                "建议关注高价值客户的维护",
                "优化成本结构以提高利润率",
                "加强营销活动以提升营收"
            ]
        }
    
    def _generate_operational_metrics(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """生成运营指标"""
        summary = data.get("summary", {})
        customer_analysis = data.get("customer_analysis", {})
        
        return {
            "efficiency_metrics": {
                "orders_per_day": summary.get("total_orders", 0) / data.get("period", {}).get("days", 1),
                "revenue_per_order": summary.get("avg_order_value", 0),
                "customer_satisfaction": 0.85  # 模拟数据
            },
            "customer_metrics": {
                "total_customers": customer_analysis.get("total_customers", 0),
                "repeat_customers": customer_analysis.get("repeat_customers", 0),
                "retention_rate": customer_analysis.get("repeat_rate", 0)
            },
            "growth_indicators": {
                "revenue_growth": 0.15,  # 模拟15%增长
                "customer_growth": 0.12,  # 模拟12%增长
                "market_expansion": "stable"
            }
        }
    
    async def _export_report(
        self,
        content: Dict[str, Any],
        format: ReportFormat,
        report_name: str
    ) -> bytes:
        """导出报表文件"""
        try:
            if format == ReportFormat.JSON:
                return json.dumps(content, ensure_ascii=False, indent=2).encode('utf-8')
            
            # 转换为表格数据格式
            table_data = self._convert_to_table_data(content)
            
            if format == ReportFormat.CSV:
                return export_service.export_to_csv(table_data)
            elif format == ReportFormat.EXCEL:
                return export_service.export_to_excel(
                    data=table_data,
                    sheet_name="报表数据",
                    template_config={"title": content["title"]}
                )
            elif format == ReportFormat.PDF:
                return export_service.export_to_pdf(
                    data=table_data,
                    title=content["title"]
                )
            else:
                raise ValueError(f"不支持的报表格式: {format}")
                
        except Exception as e:
            logger.error(f"导出报表失败: {str(e)}")
            raise
    
    def _convert_to_table_data(self, content: Dict[str, Any]) -> List[Dict[str, Any]]:
        """将报表内容转换为表格数据"""
        table_data = []
        
        # 添加报表基本信息
        table_data.append({
            "类型": "报表信息",
            "项目": "报表标题",
            "值": content.get("title", ""),
            "备注": content.get("subtitle", "")
        })
        
        table_data.append({
            "类型": "报表信息",
            "项目": "生成时间",
            "值": content.get("generated_at", ""),
            "备注": ""
        })
        
        # 处理各个章节
        for section in content.get("sections", []):
            section_name = section.get("name", "")
            section_content = section.get("content", {})
            
            if isinstance(section_content, dict):
                for key, value in section_content.items():
                    table_data.append({
                        "类型": section_name,
                        "项目": key,
                        "值": str(value),
                        "备注": ""
                    })
            elif isinstance(section_content, list):
                for i, item in enumerate(section_content):
                    table_data.append({
                        "类型": section_name,
                        "项目": f"项目{i+1}",
                        "值": str(item),
                        "备注": ""
                    })
        
        return table_data
    
    def _parse_date_range(self, date_range: str) -> tuple[datetime, datetime]:
        """解析日期范围"""
        now = datetime.now()
        
        if date_range == "today":
            start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end = start + timedelta(days=1) - timedelta(microseconds=1)
        elif date_range == "yesterday":
            start = (now - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
            end = start + timedelta(days=1) - timedelta(microseconds=1)
        elif date_range == "this_week":
            start = now - timedelta(days=now.weekday())
            start = start.replace(hour=0, minute=0, second=0, microsecond=0)
            end = start + timedelta(days=7) - timedelta(microseconds=1)
        elif date_range == "this_month":
            start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            next_month = start.replace(month=start.month + 1) if start.month < 12 else start.replace(year=start.year + 1, month=1)
            end = next_month - timedelta(microseconds=1)
        else:
            # 默认为最近7天
            end = now
            start = now - timedelta(days=7)
        
        return start, end
    
    def get_templates(self) -> List[Dict[str, Any]]:
        """获取所有报表模板"""
        return [
            {
                "id": template.id,
                "name": template.name,
                "description": template.description,
                "type": template.report_type.value,
                "created_at": template.created_at.isoformat(),
                "updated_at": template.updated_at.isoformat()
            }
            for template in self.templates.values()
        ]
    
    def get_report_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取报表历史记录"""
        return self.report_history[-limit:]


# 全局报表生成器实例
report_generator = ReportGenerator()
