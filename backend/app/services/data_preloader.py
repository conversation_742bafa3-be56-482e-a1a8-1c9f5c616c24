"""
数据预加载服务
在系统启动时或定时预加载常用的BI数据到缓存中
"""

import asyncio
import schedule
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.utils.redis_cache import RedisCache
from app.services.query_optimizer import QueryOptimizer
from app.models.operation import Order
from app.models.store import Store
import logging

logger = logging.getLogger(__name__)


class DataPreloader:
    """数据预加载器"""

    def __init__(self):
        self.cache = RedisCache()
        self.preload_tasks = []
        self.is_running = False
        self.access_patterns = {}  # 访问模式统计
        
        # 预加载配置
        self.preload_config = {
            "business_indicators": {
                "enabled": True,
                "cache_time": 1800,  # 30分钟
                "preload_periods": [
                    {"days": 7, "name": "last_week"},
                    {"days": 30, "name": "last_month"},
                    {"days": 90, "name": "last_quarter"}
                ]
            },
            "sales_trends": {
                "enabled": True,
                "cache_time": 3600,  # 1小时
                "group_by": ["day", "week", "month"]
            },
            "top_items": {
                "enabled": True,
                "cache_time": 7200,  # 2小时
                "limits": [10, 20, 50]
            },
            "customer_analytics": {
                "enabled": True,
                "cache_time": 3600,  # 1小时
                "periods": [7, 30, 90]  # 天数
            }
        }
    
    def get_active_stores(self, db: Session) -> List[int]:
        """获取活跃门店ID列表"""
        try:
            stores = db.query(Store).filter(
                Store.status == "active",
                Store.is_deleted == False
            ).all()
            return [store.id for store in stores]
        except Exception as e:
            logger.error(f"获取活跃门店失败: {str(e)}")
            return []
    
    def preload_business_indicators(self, db: Session) -> Dict[str, Any]:
        """预加载业务指标数据"""
        results = {"success": 0, "failed": 0, "details": []}
        
        if not self.preload_config["business_indicators"]["enabled"]:
            return results
        
        try:
            optimizer = QueryOptimizer(db)
            store_ids = self.get_active_stores(db)
            
            for period in self.preload_config["business_indicators"]["preload_periods"]:
                try:
                    # 计算日期范围
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=period["days"])
                    
                    # 为每个门店组合预加载数据
                    store_combinations = [
                        None,  # 全部门店
                        store_ids,  # 所有活跃门店
                    ]
                    
                    # 添加单个门店
                    for store_id in store_ids[:5]:  # 限制前5个门店避免过多缓存
                        store_combinations.append([store_id])
                    
                    for stores in store_combinations:
                        cache_key = f"business_indicators_{period['name']}"
                        if stores:
                            if len(stores) == 1:
                                cache_key += f"_store_{stores[0]}"
                            else:
                                cache_key += f"_stores_{len(stores)}"
                        
                        # 获取聚合销售数据
                        sales_data = optimizer.get_aggregated_sales_data(
                            start_date=start_date,
                            end_date=end_date,
                            store_ids=stores,
                            group_by="day"
                        )
                        
                        # 获取客户分析数据
                        customer_data = optimizer.get_customer_analytics(
                            start_date=start_date,
                            end_date=end_date,
                            store_ids=stores
                        )
                        
                        # 组合数据
                        combined_data = {
                            "sales_data": sales_data,
                            "customer_data": customer_data,
                            "period": period["name"],
                            "start_date": start_date.isoformat(),
                            "end_date": end_date.isoformat(),
                            "store_ids": stores,
                            "generated_at": datetime.now().isoformat()
                        }
                        
                        # 存储到缓存
                        self.cache.set_bi_data(
                            cache_key,
                            {"period": period["name"], "stores": stores or []},
                            combined_data,
                            self.preload_config["business_indicators"]["cache_time"]
                        )
                        
                        results["success"] += 1
                        results["details"].append({
                            "type": "business_indicators",
                            "key": cache_key,
                            "period": period["name"],
                            "stores": len(stores) if stores else "all",
                            "records": len(sales_data)
                        })
                        
                except Exception as e:
                    logger.error(f"预加载业务指标失败 {period['name']}: {str(e)}")
                    results["failed"] += 1
                    
        except Exception as e:
            logger.error(f"预加载业务指标总体失败: {str(e)}")
            results["failed"] += 1
        
        return results
    
    def preload_sales_trends(self, db: Session) -> Dict[str, Any]:
        """预加载销售趋势数据"""
        results = {"success": 0, "failed": 0, "details": []}
        
        if not self.preload_config["sales_trends"]["enabled"]:
            return results
        
        try:
            optimizer = QueryOptimizer(db)
            store_ids = self.get_active_stores(db)
            
            # 预加载不同时间粒度的趋势数据
            for group_by in self.preload_config["sales_trends"]["group_by"]:
                try:
                    # 根据分组类型确定时间范围
                    if group_by == "day":
                        days = 30
                    elif group_by == "week":
                        days = 90
                    else:  # month
                        days = 365
                    
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=days)
                    
                    # 为不同门店组合预加载
                    store_combinations = [None, store_ids]
                    
                    for stores in store_combinations:
                        cache_key = f"sales_trends_{group_by}"
                        if stores:
                            cache_key += f"_stores_{len(stores)}"
                        
                        # 获取聚合数据
                        trend_data = optimizer.get_aggregated_sales_data(
                            start_date=start_date,
                            end_date=end_date,
                            store_ids=stores,
                            group_by=group_by
                        )
                        
                        # 存储到缓存
                        self.cache.set_bi_data(
                            cache_key,
                            {"group_by": group_by, "stores": stores or []},
                            {
                                "trend_data": trend_data,
                                "group_by": group_by,
                                "start_date": start_date.isoformat(),
                                "end_date": end_date.isoformat(),
                                "store_ids": stores,
                                "generated_at": datetime.now().isoformat()
                            },
                            self.preload_config["sales_trends"]["cache_time"]
                        )
                        
                        results["success"] += 1
                        results["details"].append({
                            "type": "sales_trends",
                            "key": cache_key,
                            "group_by": group_by,
                            "stores": len(stores) if stores else "all",
                            "records": len(trend_data)
                        })
                        
                except Exception as e:
                    logger.error(f"预加载销售趋势失败 {group_by}: {str(e)}")
                    results["failed"] += 1
                    
        except Exception as e:
            logger.error(f"预加载销售趋势总体失败: {str(e)}")
            results["failed"] += 1
        
        return results
    
    def preload_top_items(self, db: Session) -> Dict[str, Any]:
        """预加载热销项目数据"""
        results = {"success": 0, "failed": 0, "details": []}
        
        if not self.preload_config["top_items"]["enabled"]:
            return results
        
        try:
            optimizer = QueryOptimizer(db)
            store_ids = self.get_active_stores(db)
            
            # 预加载不同时间段的热销数据
            periods = [7, 30, 90]  # 天数
            
            for days in periods:
                for limit in self.preload_config["top_items"]["limits"]:
                    try:
                        end_date = datetime.now()
                        start_date = end_date - timedelta(days=days)
                        
                        # 为不同门店组合预加载
                        store_combinations = [None, store_ids]
                        
                        for stores in store_combinations:
                            cache_key = f"top_items_{days}d_limit_{limit}"
                            if stores:
                                cache_key += f"_stores_{len(stores)}"
                            
                            # 获取热销项目数据
                            top_items = optimizer.get_top_performing_items(
                                start_date=start_date,
                                end_date=end_date,
                                store_ids=stores,
                                limit=limit
                            )
                            
                            # 存储到缓存
                            self.cache.set_bi_data(
                                cache_key,
                                {"days": days, "limit": limit, "stores": stores or []},
                                {
                                    "top_items": top_items,
                                    "period_days": days,
                                    "limit": limit,
                                    "start_date": start_date.isoformat(),
                                    "end_date": end_date.isoformat(),
                                    "store_ids": stores,
                                    "generated_at": datetime.now().isoformat()
                                },
                                self.preload_config["top_items"]["cache_time"]
                            )
                            
                            results["success"] += 1
                            results["details"].append({
                                "type": "top_items",
                                "key": cache_key,
                                "period_days": days,
                                "limit": limit,
                                "stores": len(stores) if stores else "all",
                                "records": len(top_items)
                            })
                            
                    except Exception as e:
                        logger.error(f"预加载热销项目失败 {days}d limit {limit}: {str(e)}")
                        results["failed"] += 1
                        
        except Exception as e:
            logger.error(f"预加载热销项目总体失败: {str(e)}")
            results["failed"] += 1
        
        return results

    def record_access_pattern(self, endpoint: str, params: Dict[str, Any]):
        """记录API访问模式"""
        try:
            pattern_key = f"{endpoint}_{hash(str(sorted(params.items())))}"
            current_time = datetime.now()

            if pattern_key not in self.access_patterns:
                self.access_patterns[pattern_key] = {
                    "endpoint": endpoint,
                    "params": params,
                    "access_count": 0,
                    "last_access": current_time,
                    "first_access": current_time
                }

            self.access_patterns[pattern_key]["access_count"] += 1
            self.access_patterns[pattern_key]["last_access"] = current_time

            # 清理过期的访问模式（超过7天未访问）
            cutoff_time = current_time - timedelta(days=7)
            expired_keys = [
                key for key, pattern in self.access_patterns.items()
                if pattern["last_access"] < cutoff_time
            ]
            for key in expired_keys:
                del self.access_patterns[key]

        except Exception as e:
            logger.error(f"记录访问模式失败: {str(e)}")

    def get_popular_patterns(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取热门访问模式"""
        try:
            # 按访问次数排序
            sorted_patterns = sorted(
                self.access_patterns.values(),
                key=lambda x: x["access_count"],
                reverse=True
            )
            return sorted_patterns[:limit]
        except Exception as e:
            logger.error(f"获取热门访问模式失败: {str(e)}")
            return []

    def intelligent_preload(self, db: Session) -> Dict[str, Any]:
        """智能预加载：基于访问模式预加载数据"""
        results = {"success": 0, "failed": 0, "details": []}

        try:
            popular_patterns = self.get_popular_patterns(20)

            for pattern in popular_patterns:
                try:
                    endpoint = pattern["endpoint"]
                    params = pattern["params"]
                    access_count = pattern["access_count"]

                    # 只预加载访问次数超过阈值的模式
                    if access_count < 3:
                        continue

                    cache_key = f"intelligent_{endpoint}_{hash(str(sorted(params.items())))}"

                    # 根据端点类型执行相应的预加载逻辑
                    if "business-indicators" in endpoint:
                        # 预加载业务指标
                        optimizer = QueryOptimizer(db)

                        # 从参数中提取信息
                        store_ids = params.get("store_ids")
                        start_date = params.get("start_date")
                        end_date = params.get("end_date")

                        if start_date and end_date:
                            start_dt = datetime.fromisoformat(start_date)
                            end_dt = datetime.fromisoformat(end_date)

                            # 获取数据
                            sales_data = optimizer.get_aggregated_sales_data(
                                start_date=start_dt,
                                end_date=end_dt,
                                store_ids=store_ids,
                                group_by="day"
                            )

                            customer_data = optimizer.get_customer_analytics(
                                start_date=start_dt,
                                end_date=end_dt,
                                store_ids=store_ids
                            )

                            # 缓存数据
                            combined_data = {
                                "sales_data": sales_data,
                                "customer_data": customer_data,
                                "params": params,
                                "access_count": access_count,
                                "generated_at": datetime.now().isoformat()
                            }

                            self.cache.set_bi_data(
                                cache_key,
                                params,
                                combined_data,
                                3600  # 1小时缓存
                            )

                            results["success"] += 1
                            results["details"].append({
                                "type": "intelligent_business_indicators",
                                "key": cache_key,
                                "access_count": access_count,
                                "params": params
                            })

                    elif "multidimensional-analysis" in endpoint:
                        # 预加载多维度分析
                        # 这里可以添加多维度分析的智能预加载逻辑
                        pass

                    elif "trend-prediction" in endpoint:
                        # 预加载趋势预测
                        # 这里可以添加趋势预测的智能预加载逻辑
                        pass

                except Exception as e:
                    logger.error(f"智能预加载模式失败 {pattern.get('endpoint', 'unknown')}: {str(e)}")
                    results["failed"] += 1

        except Exception as e:
            logger.error(f"智能预加载总体失败: {str(e)}")
            results["failed"] += 1

        return results

    def preload_all_data(self) -> Dict[str, Any]:
        """预加载所有数据"""
        logger.info("开始预加载BI数据...")
        start_time = time.time()
        
        db = SessionLocal()
        try:
            total_results = {
                "success": 0,
                "failed": 0,
                "details": [],
                "start_time": datetime.now().isoformat(),
                "duration": 0
            }
            
            # 预加载业务指标
            bi_results = self.preload_business_indicators(db)
            total_results["success"] += bi_results["success"]
            total_results["failed"] += bi_results["failed"]
            total_results["details"].extend(bi_results["details"])
            
            # 预加载销售趋势
            trends_results = self.preload_sales_trends(db)
            total_results["success"] += trends_results["success"]
            total_results["failed"] += trends_results["failed"]
            total_results["details"].extend(trends_results["details"])
            
            # 预加载热销项目
            items_results = self.preload_top_items(db)
            total_results["success"] += items_results["success"]
            total_results["failed"] += items_results["failed"]
            total_results["details"].extend(items_results["details"])

            # 智能预加载（基于访问模式）
            intelligent_results = self.intelligent_preload(db)
            total_results["success"] += intelligent_results["success"]
            total_results["failed"] += intelligent_results["failed"]
            total_results["details"].extend(intelligent_results["details"])

            total_results["duration"] = time.time() - start_time
            total_results["end_time"] = datetime.now().isoformat()
            
            logger.info(f"预加载完成: 成功 {total_results['success']}, 失败 {total_results['failed']}, 耗时 {total_results['duration']:.2f}秒")
            
            return total_results
            
        finally:
            db.close()
    
    def start_scheduled_preloading(self):
        """启动定时预加载"""
        if self.is_running:
            logger.warning("预加载调度器已在运行")
            return
        
        self.is_running = True
        
        # 设置定时任务
        schedule.every(30).minutes.do(self.preload_all_data)  # 每30分钟预加载一次
        schedule.every().day.at("02:00").do(self.preload_all_data)  # 每天凌晨2点预加载
        
        logger.info("预加载调度器已启动")
        
        # 立即执行一次预加载
        self.preload_all_data()
        
        # 运行调度器
        while self.is_running:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次
    
    def stop_scheduled_preloading(self):
        """停止定时预加载"""
        self.is_running = False
        schedule.clear()
        logger.info("预加载调度器已停止")


# 全局预加载器实例
preloader = DataPreloader()


def start_data_preloading():
    """启动数据预加载（在应用启动时调用）"""
    import threading
    
    def run_preloader():
        try:
            preloader.start_scheduled_preloading()
        except Exception as e:
            logger.error(f"预加载器运行失败: {str(e)}")
    
    # 在后台线程中运行预加载器
    thread = threading.Thread(target=run_preloader, daemon=True)
    thread.start()
    logger.info("数据预加载线程已启动")


def get_preloader_status() -> Dict[str, Any]:
    """获取预加载器状态"""
    return {
        "is_running": preloader.is_running,
        "config": preloader.preload_config,
        "cache_stats": preloader.cache.get_cache_stats()
    }
