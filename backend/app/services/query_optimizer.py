"""
查询优化器
优化BI相关的数据库查询性能
"""

from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session, joinedload, selectinload
from sqlalchemy import text, func, and_, or_, case, distinct
from datetime import datetime, timedelta
from app.models.operation import Order, OrderItem
from app.models.customer import Customer
from app.models.store import Store
from app.models.employee import Employee
import logging

logger = logging.getLogger(__name__)


class QueryOptimizer:
    """查询优化器"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_optimized_orders_query(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        store_ids: Optional[List[int]] = None,
        employee_ids: Optional[List[int]] = None,
        customer_ids: Optional[List[int]] = None,
        status: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        include_items: bool = False,
        include_customer: bool = False,
        include_employee: bool = False
    ):
        """
        获取优化的订单查询
        使用预加载和索引优化查询性能
        """
        # 基础查询
        query = self.db.query(Order).filter(Order.is_deleted == False)
        
        # 预加载相关数据以减少N+1查询
        if include_items:
            query = query.options(selectinload(Order.order_items))
        
        if include_customer:
            query = query.options(joinedload(Order.customer))
        
        if include_employee:
            query = query.options(joinedload(Order.employee))
        
        # 应用过滤条件（利用索引）
        if start_date and end_date:
            query = query.filter(Order.order_time.between(start_date, end_date))
        elif start_date:
            query = query.filter(Order.order_time >= start_date)
        elif end_date:
            query = query.filter(Order.order_time <= end_date)
        
        if store_ids:
            query = query.filter(Order.store_id.in_(store_ids))
        
        if employee_ids:
            query = query.filter(Order.employee_id.in_(employee_ids))
        
        if customer_ids:
            query = query.filter(Order.customer_id.in_(customer_ids))
        
        if status:
            query = query.filter(Order.status == status)
        
        # 排序（利用索引）
        query = query.order_by(Order.order_time.desc())
        
        # 分页
        if offset:
            query = query.offset(offset)
        if limit:
            query = query.limit(limit)
        
        return query
    
    def get_aggregated_sales_data(
        self,
        start_date: datetime,
        end_date: datetime,
        store_ids: Optional[List[int]] = None,
        group_by: str = "day"  # day, week, month
    ) -> List[Dict[str, Any]]:
        """
        获取聚合销售数据
        使用数据库聚合函数提高性能
        """
        # 根据分组类型选择时间截断函数
        if group_by == "day":
            time_trunc = func.date_trunc('day', Order.order_time)
            time_format = "YYYY-MM-DD"
        elif group_by == "week":
            time_trunc = func.date_trunc('week', Order.order_time)
            time_format = "YYYY-\"W\"WW"
        elif group_by == "month":
            time_trunc = func.date_trunc('month', Order.order_time)
            time_format = "YYYY-MM"
        else:
            time_trunc = func.date_trunc('day', Order.order_time)
            time_format = "YYYY-MM-DD"
        
        # 构建聚合查询
        query = self.db.query(
            time_trunc.label('time_period'),
            func.to_char(time_trunc, time_format).label('time_key'),
            func.count(Order.id).label('order_count'),
            func.sum(Order.total_amount).label('total_sales'),
            func.avg(Order.total_amount).label('avg_order_value'),
            func.count(distinct(Order.customer_id)).label('unique_customers')
        ).filter(
            Order.is_deleted == False,
            Order.order_time.between(start_date, end_date)
        )
        
        if store_ids:
            query = query.filter(Order.store_id.in_(store_ids))
        
        query = query.group_by(time_trunc).order_by(time_trunc)
        
        results = []
        for row in query.all():
            results.append({
                'time_period': row.time_period,
                'time_key': row.time_key,
                'order_count': row.order_count,
                'total_sales': float(row.total_sales or 0),
                'avg_order_value': float(row.avg_order_value or 0),
                'unique_customers': row.unique_customers
            })
        
        return results
    
    def get_top_performing_items(
        self,
        start_date: datetime,
        end_date: datetime,
        store_ids: Optional[List[int]] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        获取热销项目排行
        使用JOIN和聚合优化查询
        """
        query = self.db.query(
            OrderItem.store_item_id,
            func.sum(OrderItem.quantity).label('total_quantity'),
            func.sum(OrderItem.subtotal).label('total_revenue'),
            func.count(distinct(OrderItem.order_id)).label('order_count'),
            func.avg(OrderItem.unit_price).label('avg_price')
        ).join(
            Order, OrderItem.order_id == Order.id
        ).filter(
            Order.is_deleted == False,
            Order.order_time.between(start_date, end_date)
        )
        
        if store_ids:
            query = query.filter(Order.store_id.in_(store_ids))
        
        query = query.group_by(OrderItem.store_item_id).order_by(
            func.sum(OrderItem.subtotal).desc()
        ).limit(limit)
        
        results = []
        for row in query.all():
            results.append({
                'store_item_id': row.store_item_id,
                'total_quantity': row.total_quantity,
                'total_revenue': float(row.total_revenue or 0),
                'order_count': row.order_count,
                'avg_price': float(row.avg_price or 0)
            })
        
        return results
    
    def get_customer_analytics(
        self,
        start_date: datetime,
        end_date: datetime,
        store_ids: Optional[List[int]] = None
    ) -> Dict[str, Any]:
        """
        获取客户分析数据
        使用子查询和窗口函数优化
        """
        # 客户订单统计子查询
        customer_stats_subquery = self.db.query(
            Order.customer_id,
            func.count(Order.id).label('order_count'),
            func.sum(Order.total_amount).label('total_spent'),
            func.min(Order.order_time).label('first_order'),
            func.max(Order.order_time).label('last_order')
        ).filter(
            Order.is_deleted == False,
            Order.customer_id.isnot(None),
            Order.order_time.between(start_date, end_date)
        )
        
        if store_ids:
            customer_stats_subquery = customer_stats_subquery.filter(
                Order.store_id.in_(store_ids)
            )
        
        customer_stats_subquery = customer_stats_subquery.group_by(
            Order.customer_id
        ).subquery()
        
        # 主查询
        query = self.db.query(
            func.count(customer_stats_subquery.c.customer_id).label('total_customers'),
            func.avg(customer_stats_subquery.c.order_count).label('avg_orders_per_customer'),
            func.avg(customer_stats_subquery.c.total_spent).label('avg_spent_per_customer'),
            func.sum(
                case(
                    (customer_stats_subquery.c.order_count == 1, 1),
                    else_=0
                )
            ).label('one_time_customers'),
            func.sum(
                case(
                    (customer_stats_subquery.c.order_count > 1, 1),
                    else_=0
                )
            ).label('repeat_customers')
        )
        
        result = query.first()
        
        total_customers = result.total_customers or 0
        repeat_customers = result.repeat_customers or 0
        
        return {
            'total_customers': total_customers,
            'avg_orders_per_customer': float(result.avg_orders_per_customer or 0),
            'avg_spent_per_customer': float(result.avg_spent_per_customer or 0),
            'one_time_customers': result.one_time_customers or 0,
            'repeat_customers': repeat_customers,
            'repeat_rate': (repeat_customers / total_customers * 100) if total_customers > 0 else 0
        }
    
    def get_employee_performance(
        self,
        start_date: datetime,
        end_date: datetime,
        store_ids: Optional[List[int]] = None,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """
        获取员工业绩数据
        使用JOIN和聚合优化
        """
        query = self.db.query(
            Order.employee_id,
            Employee.name.label('employee_name'),
            Employee.role.label('employee_role'),
            Store.name.label('store_name'),
            func.count(Order.id).label('order_count'),
            func.sum(Order.total_amount).label('total_sales'),
            func.avg(Order.total_amount).label('avg_order_value'),
            func.count(distinct(Order.customer_id)).label('unique_customers')
        ).join(
            Employee, Order.employee_id == Employee.id
        ).join(
            Store, Order.store_id == Store.id
        ).filter(
            Order.is_deleted == False,
            Employee.is_deleted == False,
            Order.order_time.between(start_date, end_date)
        )
        
        if store_ids:
            query = query.filter(Order.store_id.in_(store_ids))
        
        query = query.group_by(
            Order.employee_id,
            Employee.name,
            Employee.role,
            Store.name
        ).order_by(
            func.sum(Order.total_amount).desc()
        ).limit(limit)
        
        results = []
        for row in query.all():
            results.append({
                'employee_id': row.employee_id,
                'employee_name': row.employee_name,
                'employee_role': row.employee_role,
                'store_name': row.store_name,
                'order_count': row.order_count,
                'total_sales': float(row.total_sales or 0),
                'avg_order_value': float(row.avg_order_value or 0),
                'unique_customers': row.unique_customers
            })
        
        return results
    
    def execute_raw_optimized_query(self, sql: str, params: dict = None) -> List[Dict[str, Any]]:
        """
        执行原生优化SQL查询
        用于复杂的分析查询
        """
        try:
            if params:
                result = self.db.execute(text(sql), params)
            else:
                result = self.db.execute(text(sql))
            
            # 转换结果为字典列表
            columns = result.keys()
            rows = []
            for row in result:
                row_dict = {}
                for i, column in enumerate(columns):
                    value = row[i]
                    # 处理特殊类型
                    if hasattr(value, 'isoformat'):  # datetime
                        value = value.isoformat()
                    elif hasattr(value, '__float__'):  # Decimal
                        value = float(value)
                    row_dict[column] = value
                rows.append(row_dict)
            
            return rows
            
        except Exception as e:
            logger.error(f"执行原生查询失败: {str(e)}")
            raise
    
    def get_query_execution_plan(self, query) -> str:
        """
        获取查询执行计划
        用于性能分析
        """
        try:
            # 获取查询的SQL语句
            sql_query = str(query.statement.compile(compile_kwargs={"literal_binds": True}))
            
            # 执行EXPLAIN ANALYZE
            explain_query = f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {sql_query}"
            result = self.db.execute(text(explain_query))
            
            return result.scalar()
            
        except Exception as e:
            logger.error(f"获取查询执行计划失败: {str(e)}")
            return f"Error: {str(e)}"
