from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, func, desc, text
import logging
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np
import pandas as pd
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

from app.models.customer_profile import CustomerProfile
# from app.models.order import Order  # 暂时注释掉，等Order模型可用时再启用
from app.models.employee import Employee
from app.models.store import Store

logger = logging.getLogger(__name__)


class AnalysisType(Enum):
    """分析类型枚举"""
    CUSTOMER_SEGMENTATION = "customer_segmentation"    # 客户细分
    SALES_FORECASTING = "sales_forecasting"           # 销售预测
    CHURN_PREDICTION = "churn_prediction"             # 流失预测
    PRICE_OPTIMIZATION = "price_optimization"         # 价格优化
    STAFF_PERFORMANCE = "staff_performance"           # 员工绩效
    MARKET_BASKET = "market_basket"                   # 购物篮分析
    SEASONAL_ANALYSIS = "seasonal_analysis"           # 季节性分析
    COHORT_ANALYSIS = "cohort_analysis"               # 队列分析


class MetricType(Enum):
    """指标类型枚举"""
    REVENUE = "revenue"                # 收入
    CUSTOMER_COUNT = "customer_count"  # 客户数量
    ORDER_COUNT = "order_count"        # 订单数量
    AVERAGE_ORDER_VALUE = "aov"        # 客单价
    RETENTION_RATE = "retention_rate"  # 留存率
    CONVERSION_RATE = "conversion_rate" # 转化率


@dataclass
class AnalysisResult:
    """分析结果"""
    analysis_type: AnalysisType
    result_data: Dict[str, Any]
    insights: List[str]
    recommendations: List[str]
    confidence_score: float
    generated_at: datetime
    parameters: Dict[str, Any]


class AdvancedAnalyticsService:
    """高级数据分析服务"""

    def __init__(self, db: Session):
        self.db = db
        from app.services.query_optimizer import QueryOptimizer
        self.optimizer = QueryOptimizer(db)

    def year_over_year_analysis(
        self,
        current_start: datetime,
        current_end: datetime,
        store_ids: Optional[List[int]] = None,
        metrics: List[str] = None
    ) -> Dict[str, Any]:
        """
        同比分析

        Args:
            current_start: 当前期间开始时间
            current_end: 当前期间结束时间
            store_ids: 门店ID列表
            metrics: 分析指标列表

        Returns:
            同比分析结果
        """
        try:
            # 计算去年同期时间范围
            period_days = (current_end - current_start).days
            last_year_start = current_start.replace(year=current_start.year - 1)
            last_year_end = last_year_start + timedelta(days=period_days)

            # 获取当前期间数据
            current_data = self._get_period_metrics(
                current_start, current_end, store_ids, metrics
            )

            # 获取去年同期数据
            last_year_data = self._get_period_metrics(
                last_year_start, last_year_end, store_ids, metrics
            )

            # 计算同比变化
            yoy_analysis = {}
            for metric in current_data:
                current_value = current_data[metric]
                last_year_value = last_year_data.get(metric, 0)

                if last_year_value > 0:
                    growth_rate = ((current_value - last_year_value) / last_year_value) * 100
                else:
                    growth_rate = 100 if current_value > 0 else 0

                yoy_analysis[metric] = {
                    'current_period': current_value,
                    'last_year_period': last_year_value,
                    'absolute_change': current_value - last_year_value,
                    'growth_rate': round(growth_rate, 2),
                    'trend': 'up' if growth_rate > 0 else 'down' if growth_rate < 0 else 'stable'
                }

            return {
                'analysis_type': 'year_over_year',
                'current_period': {
                    'start': current_start.isoformat(),
                    'end': current_end.isoformat()
                },
                'comparison_period': {
                    'start': last_year_start.isoformat(),
                    'end': last_year_end.isoformat()
                },
                'metrics': yoy_analysis,
                'summary': self._generate_yoy_summary(yoy_analysis)
            }

        except Exception as e:
            logger.error(f"同比分析失败: {str(e)}")
            raise

    def month_over_month_analysis(
        self,
        current_start: datetime,
        current_end: datetime,
        store_ids: Optional[List[int]] = None,
        metrics: List[str] = None
    ) -> Dict[str, Any]:
        """
        环比分析

        Args:
            current_start: 当前期间开始时间
            current_end: 当前期间结束时间
            store_ids: 门店ID列表
            metrics: 分析指标列表

        Returns:
            环比分析结果
        """
        try:
            # 计算上期时间范围
            period_days = (current_end - current_start).days
            last_period_end = current_start - timedelta(days=1)
            last_period_start = last_period_end - timedelta(days=period_days)

            # 获取当前期间数据
            current_data = self._get_period_metrics(
                current_start, current_end, store_ids, metrics
            )

            # 获取上期数据
            last_period_data = self._get_period_metrics(
                last_period_start, last_period_end, store_ids, metrics
            )

            # 计算环比变化
            mom_analysis = {}
            for metric in current_data:
                current_value = current_data[metric]
                last_period_value = last_period_data.get(metric, 0)

                if last_period_value > 0:
                    growth_rate = ((current_value - last_period_value) / last_period_value) * 100
                else:
                    growth_rate = 100 if current_value > 0 else 0

                mom_analysis[metric] = {
                    'current_period': current_value,
                    'last_period': last_period_value,
                    'absolute_change': current_value - last_period_value,
                    'growth_rate': round(growth_rate, 2),
                    'trend': 'up' if growth_rate > 0 else 'down' if growth_rate < 0 else 'stable'
                }

            return {
                'analysis_type': 'month_over_month',
                'current_period': {
                    'start': current_start.isoformat(),
                    'end': current_end.isoformat()
                },
                'comparison_period': {
                    'start': last_period_start.isoformat(),
                    'end': last_period_end.isoformat()
                },
                'metrics': mom_analysis,
                'summary': self._generate_mom_summary(mom_analysis)
            }

        except Exception as e:
            logger.error(f"环比分析失败: {str(e)}")
            raise

    def trend_analysis(
        self,
        start_date: datetime,
        end_date: datetime,
        store_ids: Optional[List[int]] = None,
        metric: str = 'total_revenue',
        period: str = 'day'
    ) -> Dict[str, Any]:
        """
        趋势分析

        Args:
            start_date: 开始日期
            end_date: 结束日期
            store_ids: 门店ID列表
            metric: 分析指标
            period: 时间粒度 (day, week, month)

        Returns:
            趋势分析结果
        """
        try:
            # 获取时间序列数据
            time_series_data = self.optimizer.get_aggregated_sales_data(
                start_date=start_date,
                end_date=end_date,
                store_ids=store_ids,
                group_by=period
            )

            if not time_series_data:
                return {'error': '没有足够的数据进行趋势分析'}

            # 提取指标值
            values = []
            dates = []
            for item in time_series_data:
                if metric == 'total_revenue':
                    values.append(item['total_sales'])
                elif metric == 'order_count':
                    values.append(item['order_count'])
                elif metric == 'avg_order_value':
                    values.append(item['avg_order_value'])
                else:
                    values.append(item.get(metric, 0))
                dates.append(item['time_key'])

            # 计算趋势指标
            trend_analysis = self._calculate_trend_metrics(values, dates)

            # 预测未来趋势
            forecast = self._simple_forecast(values, periods=7)

            return {
                'analysis_type': 'trend_analysis',
                'metric': metric,
                'period': period,
                'date_range': {
                    'start': start_date.isoformat(),
                    'end': end_date.isoformat()
                },
                'data_points': len(values),
                'trend_metrics': trend_analysis,
                'time_series': [
                    {'date': dates[i], 'value': values[i]}
                    for i in range(len(values))
                ],
                'forecast': forecast,
                'insights': self._generate_trend_insights(trend_analysis, values)
            }

        except Exception as e:
            logger.error(f"趋势分析失败: {str(e)}")
            raise

    def anomaly_detection(
        self,
        start_date: datetime,
        end_date: datetime,
        store_ids: Optional[List[int]] = None,
        metric: str = 'total_revenue',
        sensitivity: float = 2.0
    ) -> Dict[str, Any]:
        """
        异常检测

        Args:
            start_date: 开始日期
            end_date: 结束日期
            store_ids: 门店ID列表
            metric: 检测指标
            sensitivity: 敏感度（标准差倍数）

        Returns:
            异常检测结果
        """
        try:
            # 获取时间序列数据
            time_series_data = self.optimizer.get_aggregated_sales_data(
                start_date=start_date,
                end_date=end_date,
                store_ids=store_ids,
                group_by='day'
            )

            if len(time_series_data) < 7:
                return {'error': '数据点不足，无法进行异常检测（至少需要7天数据）'}

            # 提取指标值
            values = []
            dates = []
            for item in time_series_data:
                if metric == 'total_revenue':
                    values.append(item['total_sales'])
                elif metric == 'order_count':
                    values.append(item['order_count'])
                else:
                    values.append(item.get(metric, 0))
                dates.append(item['time_key'])

            # 计算统计指标
            import numpy as np
            mean_value = np.mean(values)
            std_value = np.std(values)

            # 检测异常点
            anomalies = []
            for i, value in enumerate(values):
                z_score = abs(value - mean_value) / std_value if std_value > 0 else 0

                if z_score > sensitivity:
                    anomaly_type = 'high' if value > mean_value else 'low'
                    anomalies.append({
                        'date': dates[i],
                        'value': value,
                        'z_score': round(z_score, 2),
                        'type': anomaly_type,
                        'deviation': round(value - mean_value, 2),
                        'severity': 'high' if z_score > 3 else 'medium'
                    })

            return {
                'analysis_type': 'anomaly_detection',
                'metric': metric,
                'sensitivity': sensitivity,
                'date_range': {
                    'start': start_date.isoformat(),
                    'end': end_date.isoformat()
                },
                'statistics': {
                    'mean': round(mean_value, 2),
                    'std': round(std_value, 2),
                    'min': min(values),
                    'max': max(values),
                    'data_points': len(values)
                },
                'anomalies': anomalies,
                'anomaly_count': len(anomalies),
                'anomaly_rate': round(len(anomalies) / len(values) * 100, 2),
                'insights': self._generate_anomaly_insights(anomalies, mean_value)
            }

        except Exception as e:
            logger.error(f"异常检测失败: {str(e)}")
            raise

    def correlation_analysis(
        self,
        start_date: datetime,
        end_date: datetime,
        store_ids: Optional[List[int]] = None
    ) -> Dict[str, Any]:
        """
        相关性分析

        Args:
            start_date: 开始日期
            end_date: 结束日期
            store_ids: 门店ID列表

        Returns:
            相关性分析结果
        """
        try:
            # 获取多维度数据
            time_series_data = self.optimizer.get_aggregated_sales_data(
                start_date=start_date,
                end_date=end_date,
                store_ids=store_ids,
                group_by='day'
            )

            if len(time_series_data) < 10:
                return {'error': '数据点不足，无法进行相关性分析（至少需要10天数据）'}

            # 构建数据矩阵
            metrics_data = {
                'total_sales': [],
                'order_count': [],
                'avg_order_value': [],
                'unique_customers': []
            }

            for item in time_series_data:
                metrics_data['total_sales'].append(item['total_sales'])
                metrics_data['order_count'].append(item['order_count'])
                metrics_data['avg_order_value'].append(item['avg_order_value'])
                metrics_data['unique_customers'].append(item['unique_customers'])

            # 计算相关系数矩阵
            import pandas as pd
            df = pd.DataFrame(metrics_data)
            correlation_matrix = df.corr()

            # 提取显著相关性
            correlations = []
            metrics = list(metrics_data.keys())

            for i in range(len(metrics)):
                for j in range(i + 1, len(metrics)):
                    metric1 = metrics[i]
                    metric2 = metrics[j]
                    corr_value = correlation_matrix.loc[metric1, metric2]

                    if abs(corr_value) > 0.3:  # 只显示中等以上相关性
                        correlations.append({
                            'metric1': metric1,
                            'metric2': metric2,
                            'correlation': round(corr_value, 3),
                            'strength': self._get_correlation_strength(abs(corr_value)),
                            'direction': 'positive' if corr_value > 0 else 'negative'
                        })

            return {
                'analysis_type': 'correlation_analysis',
                'date_range': {
                    'start': start_date.isoformat(),
                    'end': end_date.isoformat()
                },
                'data_points': len(time_series_data),
                'correlation_matrix': correlation_matrix.to_dict(),
                'significant_correlations': correlations,
                'insights': self._generate_correlation_insights(correlations)
            }

        except Exception as e:
            logger.error(f"相关性分析失败: {str(e)}")
            raise

    def _get_period_metrics(
        self,
        start_date: datetime,
        end_date: datetime,
        store_ids: Optional[List[int]],
        metrics: Optional[List[str]]
    ) -> Dict[str, float]:
        """获取期间指标数据"""
        try:
            from sqlalchemy import func
            from app.models.operation import Order

            # 基础查询
            query = self.db.query(
                func.count(Order.id).label('order_count'),
                func.sum(Order.total_amount).label('total_revenue'),
                func.avg(Order.total_amount).label('avg_order_value'),
                func.count(func.distinct(Order.customer_id)).label('unique_customers')
            ).filter(
                Order.order_time.between(start_date, end_date),
                Order.is_deleted == False
            )

            if store_ids:
                query = query.filter(Order.store_id.in_(store_ids))

            result = query.first()

            return {
                'order_count': result.order_count or 0,
                'total_revenue': float(result.total_revenue or 0),
                'avg_order_value': float(result.avg_order_value or 0),
                'unique_customers': result.unique_customers or 0
            }

        except Exception as e:
            logger.error(f"获取期间指标失败: {str(e)}")
            return {}

    def _calculate_trend_metrics(self, values: List[float], dates: List[str]) -> Dict[str, Any]:
        """计算趋势指标"""
        try:
            import numpy as np

            if len(values) < 2:
                return {}

            # 线性回归计算趋势
            x = np.arange(len(values))
            y = np.array(values)

            # 计算斜率和截距
            slope, intercept = np.polyfit(x, y, 1)

            # 计算R²
            y_pred = slope * x + intercept
            ss_res = np.sum((y - y_pred) ** 2)
            ss_tot = np.sum((y - np.mean(y)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0

            # 计算变化率
            if len(values) >= 2:
                total_change = values[-1] - values[0]
                change_rate = (total_change / values[0] * 100) if values[0] > 0 else 0
            else:
                total_change = 0
                change_rate = 0

            return {
                'slope': round(slope, 4),
                'trend_direction': 'increasing' if slope > 0 else 'decreasing' if slope < 0 else 'stable',
                'r_squared': round(r_squared, 4),
                'trend_strength': 'strong' if r_squared > 0.7 else 'moderate' if r_squared > 0.3 else 'weak',
                'total_change': round(total_change, 2),
                'change_rate': round(change_rate, 2),
                'volatility': round(np.std(values), 2)
            }

        except Exception as e:
            logger.error(f"计算趋势指标失败: {str(e)}")
            return {}

    def _simple_forecast(self, values: List[float], periods: int = 7) -> List[Dict[str, Any]]:
        """简单预测"""
        try:
            import numpy as np

            if len(values) < 3:
                return []

            # 使用移动平均进行简单预测
            window_size = min(7, len(values))
            recent_avg = np.mean(values[-window_size:])

            # 计算趋势
            if len(values) >= 2:
                trend = (values[-1] - values[-2])
            else:
                trend = 0

            forecast = []
            for i in range(periods):
                predicted_value = recent_avg + (trend * (i + 1))
                forecast.append({
                    'period': i + 1,
                    'predicted_value': round(max(0, predicted_value), 2),
                    'confidence': 'low'  # 简单预测的置信度较低
                })

            return forecast

        except Exception as e:
            logger.error(f"预测失败: {str(e)}")
            return []

    def _generate_yoy_summary(self, yoy_analysis: Dict) -> Dict[str, Any]:
        """生成同比分析摘要"""
        try:
            positive_trends = sum(1 for metric in yoy_analysis.values() if metric['growth_rate'] > 0)
            negative_trends = sum(1 for metric in yoy_analysis.values() if metric['growth_rate'] < 0)

            best_metric = max(yoy_analysis.items(), key=lambda x: x[1]['growth_rate'])
            worst_metric = min(yoy_analysis.items(), key=lambda x: x[1]['growth_rate'])

            return {
                'overall_trend': 'positive' if positive_trends > negative_trends else 'negative',
                'positive_metrics': positive_trends,
                'negative_metrics': negative_trends,
                'best_performing': {
                    'metric': best_metric[0],
                    'growth_rate': best_metric[1]['growth_rate']
                },
                'worst_performing': {
                    'metric': worst_metric[0],
                    'growth_rate': worst_metric[1]['growth_rate']
                }
            }
        except:
            return {}

    def _generate_mom_summary(self, mom_analysis: Dict) -> Dict[str, Any]:
        """生成环比分析摘要"""
        return self._generate_yoy_summary(mom_analysis)  # 逻辑相同

    def _generate_trend_insights(self, trend_metrics: Dict, values: List[float]) -> List[str]:
        """生成趋势洞察"""
        insights = []

        try:
            import numpy as np

            if trend_metrics.get('trend_direction') == 'increasing':
                insights.append(f"数据呈上升趋势，增长率为{trend_metrics.get('change_rate', 0):.1f}%")
            elif trend_metrics.get('trend_direction') == 'decreasing':
                insights.append(f"数据呈下降趋势，下降率为{abs(trend_metrics.get('change_rate', 0)):.1f}%")
            else:
                insights.append("数据趋势相对稳定")

            if trend_metrics.get('trend_strength') == 'strong':
                insights.append("趋势非常明显且稳定")
            elif trend_metrics.get('trend_strength') == 'weak':
                insights.append("趋势不够明显，数据波动较大")

            volatility = trend_metrics.get('volatility', 0)
            if volatility > np.mean(values) * 0.3:
                insights.append("数据波动性较高，建议关注异常值")

        except:
            pass

        return insights

    def _generate_anomaly_insights(self, anomalies: List[Dict], mean_value: float) -> List[str]:
        """生成异常检测洞察"""
        insights = []

        try:
            if not anomalies:
                insights.append("未检测到显著异常，数据表现正常")
            else:
                high_anomalies = [a for a in anomalies if a['type'] == 'high']
                low_anomalies = [a for a in anomalies if a['type'] == 'low']

                if high_anomalies:
                    insights.append(f"检测到{len(high_anomalies)}个高值异常点")
                if low_anomalies:
                    insights.append(f"检测到{len(low_anomalies)}个低值异常点")

                severe_anomalies = [a for a in anomalies if a['severity'] == 'high']
                if severe_anomalies:
                    insights.append(f"有{len(severe_anomalies)}个严重异常需要重点关注")

        except:
            pass

        return insights

    def _generate_correlation_insights(self, correlations: List[Dict]) -> List[str]:
        """生成相关性分析洞察"""
        insights = []

        try:
            if not correlations:
                insights.append("各指标间相关性较弱，相对独立")
            else:
                strong_correlations = [c for c in correlations if c['strength'] == 'strong']
                if strong_correlations:
                    insights.append(f"发现{len(strong_correlations)}对强相关指标")

                positive_correlations = [c for c in correlations if c['direction'] == 'positive']
                if positive_correlations:
                    insights.append(f"{len(positive_correlations)}对指标呈正相关关系")

        except:
            pass

        return insights

    def _get_correlation_strength(self, abs_corr: float) -> str:
        """获取相关性强度描述"""
        if abs_corr >= 0.7:
            return 'strong'
        elif abs_corr >= 0.5:
            return 'moderate'
        else:
            return 'weak'
    
    def customer_segmentation_analysis(self, store_id: Optional[int] = None) -> Dict[str, Any]:
        """
        客户细分分析（RFM模型）
        
        Args:
            store_id: 门店ID
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        try:
            # 获取客户数据
            customer_data = self._get_customer_rfm_data(store_id)
            
            if len(customer_data) < 10:
                return {
                    'success': False,
                    'message': '数据量不足，无法进行客户细分分析',
                    'data': None
                }
            
            # 准备数据
            df = pd.DataFrame(customer_data)
            features = ['recency', 'frequency', 'monetary']
            X = df[features].values
            
            # 标准化
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            
            # K-means聚类
            optimal_k = self._find_optimal_clusters(X_scaled, max_k=6)
            kmeans = KMeans(n_clusters=optimal_k, random_state=42)
            df['segment'] = kmeans.fit_predict(X_scaled)
            
            # 分析各细分群体
            segments = self._analyze_customer_segments(df)
            
            # 生成洞察和建议
            insights = self._generate_segmentation_insights(segments)
            recommendations = self._generate_segmentation_recommendations(segments)
            
            result = AnalysisResult(
                analysis_type=AnalysisType.CUSTOMER_SEGMENTATION,
                result_data={
                    'segments': segments,
                    'total_customers': len(df),
                    'segment_distribution': df['segment'].value_counts().to_dict(),
                    'feature_importance': {
                        'recency': 0.35,
                        'frequency': 0.40,
                        'monetary': 0.25
                    }
                },
                insights=insights,
                recommendations=recommendations,
                confidence_score=0.85,
                generated_at=datetime.now(),
                parameters={'store_id': store_id, 'clusters': optimal_k}
            )
            
            return {
                'success': True,
                'data': asdict(result)
            }
            
        except Exception as e:
            logger.error(f"客户细分分析失败: {str(e)}")
            return {
                'success': False,
                'message': f'客户细分分析失败: {str(e)}',
                'data': None
            }
    
    def sales_forecasting_analysis(self, store_id: Optional[int] = None, 
                                 forecast_days: int = 30) -> Dict[str, Any]:
        """
        销售预测分析
        
        Args:
            store_id: 门店ID
            forecast_days: 预测天数
            
        Returns:
            Dict[str, Any]: 预测结果
        """
        try:
            # 获取历史销售数据
            sales_data = self._get_historical_sales_data(store_id, days=365)
            
            if len(sales_data) < 30:
                return {
                    'success': False,
                    'message': '历史数据不足，无法进行销售预测',
                    'data': None
                }
            
            # 准备数据
            df = pd.DataFrame(sales_data)
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date')
            
            # 特征工程
            df['day_of_week'] = df['date'].dt.dayofweek
            df['month'] = df['date'].dt.month
            df['day_of_month'] = df['date'].dt.day
            df['is_weekend'] = df['day_of_week'].isin([5, 6]).astype(int)
            
            # 移动平均特征
            df['ma_7'] = df['revenue'].rolling(window=7).mean()
            df['ma_30'] = df['revenue'].rolling(window=30).mean()
            
            # 准备训练数据
            features = ['day_of_week', 'month', 'day_of_month', 'is_weekend', 'ma_7', 'ma_30']
            df_clean = df.dropna()
            
            if len(df_clean) < 20:
                return {
                    'success': False,
                    'message': '清洗后数据不足，无法训练模型',
                    'data': None
                }
            
            X = df_clean[features].values
            y = df_clean['revenue'].values
            
            # 训练模型
            model = LinearRegression()
            model.fit(X, y)
            
            # 模型评估
            y_pred = model.predict(X)
            r2 = r2_score(y, y_pred)
            rmse = np.sqrt(mean_squared_error(y, y_pred))
            
            # 生成预测
            forecast_data = self._generate_sales_forecast(model, df_clean, forecast_days)
            
            # 生成洞察
            insights = self._generate_forecasting_insights(df_clean, forecast_data, r2)
            recommendations = self._generate_forecasting_recommendations(forecast_data)
            
            result = AnalysisResult(
                analysis_type=AnalysisType.SALES_FORECASTING,
                result_data={
                    'forecast': forecast_data,
                    'model_performance': {
                        'r2_score': r2,
                        'rmse': rmse,
                        'accuracy': 'good' if r2 > 0.7 else 'moderate' if r2 > 0.5 else 'poor'
                    },
                    'historical_trend': {
                        'avg_daily_revenue': df_clean['revenue'].mean(),
                        'revenue_growth': self._calculate_growth_rate(df_clean['revenue']),
                        'seasonality': self._detect_seasonality(df_clean)
                    }
                },
                insights=insights,
                recommendations=recommendations,
                confidence_score=min(r2, 0.95),
                generated_at=datetime.now(),
                parameters={'store_id': store_id, 'forecast_days': forecast_days}
            )
            
            return {
                'success': True,
                'data': asdict(result)
            }
            
        except Exception as e:
            logger.error(f"销售预测分析失败: {str(e)}")
            return {
                'success': False,
                'message': f'销售预测分析失败: {str(e)}',
                'data': None
            }
    
    def cohort_analysis(self, store_id: Optional[int] = None) -> Dict[str, Any]:
        """
        队列分析
        
        Args:
            store_id: 门店ID
            
        Returns:
            Dict[str, Any]: 队列分析结果
        """
        try:
            # 获取客户订单数据
            order_data = self._get_customer_order_data(store_id)
            
            if len(order_data) < 50:
                return {
                    'success': False,
                    'message': '数据量不足，无法进行队列分析',
                    'data': None
                }
            
            # 准备数据
            df = pd.DataFrame(order_data)
            df['order_date'] = pd.to_datetime(df['order_date'])
            
            # 计算客户首次购买时间
            customer_first_purchase = df.groupby('customer_id')['order_date'].min().reset_index()
            customer_first_purchase.columns = ['customer_id', 'first_purchase_date']
            customer_first_purchase['cohort_month'] = customer_first_purchase['first_purchase_date'].dt.to_period('M')
            
            # 合并数据
            df = df.merge(customer_first_purchase, on='customer_id')
            df['order_period'] = df['order_date'].dt.to_period('M')
            df['period_number'] = (df['order_period'] - df['cohort_month']).apply(attrgetter('n'))
            
            # 计算队列表
            cohort_data = df.groupby(['cohort_month', 'period_number'])['customer_id'].nunique().reset_index()
            cohort_table = cohort_data.pivot(index='cohort_month', columns='period_number', values='customer_id')
            
            # 计算队列大小
            cohort_sizes = customer_first_purchase.groupby('cohort_month')['customer_id'].nunique()
            
            # 计算留存率
            retention_table = cohort_table.divide(cohort_sizes, axis=0)
            
            # 分析结果
            avg_retention = {
                'month_1': retention_table[1].mean() if 1 in retention_table.columns else 0,
                'month_3': retention_table[3].mean() if 3 in retention_table.columns else 0,
                'month_6': retention_table[6].mean() if 6 in retention_table.columns else 0,
                'month_12': retention_table[12].mean() if 12 in retention_table.columns else 0
            }
            
            # 生成洞察和建议
            insights = self._generate_cohort_insights(retention_table, avg_retention)
            recommendations = self._generate_cohort_recommendations(avg_retention)
            
            result = AnalysisResult(
                analysis_type=AnalysisType.COHORT_ANALYSIS,
                result_data={
                    'retention_table': retention_table.fillna(0).to_dict(),
                    'cohort_sizes': cohort_sizes.to_dict(),
                    'average_retention': avg_retention,
                    'total_cohorts': len(cohort_sizes),
                    'analysis_period': f"{cohort_sizes.index.min()} to {cohort_sizes.index.max()}"
                },
                insights=insights,
                recommendations=recommendations,
                confidence_score=0.80,
                generated_at=datetime.now(),
                parameters={'store_id': store_id}
            )
            
            return {
                'success': True,
                'data': asdict(result)
            }
            
        except Exception as e:
            logger.error(f"队列分析失败: {str(e)}")
            return {
                'success': False,
                'message': f'队列分析失败: {str(e)}',
                'data': None
            }
    
    def _get_customer_rfm_data(self, store_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """获取客户RFM数据"""
        # 这里应该从数据库查询真实数据
        # 现在返回模拟数据
        import random
        
        customers = []
        for i in range(100):
            customers.append({
                'customer_id': i + 1,
                'recency': random.randint(1, 365),      # 最近购买天数
                'frequency': random.randint(1, 20),     # 购买频次
                'monetary': random.uniform(100, 5000)   # 购买金额
            })
        
        return customers
    
    def _get_historical_sales_data(self, store_id: Optional[int] = None, days: int = 365) -> List[Dict[str, Any]]:
        """获取历史销售数据"""
        # 这里应该从数据库查询真实数据
        # 现在返回模拟数据
        import random
        
        sales_data = []
        base_date = datetime.now() - timedelta(days=days)
        
        for i in range(days):
            current_date = base_date + timedelta(days=i)
            
            # 模拟季节性和周期性
            day_of_week = current_date.weekday()
            month = current_date.month
            
            base_revenue = 2000
            
            # 周末加成
            if day_of_week in [5, 6]:
                base_revenue *= 1.3
            
            # 季节性调整
            if month in [6, 7, 8]:  # 夏季
                base_revenue *= 1.2
            elif month in [12, 1, 2]:  # 冬季
                base_revenue *= 0.9
            
            # 添加随机噪声
            revenue = base_revenue * (1 + random.uniform(-0.3, 0.3))
            
            sales_data.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'revenue': max(revenue, 500),  # 最小收入500
                'order_count': random.randint(10, 50),
                'customer_count': random.randint(8, 40)
            })
        
        return sales_data
    
    def _get_customer_order_data(self, store_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """获取客户订单数据"""
        # 这里应该从数据库查询真实数据
        # 现在返回模拟数据
        import random
        
        orders = []
        base_date = datetime.now() - timedelta(days=365)
        
        for customer_id in range(1, 101):
            # 每个客户的首次购买时间
            first_purchase = base_date + timedelta(days=random.randint(0, 300))
            
            # 生成该客户的订单
            order_count = random.randint(1, 10)
            for i in range(order_count):
                order_date = first_purchase + timedelta(days=random.randint(0, 60) * i)
                if order_date <= datetime.now():
                    orders.append({
                        'customer_id': customer_id,
                        'order_date': order_date.strftime('%Y-%m-%d'),
                        'order_amount': random.uniform(100, 800)
                    })
        
        return orders
    
    def _find_optimal_clusters(self, X: np.ndarray, max_k: int = 10) -> int:
        """寻找最优聚类数量"""
        inertias = []
        K_range = range(2, min(max_k + 1, len(X)))
        
        for k in K_range:
            kmeans = KMeans(n_clusters=k, random_state=42)
            kmeans.fit(X)
            inertias.append(kmeans.inertia_)
        
        # 使用肘部法则
        if len(inertias) >= 2:
            # 简化的肘部检测
            diffs = np.diff(inertias)
            diff2 = np.diff(diffs)
            if len(diff2) > 0:
                elbow_idx = np.argmax(diff2) + 2
                return min(K_range[elbow_idx], 5)
        
        return 4  # 默认4个聚类
    
    def _analyze_customer_segments(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """分析客户细分群体"""
        segments = []
        
        for segment_id in df['segment'].unique():
            segment_data = df[df['segment'] == segment_id]
            
            segments.append({
                'segment_id': int(segment_id),
                'segment_name': self._get_segment_name(segment_data),
                'customer_count': len(segment_data),
                'percentage': len(segment_data) / len(df) * 100,
                'avg_recency': segment_data['recency'].mean(),
                'avg_frequency': segment_data['frequency'].mean(),
                'avg_monetary': segment_data['monetary'].mean(),
                'characteristics': self._get_segment_characteristics(segment_data)
            })
        
        return segments
    
    def _get_segment_name(self, segment_data: pd.DataFrame) -> str:
        """获取细分群体名称"""
        avg_recency = segment_data['recency'].mean()
        avg_frequency = segment_data['frequency'].mean()
        avg_monetary = segment_data['monetary'].mean()
        
        if avg_recency < 30 and avg_frequency > 10 and avg_monetary > 2000:
            return "VIP客户"
        elif avg_recency < 60 and avg_frequency > 5:
            return "忠诚客户"
        elif avg_recency < 90 and avg_monetary > 1000:
            return "潜力客户"
        elif avg_recency > 180:
            return "流失客户"
        else:
            return "普通客户"
    
    def _get_segment_characteristics(self, segment_data: pd.DataFrame) -> List[str]:
        """获取细分群体特征"""
        characteristics = []
        
        avg_recency = segment_data['recency'].mean()
        avg_frequency = segment_data['frequency'].mean()
        avg_monetary = segment_data['monetary'].mean()
        
        if avg_recency < 30:
            characteristics.append("最近活跃")
        elif avg_recency > 180:
            characteristics.append("长期未消费")
        
        if avg_frequency > 10:
            characteristics.append("高频消费")
        elif avg_frequency < 3:
            characteristics.append("低频消费")
        
        if avg_monetary > 2000:
            characteristics.append("高价值")
        elif avg_monetary < 500:
            characteristics.append("低价值")
        
        return characteristics
    
    def _generate_segmentation_insights(self, segments: List[Dict[str, Any]]) -> List[str]:
        """生成客户细分洞察"""
        insights = []
        
        # 找出最大和最小的细分群体
        largest_segment = max(segments, key=lambda x: x['customer_count'])
        highest_value_segment = max(segments, key=lambda x: x['avg_monetary'])
        
        insights.append(f"最大的客户群体是{largest_segment['segment_name']}，占比{largest_segment['percentage']:.1f}%")
        insights.append(f"价值最高的客户群体是{highest_value_segment['segment_name']}，平均消费{highest_value_segment['avg_monetary']:.0f}元")
        
        # 分析流失风险
        at_risk_segments = [s for s in segments if s['avg_recency'] > 120]
        if at_risk_segments:
            total_at_risk = sum(s['customer_count'] for s in at_risk_segments)
            insights.append(f"有{total_at_risk}位客户存在流失风险，需要重点关注")
        
        return insights
    
    def _generate_segmentation_recommendations(self, segments: List[Dict[str, Any]]) -> List[str]:
        """生成客户细分建议"""
        recommendations = []
        
        for segment in segments:
            if "VIP客户" in segment['segment_name']:
                recommendations.append(f"为{segment['segment_name']}提供专属服务和优先预约权")
            elif "流失客户" in segment['segment_name']:
                recommendations.append(f"对{segment['segment_name']}实施挽回营销活动")
            elif "潜力客户" in segment['segment_name']:
                recommendations.append(f"通过个性化推荐提升{segment['segment_name']}的消费频次")
        
        return recommendations
    
    def _generate_sales_forecast(self, model, df: pd.DataFrame, forecast_days: int) -> List[Dict[str, Any]]:
        """生成销售预测"""
        forecast_data = []
        last_date = df['date'].max()
        
        for i in range(1, forecast_days + 1):
            forecast_date = last_date + timedelta(days=i)
            
            # 准备特征
            features = [
                forecast_date.weekday(),
                forecast_date.month,
                forecast_date.day,
                1 if forecast_date.weekday() in [5, 6] else 0,
                df['revenue'].tail(7).mean(),  # 简化的移动平均
                df['revenue'].tail(30).mean()
            ]
            
            predicted_revenue = model.predict([features])[0]
            
            forecast_data.append({
                'date': forecast_date.strftime('%Y-%m-%d'),
                'predicted_revenue': max(predicted_revenue, 0),
                'confidence_interval': {
                    'lower': predicted_revenue * 0.8,
                    'upper': predicted_revenue * 1.2
                }
            })
        
        return forecast_data
    
    def _calculate_growth_rate(self, revenue_series: pd.Series) -> float:
        """计算增长率"""
        if len(revenue_series) < 2:
            return 0.0
        
        first_half = revenue_series[:len(revenue_series)//2].mean()
        second_half = revenue_series[len(revenue_series)//2:].mean()
        
        if first_half > 0:
            return (second_half - first_half) / first_half * 100
        return 0.0
    
    def _detect_seasonality(self, df: pd.DataFrame) -> Dict[str, Any]:
        """检测季节性"""
        df['month'] = pd.to_datetime(df['date']).dt.month
        monthly_avg = df.groupby('month')['revenue'].mean()
        
        return {
            'peak_month': int(monthly_avg.idxmax()),
            'low_month': int(monthly_avg.idxmin()),
            'seasonal_variation': (monthly_avg.max() - monthly_avg.min()) / monthly_avg.mean() * 100
        }
    
    def _generate_forecasting_insights(self, df: pd.DataFrame, forecast_data: List[Dict[str, Any]], r2: float) -> List[str]:
        """生成预测洞察"""
        insights = []
        
        current_avg = df['revenue'].tail(30).mean()
        forecast_avg = np.mean([f['predicted_revenue'] for f in forecast_data])
        
        if forecast_avg > current_avg * 1.1:
            insights.append("预测显示未来收入将有显著增长")
        elif forecast_avg < current_avg * 0.9:
            insights.append("预测显示未来收入可能下降，需要关注")
        else:
            insights.append("预测显示收入将保持稳定")
        
        if r2 > 0.8:
            insights.append("预测模型准确性较高，可信度良好")
        elif r2 > 0.6:
            insights.append("预测模型准确性中等，建议结合其他因素判断")
        else:
            insights.append("预测模型准确性较低，仅供参考")
        
        return insights
    
    def _generate_forecasting_recommendations(self, forecast_data: List[Dict[str, Any]]) -> List[str]:
        """生成预测建议"""
        recommendations = []
        
        revenues = [f['predicted_revenue'] for f in forecast_data]
        
        if max(revenues) > min(revenues) * 1.5:
            recommendations.append("收入波动较大，建议制定灵活的人员排班计划")
        
        recommendations.append("根据预测结果调整库存和服务安排")
        recommendations.append("在预测收入较高的时段增加营销投入")
        
        return recommendations
    
    def _generate_cohort_insights(self, retention_table: pd.DataFrame, avg_retention: Dict[str, float]) -> List[str]:
        """生成队列分析洞察"""
        insights = []
        
        if avg_retention['month_1'] > 0.5:
            insights.append("首月留存率较高，客户粘性良好")
        else:
            insights.append("首月留存率偏低，需要改善新客户体验")
        
        if avg_retention['month_6'] > 0.3:
            insights.append("半年留存率表现良好，客户忠诚度较高")
        else:
            insights.append("半年留存率需要提升，建议加强客户关系维护")
        
        return insights
    
    def _generate_cohort_recommendations(self, avg_retention: Dict[str, float]) -> List[str]:
        """生成队列分析建议"""
        recommendations = []
        
        if avg_retention['month_1'] < 0.4:
            recommendations.append("优化新客户引导流程，提升首月留存率")
        
        if avg_retention['month_3'] < 0.3:
            recommendations.append("建立3个月客户回访机制，防止客户流失")
        
        recommendations.append("针对不同队列制定差异化的营销策略")
        
        return recommendations
