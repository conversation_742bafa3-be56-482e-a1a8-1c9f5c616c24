"""
数据导出服务
支持Excel、PDF、CSV等格式的数据导出，包括图表导出和自定义模板
"""

import io
import base64
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.chart import <PERSON><PERSON>hart, LineChart, PieChart, Reference
from openpyxl.drawing.image import Image
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image as RLImage
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.pdfgen import canvas
import logging

logger = logging.getLogger(__name__)


class ExportService:
    """数据导出服务"""
    
    def __init__(self):
        self.supported_formats = ['excel', 'csv', 'pdf', 'json']
        self.chart_formats = ['png', 'jpg', 'svg']
    
    def export_to_excel(
        self,
        data: List[Dict[str, Any]],
        filename: str = None,
        sheet_name: str = "数据",
        include_charts: bool = False,
        chart_data: Optional[Dict] = None,
        template_config: Optional[Dict] = None
    ) -> bytes:
        """
        导出数据到Excel格式
        
        Args:
            data: 要导出的数据
            filename: 文件名
            sheet_name: 工作表名称
            include_charts: 是否包含图表
            chart_data: 图表数据
            template_config: 模板配置
        
        Returns:
            Excel文件的字节数据
        """
        try:
            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = sheet_name
            
            if not data:
                # 如果没有数据，创建空表
                ws['A1'] = "暂无数据"
                output = io.BytesIO()
                wb.save(output)
                return output.getvalue()
            
            # 获取列名
            columns = list(data[0].keys())
            
            # 应用模板配置
            if template_config:
                self._apply_excel_template(ws, template_config)
            
            # 写入标题行
            title_row = 1
            if template_config and template_config.get('title'):
                ws.merge_cells(f'A1:{chr(65 + len(columns) - 1)}1')
                ws['A1'] = template_config['title']
                ws['A1'].font = Font(size=16, bold=True)
                ws['A1'].alignment = Alignment(horizontal='center')
                title_row = 3
            
            # 写入列标题
            for col_idx, column in enumerate(columns, 1):
                cell = ws.cell(row=title_row, column=col_idx, value=self._format_column_name(column))
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                cell.alignment = Alignment(horizontal='center')
            
            # 写入数据
            for row_idx, row_data in enumerate(data, title_row + 1):
                for col_idx, column in enumerate(columns, 1):
                    value = row_data.get(column, '')
                    # 格式化数值
                    if isinstance(value, (int, float)):
                        if column in ['total_amount', 'revenue', 'sales']:
                            value = f"¥{value:,.2f}"
                        elif column in ['percentage', 'rate']:
                            value = f"{value:.2%}"
                    
                    cell = ws.cell(row=row_idx, column=col_idx, value=value)
                    
                    # 应用数据格式
                    if row_idx % 2 == 0:
                        cell.fill = PatternFill(start_color="F8F8F8", end_color="F8F8F8", fill_type="solid")
            
            # 添加边框
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            
            for row in ws.iter_rows(min_row=title_row, max_row=len(data) + title_row, 
                                  min_col=1, max_col=len(columns)):
                for cell in row:
                    cell.border = thin_border
            
            # 自动调整列宽
            for col_idx in range(1, len(columns) + 1):
                max_length = 0
                column_letter = chr(64 + col_idx)  # A, B, C, ...

                for row_idx in range(title_row, len(data) + title_row + 1):
                    try:
                        cell = ws.cell(row=row_idx, column=col_idx)
                        if cell.value and len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # 添加图表
            if include_charts and chart_data:
                self._add_excel_charts(wb, ws, chart_data, len(data) + title_row + 3)
            
            # 添加统计信息
            self._add_excel_summary(ws, data, len(data) + title_row + 2)
            
            # 保存到字节流
            output = io.BytesIO()
            wb.save(output)
            return output.getvalue()
            
        except Exception as e:
            logger.error(f"Excel导出失败: {str(e)}")
            raise
    
    def export_to_csv(
        self,
        data: List[Dict[str, Any]],
        filename: str = None,
        encoding: str = 'utf-8-sig'
    ) -> bytes:
        """
        导出数据到CSV格式
        
        Args:
            data: 要导出的数据
            filename: 文件名
            encoding: 编码格式
        
        Returns:
            CSV文件的字节数据
        """
        try:
            if not data:
                return "暂无数据".encode(encoding)
            
            # 转换为DataFrame
            df = pd.DataFrame(data)
            
            # 格式化列名
            df.columns = [self._format_column_name(col) for col in df.columns]
            
            # 格式化数值
            for column in df.columns:
                if df[column].dtype in ['int64', 'float64']:
                    if any(keyword in column.lower() for keyword in ['金额', '收入', '销售额']):
                        df[column] = df[column].apply(lambda x: f"¥{x:,.2f}" if pd.notna(x) else '')
                    elif any(keyword in column.lower() for keyword in ['百分比', '比率', '率']):
                        df[column] = df[column].apply(lambda x: f"{x:.2%}" if pd.notna(x) else '')
            
            # 转换为CSV
            output = io.StringIO()
            df.to_csv(output, index=False, encoding=encoding)
            return output.getvalue().encode(encoding)
            
        except Exception as e:
            logger.error(f"CSV导出失败: {str(e)}")
            raise
    
    def export_to_pdf(
        self,
        data: List[Dict[str, Any]],
        filename: str = None,
        title: str = "数据报表",
        include_charts: bool = False,
        chart_images: Optional[List[bytes]] = None,
        template_config: Optional[Dict] = None
    ) -> bytes:
        """
        导出数据到PDF格式
        
        Args:
            data: 要导出的数据
            filename: 文件名
            title: 报表标题
            include_charts: 是否包含图表
            chart_images: 图表图片数据
            template_config: 模板配置
        
        Returns:
            PDF文件的字节数据
        """
        try:
            output = io.BytesIO()
            doc = SimpleDocTemplate(output, pagesize=A4)
            story = []
            
            # 获取样式
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1  # 居中
            )
            
            # 添加标题
            story.append(Paragraph(title, title_style))
            story.append(Spacer(1, 12))
            
            # 添加生成时间
            story.append(Paragraph(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
            story.append(Spacer(1, 12))
            
            if not data:
                story.append(Paragraph("暂无数据", styles['Normal']))
            else:
                # 准备表格数据
                columns = list(data[0].keys())
                formatted_columns = [self._format_column_name(col) for col in columns]
                
                # 表格数据
                table_data = [formatted_columns]
                for row in data:
                    formatted_row = []
                    for col in columns:
                        value = row.get(col, '')
                        if isinstance(value, (int, float)):
                            if col in ['total_amount', 'revenue', 'sales']:
                                value = f"¥{value:,.2f}"
                            elif col in ['percentage', 'rate']:
                                value = f"{value:.2%}"
                        formatted_row.append(str(value))
                    table_data.append(formatted_row)
                
                # 创建表格
                table = Table(table_data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                story.append(table)
                story.append(Spacer(1, 12))
            
            # 添加图表
            if include_charts and chart_images:
                story.append(Paragraph("图表分析", styles['Heading2']))
                story.append(Spacer(1, 12))
                
                for i, chart_image in enumerate(chart_images):
                    try:
                        img_io = io.BytesIO(chart_image)
                        img = RLImage(img_io, width=400, height=300)
                        story.append(img)
                        story.append(Spacer(1, 12))
                    except Exception as e:
                        logger.warning(f"添加图表{i+1}失败: {str(e)}")
            
            # 添加统计摘要
            if data:
                story.append(Paragraph("数据摘要", styles['Heading2']))
                story.append(Spacer(1, 12))
                
                summary_data = self._generate_data_summary(data)
                for key, value in summary_data.items():
                    story.append(Paragraph(f"{key}: {value}", styles['Normal']))
                
                story.append(Spacer(1, 12))
            
            # 生成PDF
            doc.build(story)
            return output.getvalue()
            
        except Exception as e:
            logger.error(f"PDF导出失败: {str(e)}")
            raise
    
    def _format_column_name(self, column: str) -> str:
        """格式化列名"""
        column_mapping = {
            'store_name': '门店名称',
            'total_amount': '总金额',
            'order_count': '订单数量',
            'customer_count': '客户数量',
            'avg_order_value': '平均订单价值',
            'revenue': '营收',
            'profit': '利润',
            'date': '日期',
            'month': '月份',
            'week': '周',
            'day': '日',
            'item_name': '项目名称',
            'quantity': '数量',
            'price': '价格',
            'employee_name': '员工姓名',
            'customer_name': '客户姓名',
            'status': '状态',
            'created_at': '创建时间',
            'updated_at': '更新时间'
        }
        return column_mapping.get(column, column)
    
    def _apply_excel_template(self, ws, template_config: Dict):
        """应用Excel模板配置"""
        try:
            if template_config.get('freeze_panes'):
                ws.freeze_panes = template_config['freeze_panes']
            
            if template_config.get('column_widths'):
                for col, width in template_config['column_widths'].items():
                    ws.column_dimensions[col].width = width
            
            if template_config.get('row_heights'):
                for row, height in template_config['row_heights'].items():
                    ws.row_dimensions[row].height = height
                    
        except Exception as e:
            logger.warning(f"应用Excel模板失败: {str(e)}")
    
    def _add_excel_charts(self, wb, ws, chart_data: Dict, start_row: int):
        """添加Excel图表"""
        try:
            if chart_data.get('type') == 'bar':
                chart = BarChart()
            elif chart_data.get('type') == 'line':
                chart = LineChart()
            elif chart_data.get('type') == 'pie':
                chart = PieChart()
            else:
                return
            
            chart.title = chart_data.get('title', '图表')
            chart.style = 10
            chart.x_axis.title = chart_data.get('x_title', 'X轴')
            chart.y_axis.title = chart_data.get('y_title', 'Y轴')
            
            # 添加数据引用
            data_range = chart_data.get('data_range', 'A1:B10')
            data = Reference(ws, range_string=data_range)
            chart.add_data(data, titles_from_data=True)
            
            # 添加到工作表
            ws.add_chart(chart, f"A{start_row}")
            
        except Exception as e:
            logger.warning(f"添加Excel图表失败: {str(e)}")
    
    def _add_excel_summary(self, ws, data: List[Dict], start_row: int):
        """添加Excel统计摘要"""
        try:
            ws.cell(row=start_row, column=1, value="数据统计").font = Font(bold=True)
            ws.cell(row=start_row + 1, column=1, value=f"总记录数: {len(data)}")
            ws.cell(row=start_row + 2, column=1, value=f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 计算数值列的统计信息
            numeric_columns = []
            for col in data[0].keys():
                if all(isinstance(row.get(col), (int, float)) for row in data):
                    numeric_columns.append(col)
            
            for i, col in enumerate(numeric_columns[:3]):  # 最多显示3个数值列的统计
                values = [row[col] for row in data if isinstance(row.get(col), (int, float))]
                if values:
                    avg_val = sum(values) / len(values)
                    max_val = max(values)
                    min_val = min(values)
                    
                    col_name = self._format_column_name(col)
                    ws.cell(row=start_row + 3 + i * 3, column=1, value=f"{col_name}统计:").font = Font(bold=True)
                    ws.cell(row=start_row + 4 + i * 3, column=1, value=f"  平均值: {avg_val:.2f}")
                    ws.cell(row=start_row + 5 + i * 3, column=1, value=f"  最大值: {max_val}")
                    ws.cell(row=start_row + 6 + i * 3, column=1, value=f"  最小值: {min_val}")
                    
        except Exception as e:
            logger.warning(f"添加Excel统计摘要失败: {str(e)}")
    
    def _generate_data_summary(self, data: List[Dict]) -> Dict[str, str]:
        """生成数据摘要"""
        try:
            summary = {
                "总记录数": str(len(data)),
                "数据列数": str(len(data[0].keys()) if data else 0)
            }
            
            # 计算数值列统计
            if data:
                for col in data[0].keys():
                    values = [row.get(col) for row in data if isinstance(row.get(col), (int, float))]
                    if values and len(values) > 0:
                        col_name = self._format_column_name(col)
                        summary[f"{col_name}平均值"] = f"{sum(values) / len(values):.2f}"
                        summary[f"{col_name}总计"] = f"{sum(values):.2f}"
            
            return summary
            
        except Exception as e:
            logger.warning(f"生成数据摘要失败: {str(e)}")
            return {"总记录数": str(len(data))}


# 全局导出服务实例
export_service = ExportService()
