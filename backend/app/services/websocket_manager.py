"""
WebSocket管理器
实现实时数据推送和连接管理
"""

import json
import asyncio
from typing import Dict, List, Set, Any, Optional
from datetime import datetime, timedelta
from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session
from sqlalchemy import func
from app.db.session import SessionLocal
from app.models.operation import Order
from app.models.customer import Customer
from app.models.store import Store
import logging

logger = logging.getLogger(__name__)


class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 存储活跃连接
        self.active_connections: Dict[str, WebSocket] = {}
        # 存储用户订阅的数据类型
        self.subscriptions: Dict[str, Set[str]] = {}
        # 存储连接的元数据
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        
        # 支持的数据类型
        self.supported_data_types = {
            'business_indicators',  # 业务指标
            'real_time_orders',     # 实时订单
            'customer_activity',    # 客户活动
            'store_performance',    # 门店表现
            'system_alerts'         # 系统警报
        }
    
    async def connect(self, websocket: WebSocket, client_id: str, user_info: Dict[str, Any] = None):
        """建立WebSocket连接"""
        try:
            await websocket.accept()
            self.active_connections[client_id] = websocket
            self.subscriptions[client_id] = set()
            self.connection_metadata[client_id] = {
                'connected_at': datetime.now(),
                'user_info': user_info or {},
                'last_activity': datetime.now()
            }
            
            logger.info(f"WebSocket连接已建立: {client_id}")
            
            # 发送连接成功消息
            await self.send_personal_message(client_id, {
                'type': 'connection_established',
                'client_id': client_id,
                'supported_data_types': list(self.supported_data_types),
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"WebSocket连接失败: {client_id}, 错误: {str(e)}")
            raise
    
    def disconnect(self, client_id: str):
        """断开WebSocket连接"""
        try:
            if client_id in self.active_connections:
                del self.active_connections[client_id]
            if client_id in self.subscriptions:
                del self.subscriptions[client_id]
            if client_id in self.connection_metadata:
                del self.connection_metadata[client_id]
            
            logger.info(f"WebSocket连接已断开: {client_id}")
            
        except Exception as e:
            logger.error(f"断开WebSocket连接失败: {client_id}, 错误: {str(e)}")
    
    async def send_personal_message(self, client_id: str, message: Dict[str, Any]):
        """发送个人消息"""
        try:
            if client_id in self.active_connections:
                websocket = self.active_connections[client_id]
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
                
                # 更新最后活动时间
                if client_id in self.connection_metadata:
                    self.connection_metadata[client_id]['last_activity'] = datetime.now()
                    
        except WebSocketDisconnect:
            logger.info(f"客户端主动断开连接: {client_id}")
            self.disconnect(client_id)
        except Exception as e:
            logger.error(f"发送个人消息失败: {client_id}, 错误: {str(e)}")
            self.disconnect(client_id)
    
    async def broadcast_message(self, message: Dict[str, Any], data_type: str = None):
        """广播消息"""
        disconnected_clients = []
        
        for client_id, websocket in self.active_connections.items():
            try:
                # 检查客户端是否订阅了此数据类型
                if data_type and data_type not in self.subscriptions.get(client_id, set()):
                    continue
                
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
                
                # 更新最后活动时间
                if client_id in self.connection_metadata:
                    self.connection_metadata[client_id]['last_activity'] = datetime.now()
                    
            except WebSocketDisconnect:
                logger.info(f"广播时发现客户端断开: {client_id}")
                disconnected_clients.append(client_id)
            except Exception as e:
                logger.error(f"广播消息失败: {client_id}, 错误: {str(e)}")
                disconnected_clients.append(client_id)
        
        # 清理断开的连接
        for client_id in disconnected_clients:
            self.disconnect(client_id)
    
    async def subscribe(self, client_id: str, data_types: List[str]):
        """订阅数据类型"""
        try:
            if client_id not in self.subscriptions:
                self.subscriptions[client_id] = set()
            
            valid_types = []
            for data_type in data_types:
                if data_type in self.supported_data_types:
                    self.subscriptions[client_id].add(data_type)
                    valid_types.append(data_type)
                else:
                    logger.warning(f"不支持的数据类型: {data_type}")
            
            await self.send_personal_message(client_id, {
                'type': 'subscription_confirmed',
                'subscribed_types': valid_types,
                'timestamp': datetime.now().isoformat()
            })
            
            logger.info(f"客户端 {client_id} 订阅了数据类型: {valid_types}")
            
        except Exception as e:
            logger.error(f"订阅失败: {client_id}, 错误: {str(e)}")
    
    async def unsubscribe(self, client_id: str, data_types: List[str]):
        """取消订阅数据类型"""
        try:
            if client_id in self.subscriptions:
                for data_type in data_types:
                    self.subscriptions[client_id].discard(data_type)
            
            await self.send_personal_message(client_id, {
                'type': 'unsubscription_confirmed',
                'unsubscribed_types': data_types,
                'timestamp': datetime.now().isoformat()
            })
            
            logger.info(f"客户端 {client_id} 取消订阅数据类型: {data_types}")
            
        except Exception as e:
            logger.error(f"取消订阅失败: {client_id}, 错误: {str(e)}")
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        try:
            total_connections = len(self.active_connections)
            subscription_stats = {}
            
            for data_type in self.supported_data_types:
                count = sum(1 for subs in self.subscriptions.values() if data_type in subs)
                subscription_stats[data_type] = count
            
            return {
                'total_connections': total_connections,
                'subscription_stats': subscription_stats,
                'active_clients': list(self.active_connections.keys()),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取连接统计失败: {str(e)}")
            return {'error': str(e)}


class RealTimeDataService:
    """实时数据服务"""
    
    def __init__(self, connection_manager: ConnectionManager):
        self.connection_manager = connection_manager
        self.is_running = False
        self.update_interval = 5  # 5秒更新一次
        
    async def start_real_time_updates(self):
        """启动实时数据更新"""
        if self.is_running:
            return
        
        self.is_running = True
        logger.info("实时数据更新服务已启动")
        
        # 启动各种数据更新任务
        await asyncio.gather(
            self._update_business_indicators(),
            self._update_real_time_orders(),
            self._update_customer_activity(),
            self._update_store_performance(),
            return_exceptions=True
        )
    
    def stop_real_time_updates(self):
        """停止实时数据更新"""
        self.is_running = False
        logger.info("实时数据更新服务已停止")
    
    async def _update_business_indicators(self):
        """更新业务指标"""
        while self.is_running:
            try:
                db = SessionLocal()
                try:
                    # 获取最新的业务指标
                    today = datetime.now().date()
                    
                    # 今日订单统计
                    today_orders = db.query(Order).filter(
                        Order.order_time >= today,
                        Order.is_deleted == False
                    ).count()
                    
                    # 今日营收
                    today_revenue = db.query(func.sum(Order.total_amount)).filter(
                        Order.order_time >= today,
                        Order.is_deleted == False
                    ).scalar() or 0
                    
                    # 活跃门店数
                    active_stores = db.query(Store).filter(
                        Store.status == 'active',
                        Store.is_deleted == False
                    ).count()
                    
                    # 推送数据
                    await self.connection_manager.broadcast_message({
                        'type': 'business_indicators_update',
                        'data': {
                            'today_orders': today_orders,
                            'today_revenue': float(today_revenue),
                            'active_stores': active_stores,
                            'update_time': datetime.now().isoformat()
                        },
                        'timestamp': datetime.now().isoformat()
                    }, 'business_indicators')
                    
                finally:
                    db.close()
                
                await asyncio.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"更新业务指标失败: {str(e)}")
                await asyncio.sleep(self.update_interval)
    
    async def _update_real_time_orders(self):
        """更新实时订单"""
        while self.is_running:
            try:
                db = SessionLocal()
                try:
                    # 获取最近5分钟的订单
                    five_minutes_ago = datetime.now() - timedelta(minutes=5)
                    
                    recent_orders = db.query(Order).filter(
                        Order.order_time >= five_minutes_ago,
                        Order.is_deleted == False
                    ).order_by(Order.order_time.desc()).limit(10).all()
                    
                    orders_data = []
                    for order in recent_orders:
                        orders_data.append({
                            'id': order.id,
                            'order_number': order.order_number,
                            'total_amount': float(order.total_amount),
                            'order_time': order.order_time.isoformat(),
                            'store_id': order.store_id,
                            'customer_id': order.customer_id
                        })
                    
                    # 推送数据
                    await self.connection_manager.broadcast_message({
                        'type': 'real_time_orders_update',
                        'data': {
                            'recent_orders': orders_data,
                            'count': len(orders_data),
                            'update_time': datetime.now().isoformat()
                        },
                        'timestamp': datetime.now().isoformat()
                    }, 'real_time_orders')
                    
                finally:
                    db.close()
                
                await asyncio.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"更新实时订单失败: {str(e)}")
                await asyncio.sleep(self.update_interval)
    
    async def _update_customer_activity(self):
        """更新客户活动"""
        while self.is_running:
            try:
                db = SessionLocal()
                try:
                    # 获取今日新客户
                    today = datetime.now().date()
                    
                    new_customers_today = db.query(Customer).filter(
                        Customer.created_at >= today,
                        Customer.is_deleted == False
                    ).count()
                    
                    # 获取活跃客户（今日有订单的客户）
                    active_customers_today = db.query(func.count(func.distinct(Order.customer_id))).filter(
                        Order.order_time >= today,
                        Order.customer_id.isnot(None),
                        Order.is_deleted == False
                    ).scalar() or 0
                    
                    # 推送数据
                    await self.connection_manager.broadcast_message({
                        'type': 'customer_activity_update',
                        'data': {
                            'new_customers_today': new_customers_today,
                            'active_customers_today': active_customers_today,
                            'update_time': datetime.now().isoformat()
                        },
                        'timestamp': datetime.now().isoformat()
                    }, 'customer_activity')
                    
                finally:
                    db.close()
                
                await asyncio.sleep(self.update_interval * 2)  # 客户活动更新频率稍低
                
            except Exception as e:
                logger.error(f"更新客户活动失败: {str(e)}")
                await asyncio.sleep(self.update_interval * 2)
    
    async def _update_store_performance(self):
        """更新门店表现"""
        while self.is_running:
            try:
                db = SessionLocal()
                try:
                    # 获取各门店今日表现
                    today = datetime.now().date()
                    
                    store_performance = db.query(
                        Order.store_id,
                        func.count(Order.id).label('order_count'),
                        func.sum(Order.total_amount).label('revenue')
                    ).filter(
                        Order.order_time >= today,
                        Order.is_deleted == False
                    ).group_by(Order.store_id).all()
                    
                    performance_data = []
                    for perf in store_performance:
                        performance_data.append({
                            'store_id': perf.store_id,
                            'order_count': perf.order_count,
                            'revenue': float(perf.revenue or 0)
                        })
                    
                    # 推送数据
                    await self.connection_manager.broadcast_message({
                        'type': 'store_performance_update',
                        'data': {
                            'store_performance': performance_data,
                            'update_time': datetime.now().isoformat()
                        },
                        'timestamp': datetime.now().isoformat()
                    }, 'store_performance')
                    
                finally:
                    db.close()
                
                await asyncio.sleep(self.update_interval * 3)  # 门店表现更新频率更低
                
            except Exception as e:
                logger.error(f"更新门店表现失败: {str(e)}")
                await asyncio.sleep(self.update_interval * 3)


# 全局连接管理器和实时数据服务
connection_manager = ConnectionManager()
real_time_service = RealTimeDataService(connection_manager)
