"""
系统监控和日志服务
提供错误监控、性能监控、日志管理等功能
"""

import os
import sys
import time
import psutil
import logging
import traceback
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.utils.redis_cache import RedisCache
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


@dataclass
class ErrorRecord:
    """错误记录"""
    timestamp: datetime
    error_type: str
    error_message: str
    stack_trace: str
    request_path: Optional[str] = None
    user_id: Optional[str] = None
    request_data: Optional[Dict] = None
    severity: str = "ERROR"  # ERROR, WARNING, CRITICAL
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class PerformanceMetric:
    """性能指标"""
    timestamp: datetime
    metric_name: str
    value: float
    unit: str
    tags: Optional[Dict[str, str]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self):
        self.cache = RedisCache()
        self.error_records = deque(maxlen=1000)  # 最多保存1000条错误记录
        self.performance_metrics = deque(maxlen=5000)  # 最多保存5000条性能指标
        self.alert_thresholds = {
            'cpu_usage': 80.0,      # CPU使用率阈值
            'memory_usage': 85.0,   # 内存使用率阈值
            'disk_usage': 90.0,     # 磁盘使用率阈值
            'response_time': 5.0,   # 响应时间阈值（秒）
            'error_rate': 10.0      # 错误率阈值（每分钟）
        }
        self.is_monitoring = False
        
        # 确保日志目录存在
        os.makedirs('logs', exist_ok=True)
    
    def start_monitoring(self):
        """启动监控"""
        self.is_monitoring = True
        logger.info("系统监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        logger.info("系统监控已停止")
    
    def record_error(
        self,
        error: Exception,
        request_path: Optional[str] = None,
        user_id: Optional[str] = None,
        request_data: Optional[Dict] = None,
        severity: str = "ERROR"
    ):
        """记录错误"""
        try:
            error_record = ErrorRecord(
                timestamp=datetime.now(),
                error_type=type(error).__name__,
                error_message=str(error),
                stack_trace=traceback.format_exc(),
                request_path=request_path,
                user_id=user_id,
                request_data=request_data,
                severity=severity
            )
            
            self.error_records.append(error_record)
            
            # 记录到日志文件
            logger.error(f"错误记录: {error_record.error_type} - {error_record.error_message}")
            
            # 存储到Redis（如果可用）
            if self.cache.use_redis:
                try:
                    key = f"error_record:{datetime.now().strftime('%Y%m%d_%H%M%S')}:{id(error_record)}"
                    self.cache.redis_client.setex(
                        key, 
                        86400,  # 24小时过期
                        json.dumps(error_record.to_dict(), ensure_ascii=False)
                    )
                except Exception as e:
                    logger.warning(f"存储错误记录到Redis失败: {str(e)}")
            
            # 检查是否需要发送警报
            self._check_error_rate_alert()
            
        except Exception as e:
            logger.error(f"记录错误失败: {str(e)}")
    
    def record_performance_metric(
        self,
        metric_name: str,
        value: float,
        unit: str,
        tags: Optional[Dict[str, str]] = None
    ):
        """记录性能指标"""
        try:
            metric = PerformanceMetric(
                timestamp=datetime.now(),
                metric_name=metric_name,
                value=value,
                unit=unit,
                tags=tags or {}
            )
            
            self.performance_metrics.append(metric)
            
            # 存储到Redis（如果可用）
            if self.cache.use_redis:
                try:
                    key = f"performance_metric:{metric_name}:{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    self.cache.redis_client.setex(
                        key,
                        3600,  # 1小时过期
                        json.dumps(metric.to_dict(), ensure_ascii=False)
                    )
                except Exception as e:
                    logger.warning(f"存储性能指标到Redis失败: {str(e)}")
            
            # 检查是否超过阈值
            self._check_performance_alert(metric_name, value)
            
        except Exception as e:
            logger.error(f"记录性能指标失败: {str(e)}")
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # 网络IO
            network = psutil.net_io_counters()
            
            # 进程信息
            process = psutil.Process()
            process_memory = process.memory_info()
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'cpu': {
                    'usage_percent': cpu_percent,
                    'count': psutil.cpu_count()
                },
                'memory': {
                    'total': memory.total,
                    'available': memory.available,
                    'used': memory.used,
                    'usage_percent': memory_percent
                },
                'disk': {
                    'total': disk.total,
                    'used': disk.used,
                    'free': disk.free,
                    'usage_percent': disk_percent
                },
                'network': {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                },
                'process': {
                    'memory_rss': process_memory.rss,
                    'memory_vms': process_memory.vms,
                    'cpu_percent': process.cpu_percent()
                }
            }
            
            # 记录性能指标
            self.record_performance_metric('cpu_usage', cpu_percent, 'percent')
            self.record_performance_metric('memory_usage', memory_percent, 'percent')
            self.record_performance_metric('disk_usage', disk_percent, 'percent')
            
            return metrics
            
        except Exception as e:
            logger.error(f"获取系统指标失败: {str(e)}")
            return {'error': str(e)}
    
    def get_error_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """获取错误统计"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # 过滤指定时间范围内的错误
            recent_errors = [
                error for error in self.error_records
                if error.timestamp >= cutoff_time
            ]
            
            # 按错误类型统计
            error_by_type = defaultdict(int)
            error_by_severity = defaultdict(int)
            error_by_hour = defaultdict(int)
            
            for error in recent_errors:
                error_by_type[error.error_type] += 1
                error_by_severity[error.severity] += 1
                hour_key = error.timestamp.strftime('%Y-%m-%d %H:00')
                error_by_hour[hour_key] += 1
            
            return {
                'total_errors': len(recent_errors),
                'time_range_hours': hours,
                'error_by_type': dict(error_by_type),
                'error_by_severity': dict(error_by_severity),
                'error_by_hour': dict(error_by_hour),
                'recent_errors': [error.to_dict() for error in recent_errors[-10:]]  # 最近10个错误
            }
            
        except Exception as e:
            logger.error(f"获取错误统计失败: {str(e)}")
            return {'error': str(e)}
    
    def get_performance_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """获取性能统计"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # 过滤指定时间范围内的性能指标
            recent_metrics = [
                metric for metric in self.performance_metrics
                if metric.timestamp >= cutoff_time
            ]
            
            # 按指标名称分组
            metrics_by_name = defaultdict(list)
            for metric in recent_metrics:
                metrics_by_name[metric.metric_name].append(metric.value)
            
            # 计算统计信息
            statistics = {}
            for name, values in metrics_by_name.items():
                if values:
                    statistics[name] = {
                        'count': len(values),
                        'avg': sum(values) / len(values),
                        'min': min(values),
                        'max': max(values),
                        'latest': values[-1] if values else 0
                    }
            
            return {
                'time_range_hours': hours,
                'metrics_count': len(recent_metrics),
                'statistics': statistics
            }
            
        except Exception as e:
            logger.error(f"获取性能统计失败: {str(e)}")
            return {'error': str(e)}
    
    def _check_error_rate_alert(self):
        """检查错误率警报"""
        try:
            # 计算最近1分钟的错误数
            one_minute_ago = datetime.now() - timedelta(minutes=1)
            recent_errors = [
                error for error in self.error_records
                if error.timestamp >= one_minute_ago
            ]
            
            error_rate = len(recent_errors)
            if error_rate >= self.alert_thresholds['error_rate']:
                self._send_alert(
                    'high_error_rate',
                    f'错误率过高: {error_rate} 错误/分钟',
                    {'error_rate': error_rate, 'threshold': self.alert_thresholds['error_rate']}
                )
                
        except Exception as e:
            logger.error(f"检查错误率警报失败: {str(e)}")
    
    def _check_performance_alert(self, metric_name: str, value: float):
        """检查性能警报"""
        try:
            threshold_key = metric_name.lower()
            if threshold_key in self.alert_thresholds:
                threshold = self.alert_thresholds[threshold_key]
                if value >= threshold:
                    self._send_alert(
                        f'high_{threshold_key}',
                        f'{metric_name} 超过阈值: {value}',
                        {'metric': metric_name, 'value': value, 'threshold': threshold}
                    )
                    
        except Exception as e:
            logger.error(f"检查性能警报失败: {str(e)}")
    
    def _send_alert(self, alert_type: str, message: str, data: Dict[str, Any]):
        """发送警报"""
        try:
            alert = {
                'type': alert_type,
                'message': message,
                'data': data,
                'timestamp': datetime.now().isoformat(),
                'severity': 'WARNING'
            }
            
            # 记录警报日志
            logger.warning(f"系统警报: {alert_type} - {message}")
            
            # 存储警报到Redis（如果可用）
            if self.cache.use_redis:
                try:
                    key = f"alert:{alert_type}:{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    self.cache.redis_client.setex(
                        key,
                        3600,  # 1小时过期
                        json.dumps(alert, ensure_ascii=False)
                    )
                except Exception as e:
                    logger.warning(f"存储警报到Redis失败: {str(e)}")
            
            # 这里可以添加其他警报通知方式，如邮件、短信、Webhook等
            
        except Exception as e:
            logger.error(f"发送警报失败: {str(e)}")
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        try:
            system_metrics = self.get_system_metrics()
            error_stats = self.get_error_statistics(hours=1)  # 最近1小时的错误
            
            # 计算健康分数
            health_score = 100
            issues = []
            
            # 检查CPU使用率
            cpu_usage = system_metrics.get('cpu', {}).get('usage_percent', 0)
            if cpu_usage > self.alert_thresholds['cpu_usage']:
                health_score -= 20
                issues.append(f"CPU使用率过高: {cpu_usage:.1f}%")
            
            # 检查内存使用率
            memory_usage = system_metrics.get('memory', {}).get('usage_percent', 0)
            if memory_usage > self.alert_thresholds['memory_usage']:
                health_score -= 20
                issues.append(f"内存使用率过高: {memory_usage:.1f}%")
            
            # 检查磁盘使用率
            disk_usage = system_metrics.get('disk', {}).get('usage_percent', 0)
            if disk_usage > self.alert_thresholds['disk_usage']:
                health_score -= 15
                issues.append(f"磁盘使用率过高: {disk_usage:.1f}%")
            
            # 检查错误率
            error_count = error_stats.get('total_errors', 0)
            if error_count > 5:  # 1小时内超过5个错误
                health_score -= 25
                issues.append(f"错误数量过多: {error_count} 个/小时")
            
            # 确定健康状态
            if health_score >= 90:
                status = "healthy"
            elif health_score >= 70:
                status = "warning"
            else:
                status = "critical"
            
            return {
                'status': status,
                'health_score': max(0, health_score),
                'issues': issues,
                'system_metrics': system_metrics,
                'error_stats': error_stats,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取健康状态失败: {str(e)}")
            return {
                'status': 'error',
                'health_score': 0,
                'issues': [f"健康检查失败: {str(e)}"],
                'timestamp': datetime.now().isoformat()
            }


# 全局监控实例
system_monitor = SystemMonitor()


def setup_monitoring():
    """设置监控"""
    system_monitor.start_monitoring()
    logger.info("监控系统已初始化")


def get_monitoring_stats() -> Dict[str, Any]:
    """获取监控统计信息"""
    return {
        'system_metrics': system_monitor.get_system_metrics(),
        'error_statistics': system_monitor.get_error_statistics(),
        'performance_statistics': system_monitor.get_performance_statistics(),
        'health_status': system_monitor.get_health_status()
    }
