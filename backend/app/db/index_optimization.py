"""
数据库索引优化脚本
为BI查询性能优化添加必要的数据库索引
"""

from sqlalchemy import text, Index
from sqlalchemy.orm import Session
from app.db.session import engine
from app.models.operation import Order, OrderItem
from app.models.customer import Customer
from app.models.store import Store
from app.models.employee import Employee
import logging

logger = logging.getLogger(__name__)


class DatabaseIndexOptimizer:
    """数据库索引优化器"""
    
    def __init__(self):
        self.indexes_to_create = [
            # Orders表的关键索引
            {
                "table": "orders",
                "name": "idx_orders_order_time",
                "columns": ["order_time"],
                "description": "订单时间索引，用于时间范围查询"
            },
            {
                "table": "orders", 
                "name": "idx_orders_store_id_order_time",
                "columns": ["store_id", "order_time"],
                "description": "门店+时间复合索引，用于门店时间范围查询"
            },
            {
                "table": "orders",
                "name": "idx_orders_customer_id_order_time", 
                "columns": ["customer_id", "order_time"],
                "description": "客户+时间复合索引，用于客户分析"
            },
            {
                "table": "orders",
                "name": "idx_orders_employee_id_order_time",
                "columns": ["employee_id", "order_time"], 
                "description": "员工+时间复合索引，用于员工业绩分析"
            },
            {
                "table": "orders",
                "name": "idx_orders_status_order_time",
                "columns": ["status", "order_time"],
                "description": "状态+时间复合索引，用于订单状态分析"
            },
            {
                "table": "orders",
                "name": "idx_orders_payment_method",
                "columns": ["payment_method"],
                "description": "支付方式索引，用于支付方式分析"
            },
            {
                "table": "orders",
                "name": "idx_orders_total_amount",
                "columns": ["total_amount"],
                "description": "订单金额索引，用于金额范围查询"
            },
            
            # OrderItems表的索引
            {
                "table": "order_items",
                "name": "idx_order_items_order_id",
                "columns": ["order_id"],
                "description": "订单ID索引，用于订单明细查询"
            },
            {
                "table": "order_items", 
                "name": "idx_order_items_store_item_id",
                "columns": ["store_item_id"],
                "description": "门店项目ID索引，用于项目销售分析"
            },
            {
                "table": "order_items",
                "name": "idx_order_items_service_employee_id",
                "columns": ["service_employee_id"],
                "description": "服务员工ID索引，用于员工服务分析"
            },
            
            # Customers表的索引
            {
                "table": "customers",
                "name": "idx_customers_phone",
                "columns": ["phone"],
                "description": "客户手机号索引，用于客户查找"
            },
            {
                "table": "customers",
                "name": "idx_customers_member_level_id",
                "columns": ["member_level_id"],
                "description": "会员等级索引，用于会员分析"
            },
            {
                "table": "customers",
                "name": "idx_customers_created_at",
                "columns": ["created_at"],
                "description": "客户创建时间索引，用于新客户分析"
            },
            
            # Stores表的索引
            {
                "table": "stores",
                "name": "idx_stores_status",
                "columns": ["status"],
                "description": "门店状态索引"
            },
            {
                "table": "stores",
                "name": "idx_stores_manager_id",
                "columns": ["manager_id"],
                "description": "门店经理索引"
            },
            
            # Employees表的索引
            {
                "table": "employees",
                "name": "idx_employees_store_id_role",
                "columns": ["store_id", "role"],
                "description": "门店+角色复合索引，用于员工分析"
            },
            {
                "table": "employees",
                "name": "idx_employees_status",
                "columns": ["status"],
                "description": "员工状态索引"
            },
            
            # 性能优化的部分索引（仅包含活跃数据）
            {
                "table": "orders",
                "name": "idx_orders_active_order_time",
                "columns": ["order_time"],
                "condition": "is_deleted = false",
                "description": "活跃订单时间索引（部分索引）"
            },
            {
                "table": "customers", 
                "name": "idx_customers_active_created_at",
                "columns": ["created_at"],
                "condition": "is_deleted = false",
                "description": "活跃客户创建时间索引（部分索引）"
            }
        ]
    
    def check_index_exists(self, db: Session, table_name: str, index_name: str) -> bool:
        """检查索引是否存在"""
        try:
            result = db.execute(text("""
                SELECT COUNT(*) as count
                FROM pg_indexes 
                WHERE tablename = :table_name AND indexname = :index_name
            """), {"table_name": table_name, "index_name": index_name})
            
            count = result.scalar()
            return count > 0
        except Exception as e:
            logger.error(f"检查索引存在性失败: {str(e)}")
            return False
    
    def create_index(self, db: Session, index_info: dict) -> bool:
        """创建单个索引"""
        try:
            table_name = index_info["table"]
            index_name = index_info["name"]
            columns = index_info["columns"]
            condition = index_info.get("condition")
            
            # 检查索引是否已存在
            if self.check_index_exists(db, table_name, index_name):
                logger.info(f"索引 {index_name} 已存在，跳过创建")
                return True
            
            # 构建CREATE INDEX语句
            columns_str = ", ".join(columns)
            
            if condition:
                # 部分索引
                sql = f"""
                CREATE INDEX CONCURRENTLY {index_name} 
                ON {table_name} ({columns_str}) 
                WHERE {condition}
                """
            else:
                # 普通索引
                sql = f"""
                CREATE INDEX CONCURRENTLY {index_name} 
                ON {table_name} ({columns_str})
                """
            
            logger.info(f"创建索引: {index_name} - {index_info['description']}")
            db.execute(text(sql))
            db.commit()
            
            logger.info(f"索引 {index_name} 创建成功")
            return True
            
        except Exception as e:
            logger.error(f"创建索引 {index_info['name']} 失败: {str(e)}")
            db.rollback()
            return False
    
    def create_all_indexes(self, db: Session) -> dict:
        """创建所有索引"""
        results = {
            "success": [],
            "failed": [],
            "skipped": []
        }
        
        logger.info("开始创建数据库索引...")
        
        for index_info in self.indexes_to_create:
            try:
                if self.create_index(db, index_info):
                    results["success"].append(index_info["name"])
                else:
                    results["failed"].append(index_info["name"])
            except Exception as e:
                logger.error(f"处理索引 {index_info['name']} 时出错: {str(e)}")
                results["failed"].append(index_info["name"])
        
        logger.info(f"索引创建完成: 成功 {len(results['success'])}, 失败 {len(results['failed'])}")
        return results
    
    def analyze_table_statistics(self, db: Session) -> dict:
        """分析表统计信息"""
        try:
            # 更新表统计信息
            tables = ["orders", "order_items", "customers", "stores", "employees"]
            
            for table in tables:
                logger.info(f"分析表 {table} 的统计信息...")
                db.execute(text(f"ANALYZE {table}"))
            
            db.commit()
            
            # 获取表大小信息
            result = db.execute(text("""
                SELECT 
                    schemaname,
                    tablename,
                    attname,
                    n_distinct,
                    correlation
                FROM pg_stats 
                WHERE schemaname = 'public' 
                AND tablename IN ('orders', 'order_items', 'customers', 'stores', 'employees')
                ORDER BY tablename, attname
            """))
            
            stats = []
            for row in result:
                stats.append({
                    "schema": row.schemaname,
                    "table": row.tablename, 
                    "column": row.attname,
                    "n_distinct": row.n_distinct,
                    "correlation": row.correlation
                })
            
            return {"status": "success", "statistics": stats}
            
        except Exception as e:
            logger.error(f"分析表统计信息失败: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    def get_slow_queries(self, db: Session) -> list:
        """获取慢查询信息（需要启用pg_stat_statements扩展）"""
        try:
            result = db.execute(text("""
                SELECT 
                    query,
                    calls,
                    total_time,
                    mean_time,
                    rows
                FROM pg_stat_statements 
                WHERE query LIKE '%orders%' OR query LIKE '%customers%'
                ORDER BY mean_time DESC 
                LIMIT 10
            """))
            
            slow_queries = []
            for row in result:
                slow_queries.append({
                    "query": row.query,
                    "calls": row.calls,
                    "total_time": row.total_time,
                    "mean_time": row.mean_time,
                    "rows": row.rows
                })
            
            return slow_queries
            
        except Exception as e:
            logger.warning(f"获取慢查询信息失败（可能未启用pg_stat_statements）: {str(e)}")
            return []


def optimize_database_indexes():
    """优化数据库索引的主函数"""
    from app.db.session import SessionLocal
    
    db = SessionLocal()
    optimizer = DatabaseIndexOptimizer()
    
    try:
        # 创建索引
        results = optimizer.create_all_indexes(db)
        
        # 分析表统计信息
        stats_result = optimizer.analyze_table_statistics(db)
        
        # 获取慢查询信息
        slow_queries = optimizer.get_slow_queries(db)
        
        return {
            "index_results": results,
            "statistics": stats_result,
            "slow_queries": slow_queries
        }
        
    finally:
        db.close()


if __name__ == "__main__":
    # 直接运行时执行优化
    result = optimize_database_indexes()
    print("数据库索引优化结果:")
    print(f"成功创建索引: {result['index_results']['success']}")
    print(f"创建失败索引: {result['index_results']['failed']}")
    print(f"统计信息分析: {result['statistics']['status']}")
    print(f"慢查询数量: {len(result['slow_queries'])}")
