from typing import Optional, List, Any
from datetime import date, datetime
from pydantic import BaseModel, Field, EmailStr, validator, constr


class EmployeeBase(BaseModel):
    """员工基础信息"""
    name: str = Field(..., example="张三", description="姓名")
    phone: str = Field(..., example="13812345678", description="手机号 (登录账号)")
    role: str = Field(..., example="技师", description="职位 (店长/技师/助理/收银)")
    photo_url: Optional[str] = Field(None, example="http://example.com/photo.jpg", description="员工照片")
    skills: Optional[List[str]] = Field(None, example=["足疗", "推拿"], description="技能标签")
    attachments: Optional[dict] = Field(None, description="附件 (如健康证、资格证URL)")
    default_commission_scheme_id: Optional[int] = Field(None, description="默认提成方案ID")
    hire_date: Optional[date] = Field(None, description="入职日期")
    status: str = Field("active", example="active", description="状态 (active/inactive/left)")


class EmployeeCreate(EmployeeBase):
    """创建员工"""
    store_id: int = Field(..., example=1, description="所属门店ID")
    password: str = Field(..., min_length=6, example="password123", description="密码")


class EmployeeUpdate(BaseModel):
    """更新员工信息"""
    name: Optional[str] = Field(None, description="姓名")
    phone: Optional[str] = Field(None, description="手机号")
    role: Optional[str] = Field(None, description="职位")
    photo_url: Optional[str] = Field(None, description="员工照片")
    skills: Optional[List[str]] = Field(None, description="技能标签")
    attachments: Optional[dict] = Field(None, description="附件")
    default_commission_scheme_id: Optional[int] = Field(None, description="默认提成方案ID")
    hire_date: Optional[date] = Field(None, description="入职日期")
    status: Optional[str] = Field(None, description="状态")
    password: Optional[str] = Field(None, description="密码")


class EmployeeInDBBase(EmployeeBase):
    """数据库中的员工信息"""
    id: int
    store_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Employee(EmployeeInDBBase):
    """API返回的员工信息"""
    pass


class EmployeeInDB(EmployeeInDBBase):
    """数据库中的员工信息(包含密码哈希)"""
    password_hash: str 