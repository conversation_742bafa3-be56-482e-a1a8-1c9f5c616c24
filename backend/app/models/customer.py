from sqlalchemy import <PERSON><PERSON><PERSON>, Column, ForeignKey, Integer, String, DateTime, Text, JSON, DECIMAL, Enum, Numeric
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime

from app.db.session import Base


class Customer(Base):
    """客户表模型"""
    __tablename__ = "customers"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    phone = Column(String, nullable=False, index=True)
    gender = Column(String, nullable=True)  # male, female, other
    birthday = Column(DateTime, nullable=True)
    avatar = Column(String, nullable=True)  # 头像URL
    address = Column(String, nullable=True)
    source = Column(String, nullable=True)  # 客户来源
    tags_json = Column(JSON, nullable=True, name="tags")  # 客户标签JSON
    remark = Column(Text, nullable=True)  # 备注

    # 会员相关字段
    member_level_id = Column(Integer, ForeignKey("member_levels.id"), nullable=True, index=True)
    current_points = Column(Integer, default=0)  # 当前积分
    total_recharge_amount = Column(Numeric(12, 2), default=0)  # 累计充值金额
    total_consumption_amount = Column(Numeric(12, 2), default=0)  # 累计消费金额
    level_upgrade_date = Column(DateTime, nullable=True)  # 等级升级日期

    # 兼容旧字段
    points = Column(Integer, default=0)  # 积分（旧字段，保留兼容）
    total_amount = Column(DECIMAL(10, 2), default=0)  # 累计消费金额（旧字段，保留兼容）

    last_visit_time = Column(DateTime, nullable=True)  # 最后到店时间
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    is_deleted = Column(Boolean, default=False)

    # 关系
    member_level = relationship("MemberLevel", back_populates="customers")
    orders = relationship("Order", back_populates="customer")
    cards = relationship("MemberCard", back_populates="customer")
    point_records = relationship("PointRecord", back_populates="customer")
    coupons = relationship("Coupon", back_populates="customer")
    customer_coupons = relationship("CustomerCoupon", back_populates="customer")
    appointments = relationship("Appointment", back_populates="customer")
    follow_ups = relationship("FollowUp", back_populates="customer")
    reviews = relationship("Review", back_populates="customer")

    # 客户画像相关关系
    tags = relationship("CustomerTag", secondary="customer_tag_association", back_populates="customers")
    segments = relationship("CustomerSegment", secondary="customer_segment_association", back_populates="customers")
    behaviors = relationship("CustomerBehavior", back_populates="customer")
    value_metric = relationship("CustomerValueMetric", uselist=False, back_populates="customer")
    preference = relationship("CustomerPreference", uselist=False, back_populates="customer")
    insights = relationship("CustomerInsight", back_populates="customer")

    def get_discount_rate(self):
        """获取客户折扣率"""
        if self.member_level:
            return float(self.member_level.discount_rate)
        return 1.0  # 无折扣

    def calculate_price(self, original_price):
        """计算客户折扣后价格"""
        discount_rate = self.get_discount_rate()
        return round(float(original_price) * discount_rate, 2)

    def can_upgrade_level(self, target_level):
        """检查客户是否可以升级到目标等级"""
        if not target_level:
            return False

        # 检查充值或消费是否达到目标等级要求
        return (target_level.can_upgrade_from_recharge(float(self.total_recharge_amount)) or
                target_level.can_upgrade_from_consumption(float(self.total_consumption_amount)))





class MemberCard(Base):
    """会员卡表模型"""
    __tablename__ = "member_cards"

    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    card_number = Column(String, unique=True, nullable=False)  # 卡号
    level_id = Column(Integer, ForeignKey("member_levels.id"), nullable=False)
    balance = Column(DECIMAL(10, 2), default=0)  # 余额
    points = Column(Integer, default=0)  # 积分
    status = Column(Enum("active", "inactive", "frozen", name="card_status"), default="active")
    expire_time = Column(DateTime, nullable=True)  # 过期时间
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    is_deleted = Column(Boolean, default=False)

    # 关系
    customer = relationship("Customer", back_populates="cards")
    level = relationship("MemberLevel")  # 移除back_populates，因为新MemberLevel模型没有cards关系
    transactions = relationship("CardTransaction", back_populates="card")


class CardTransaction(Base):
    """会员卡交易记录表模型"""
    __tablename__ = "card_transactions"

    id = Column(Integer, primary_key=True, index=True)
    card_id = Column(Integer, ForeignKey("member_cards.id"), nullable=False)
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=True)  # 关联订单，可为空（如充值）
    store_id = Column(Integer, ForeignKey("stores.id"), nullable=False)
    employee_id = Column(Integer, ForeignKey("employees.id"), nullable=False)  # 操作员工
    type = Column(Enum("recharge", "consume", "refund", "adjust", name="transaction_type"), nullable=False)
    amount = Column(DECIMAL(10, 2), nullable=False)  # 交易金额
    balance_before = Column(DECIMAL(10, 2), nullable=False)  # 交易前余额
    balance_after = Column(DECIMAL(10, 2), nullable=False)  # 交易后余额
    remark = Column(Text, nullable=True)  # 备注
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # 关系
    card = relationship("MemberCard", back_populates="transactions")
    store = relationship("Store", back_populates="card_transactions")
    employee = relationship("Employee", back_populates="card_transactions")
    order = relationship("Order", back_populates="card_transactions")


class PointRecord(Base):
    """积分记录表模型"""
    __tablename__ = "point_records"

    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=True)  # 关联订单，可为空（如活动赠送）
    store_id = Column(Integer, ForeignKey("stores.id"), nullable=False)
    type = Column(Enum("earn", "use", "expire", "adjust", name="point_record_type"), nullable=False)
    points = Column(Integer, nullable=False)  # 积分变动值
    points_before = Column(Integer, nullable=False)  # 变动前积分
    points_after = Column(Integer, nullable=False)  # 变动后积分
    expire_time = Column(DateTime, nullable=True)  # 过期时间
    remark = Column(Text, nullable=True)  # 备注
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # 关系
    customer = relationship("Customer", back_populates="point_records")
    store = relationship("Store", back_populates="point_records")
    order = relationship("Order", back_populates="point_records") 