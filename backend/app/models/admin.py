from sqlalchemy import <PERSON><PERSON><PERSON>, Column, ForeignKey, Integer, String, DateTime, Text, JSON, DECIMAL
from sqlalchemy.orm import relationship
from datetime import datetime
import pytz

from app.db.session import Base

# 北京时区
BEIJING_TZ = pytz.timezone('Asia/Shanghai')

def get_beijing_time():
    """获取当前北京时间"""
    return datetime.now(BEIJING_TZ)


class AdminRole(Base):
    """管理员角色表"""
    __tablename__ = "admin_roles"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, unique=True)  # 角色名称
    display_name = Column(String, nullable=False)  # 显示名称
    description = Column(Text, nullable=True)  # 角色描述
    permissions = Column(JSON, nullable=True)  # 权限列表
    is_system = Column(Boolean, default=False)  # 是否系统角色（不可删除）
    is_active = Column(Boolean, default=True)  # 是否启用
    created_at = Column(DateTime, default=get_beijing_time, nullable=False)
    updated_at = Column(DateTime, default=get_beijing_time, onupdate=get_beijing_time, nullable=False)
    is_deleted = Column(Boolean, default=False)

    # 关系
    admin_users = relationship("AdminUser", back_populates="role")


class AdminUser(Base):
    """超级管理员用户表"""
    __tablename__ = "admin_users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, nullable=False, unique=True)  # 用户名
    email = Column(String, nullable=False, unique=True)  # 邮箱
    phone = Column(String, nullable=True)  # 手机号
    password_hash = Column(String, nullable=False)  # 密码哈希
    real_name = Column(String, nullable=True)  # 真实姓名
    avatar = Column(String, nullable=True)  # 头像URL
    role_id = Column(Integer, ForeignKey("admin_roles.id"), nullable=False)  # 角色ID
    is_active = Column(Boolean, default=True)  # 是否启用
    is_super = Column(Boolean, default=False)  # 是否超级管理员
    last_login_at = Column(DateTime, nullable=True)  # 最后登录时间
    last_login_ip = Column(String, nullable=True)  # 最后登录IP
    login_count = Column(Integer, default=0)  # 登录次数
    created_at = Column(DateTime, default=get_beijing_time, nullable=False)
    updated_at = Column(DateTime, default=get_beijing_time, onupdate=get_beijing_time, nullable=False)
    is_deleted = Column(Boolean, default=False)

    # 关系
    role = relationship("AdminRole", back_populates="admin_users")
    operation_logs = relationship("AdminOperationLog", back_populates="admin_user")


class AdminOperationLog(Base):
    """管理员操作日志表"""
    __tablename__ = "admin_operation_logs"

    id = Column(Integer, primary_key=True, index=True)
    admin_user_id = Column(Integer, ForeignKey("admin_users.id"), nullable=False)  # 管理员ID
    operation = Column(String, nullable=False)  # 操作类型
    resource = Column(String, nullable=True)  # 操作资源
    resource_id = Column(String, nullable=True)  # 资源ID
    description = Column(Text, nullable=True)  # 操作描述
    request_data = Column(JSON, nullable=True)  # 请求数据
    response_data = Column(JSON, nullable=True)  # 响应数据
    ip_address = Column(String, nullable=True)  # IP地址
    user_agent = Column(Text, nullable=True)  # 用户代理
    status = Column(String, nullable=False)  # 操作状态 (success/failed)
    error_message = Column(Text, nullable=True)  # 错误信息
    created_at = Column(DateTime, default=get_beijing_time, nullable=False)

    # 关系
    admin_user = relationship("AdminUser", back_populates="operation_logs")


class SystemConfig(Base):
    """系统配置表"""
    __tablename__ = "system_configs"

    id = Column(Integer, primary_key=True, index=True)
    key = Column(String, nullable=False, unique=True)  # 配置键
    value = Column(Text, nullable=True)  # 配置值
    type = Column(String, nullable=False)  # 配置类型 (string/int/bool/json)
    category = Column(String, nullable=False)  # 配置分类
    name = Column(String, nullable=False)  # 配置名称
    description = Column(Text, nullable=True)  # 配置描述
    is_public = Column(Boolean, default=False)  # 是否公开（前端可访问）
    is_readonly = Column(Boolean, default=False)  # 是否只读
    created_at = Column(DateTime, default=get_beijing_time, nullable=False)
    updated_at = Column(DateTime, default=get_beijing_time, onupdate=get_beijing_time, nullable=False)


class SystemMonitor(Base):
    """系统监控表"""
    __tablename__ = "system_monitors"

    id = Column(Integer, primary_key=True, index=True)
    metric_name = Column(String, nullable=False)  # 指标名称
    metric_value = Column(DECIMAL(10, 2), nullable=False)  # 指标值
    metric_unit = Column(String, nullable=True)  # 指标单位
    category = Column(String, nullable=False)  # 分类 (system/database/application)
    description = Column(Text, nullable=True)  # 描述
    status = Column(String, nullable=False)  # 状态 (normal/warning/critical)
    threshold_warning = Column(DECIMAL(10, 2), nullable=True)  # 警告阈值
    threshold_critical = Column(DECIMAL(10, 2), nullable=True)  # 严重阈值
    created_at = Column(DateTime, default=get_beijing_time, nullable=False)


class DataBackup(Base):
    """数据备份记录表"""
    __tablename__ = "data_backups"

    id = Column(Integer, primary_key=True, index=True)
    backup_name = Column(String, nullable=False)  # 备份名称
    backup_type = Column(String, nullable=False)  # 备份类型 (full/incremental/manual)
    file_path = Column(String, nullable=False)  # 备份文件路径
    file_size = Column(Integer, nullable=True)  # 文件大小（字节）
    status = Column(String, nullable=False)  # 状态 (running/completed/failed)
    start_time = Column(DateTime, nullable=False)  # 开始时间
    end_time = Column(DateTime, nullable=True)  # 结束时间
    error_message = Column(Text, nullable=True)  # 错误信息
    created_by = Column(Integer, ForeignKey("admin_users.id"), nullable=True)  # 创建者
    created_at = Column(DateTime, default=get_beijing_time, nullable=False)


class BusinessPermission(Base):
    """业务权限表"""
    __tablename__ = "business_permissions"

    id = Column(Integer, primary_key=True, index=True)
    code = Column(String, nullable=False, unique=True)  # 权限代码
    name = Column(String, nullable=False)  # 权限名称
    description = Column(Text, nullable=True)  # 权限描述
    category = Column(String, nullable=False)  # 权限分类
    resource = Column(String, nullable=True)  # 资源类型
    action = Column(String, nullable=True)  # 操作类型
    is_system = Column(Boolean, default=False)  # 是否系统权限（不可删除）
    is_active = Column(Boolean, default=True)  # 是否启用
    sort_order = Column(Integer, default=0)  # 排序
    created_at = Column(DateTime, default=get_beijing_time, nullable=False)
    updated_at = Column(DateTime, default=get_beijing_time, onupdate=get_beijing_time, nullable=False)
    is_deleted = Column(Boolean, default=False)

    # 关系
    role_permissions = relationship("BusinessRolePermission", back_populates="permission")


class BusinessRole(Base):
    """业务角色表"""
    __tablename__ = "business_roles"

    id = Column(Integer, primary_key=True, index=True)
    code = Column(String, nullable=False, unique=True)  # 角色代码
    name = Column(String, nullable=False)  # 角色名称
    description = Column(Text, nullable=True)  # 角色描述
    level = Column(Integer, default=1)  # 角色级别 (1-基础, 2-中级, 3-高级)
    is_system = Column(Boolean, default=False)  # 是否系统角色（不可删除）
    is_active = Column(Boolean, default=True)  # 是否启用
    sort_order = Column(Integer, default=0)  # 排序
    created_at = Column(DateTime, default=get_beijing_time, nullable=False)
    updated_at = Column(DateTime, default=get_beijing_time, onupdate=get_beijing_time, nullable=False)
    is_deleted = Column(Boolean, default=False)

    # 关系
    role_permissions = relationship("BusinessRolePermission", back_populates="role")
    employees = relationship("Employee", back_populates="business_role")


class BusinessRolePermission(Base):
    """业务角色权限关联表"""
    __tablename__ = "business_role_permissions"

    id = Column(Integer, primary_key=True, index=True)
    role_id = Column(Integer, ForeignKey("business_roles.id"), nullable=False)  # 角色ID
    permission_id = Column(Integer, ForeignKey("business_permissions.id"), nullable=False)  # 权限ID
    granted_by = Column(Integer, ForeignKey("admin_users.id"), nullable=True)  # 授权人
    granted_at = Column(DateTime, default=datetime.utcnow, nullable=False)  # 授权时间
    is_active = Column(Boolean, default=True)  # 是否启用

    # 关系
    role = relationship("BusinessRole", back_populates="role_permissions")
    permission = relationship("BusinessPermission", back_populates="role_permissions")
    granted_by_user = relationship("AdminUser")
