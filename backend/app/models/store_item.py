from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, DECIMAL
from sqlalchemy.orm import relationship
from datetime import datetime

from app.db.session import Base


class StoreItem(Base):
    """门店项目表"""
    __tablename__ = "store_items"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    store_id = Column(Integer, ForeignKey("stores.id"), nullable=False, comment="门店ID")
    group_item_id = Column(Integer, ForeignKey("group_items.id"), nullable=False, comment="集团项目ID")
    store_price = Column(DECIMAL(10, 2), nullable=False, comment="本店原价（非会员价格）")
    member_price = Column(DECIMAL(10, 2), nullable=True, comment="本店会员价（可选）")
    is_enabled = Column(Boolean, nullable=False, default=True, comment="是否启用")
    commission_scheme_id = Column(Integer, ForeignKey("commission_schemes.id"), nullable=True, comment="本店专属提成方案ID")
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 关系
    store = relationship("Store", back_populates="store_items")
    group_item = relationship("GroupItem", back_populates="store_items")
    commission_scheme = relationship("CommissionScheme")
    order_items = relationship("OrderItem", back_populates="store_item")
    appointments = relationship("Appointment", back_populates="store_item")