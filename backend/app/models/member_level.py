from sqlalchemy import Column, Integer, String, Numeric, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base_class import Base


class MemberLevel(Base):
    """会员等级模型"""
    __tablename__ = "member_levels"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False, comment="等级名称")
    level_order = Column(Integer, nullable=False, index=True, comment="等级顺序，数字越大等级越高")
    min_recharge_amount = Column(Numeric(10, 2), default=0, comment="最低充值金额")
    min_consumption_amount = Column(Numeric(10, 2), default=0, comment="最低消费金额")
    discount_rate = Column(Numeric(5, 4), default=1.0000, comment="折扣率，1.0为无折扣，0.9为9折")
    points_multiplier = Column(Numeric(5, 2), default=1.00, comment="积分倍率")
    description = Column(Text, comment="等级描述")
    benefits = Column(JSONB, comment="等级权益JSON配置")
    is_active = Column(Boolean, default=True, index=True, comment="是否启用")
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关联关系
    customers = relationship("Customer", back_populates="member_level")
    benefits_detail = relationship("MemberLevelBenefit", back_populates="level", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<MemberLevel(id={self.id}, name='{self.name}', level_order={self.level_order})>"

    @property
    def discount_percentage(self):
        """获取折扣百分比（用于显示）"""
        return round((1 - float(self.discount_rate)) * 100, 1)

    def can_upgrade_from_recharge(self, total_recharge: float) -> bool:
        """检查是否可以通过充值金额升级到此等级"""
        return total_recharge >= float(self.min_recharge_amount)

    def can_upgrade_from_consumption(self, total_consumption: float) -> bool:
        """检查是否可以通过消费金额升级到此等级"""
        return total_consumption >= float(self.min_consumption_amount)

    def calculate_points(self, base_points: int) -> int:
        """计算积分（应用倍率）"""
        return int(base_points * float(self.points_multiplier))


class MemberLevelBenefit(Base):
    """会员等级权益模型"""
    __tablename__ = "member_level_benefits"

    id = Column(Integer, primary_key=True, index=True)
    level_id = Column(Integer, ForeignKey("member_levels.id", ondelete="CASCADE"), nullable=False, index=True)
    benefit_type = Column(String(50), nullable=False, index=True, comment="权益类型")
    benefit_value = Column(JSONB, nullable=False, comment="权益值JSON配置")
    description = Column(Text, comment="权益描述")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")

    # 关联关系
    level = relationship("MemberLevel", back_populates="benefits_detail")

    def __repr__(self):
        return f"<MemberLevelBenefit(id={self.id}, level_id={self.level_id}, type='{self.benefit_type}')>"

    @property
    def benefit_display(self):
        """获取权益显示文本"""
        if self.benefit_type == "discount":
            value = self.benefit_value.get("value", 0)
            if self.benefit_value.get("type") == "percentage":
                return f"{value * 100:.1f}%折扣"
            else:
                return f"减免{value}元"
        elif self.benefit_type == "points":
            multiplier = self.benefit_value.get("multiplier", 1)
            return f"积分{multiplier}倍"
        elif self.benefit_type == "free_service":
            count = self.benefit_value.get("count", 0)
            period = self.benefit_value.get("type", "monthly")
            period_text = {"monthly": "每月", "yearly": "每年", "once": "一次性"}.get(period, period)
            return f"{period_text}{count}次免费服务"
        elif self.benefit_type == "priority_booking":
            return "优先预约" if self.benefit_value.get("enabled") else ""
        elif self.benefit_type == "exclusive_service":
            return "专属服务" if self.benefit_value.get("enabled") else ""
        else:
            return self.description or self.benefit_type
