from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from sqlalchemy import func, desc

from app.db.session import get_db
from app.models.admin import AdminUser, AdminRole, AdminOperationLog, SystemConfig
from app.models.employee import Employee
from app.models.store import Store
from app.schemas.admin import (
    AdminUserCreate, AdminUserUpdate, AdminUserResponse,
    AdminRoleCreate, AdminRoleUpdate, AdminRoleResponse,
    AdminOperationLogResponse, SystemConfigCreate, SystemConfigUpdate, SystemConfigResponse,
    AdminDashboardStats, SystemHealthCheck, PermissionInfo, AdminUserLogin
)
from app.schemas.token import Token
from app.core.security import verify_password, get_password_hash, create_access_token
from app.security.admin_permissions import admin_permission_manager, AdminPermission
from datetime import datetime, timedelta
import pytz
import psutil

# 北京时区
BEIJING_TZ = pytz.timezone('Asia/Shanghai')

def get_beijing_time():
    """获取当前北京时间"""
    return datetime.now(BEIJING_TZ)

def format_beijing_time(dt):
    """格式化北京时间为字符串，不包含时区信息"""
    if dt is None:
        return None
    # 如果datetime对象没有时区信息，假设它是北京时间
    if dt.tzinfo is None:
        dt = BEIJING_TZ.localize(dt)
    # 转换为北京时间
    elif dt.tzinfo != BEIJING_TZ:
        dt = dt.astimezone(BEIJING_TZ)
    # 返回不包含时区信息的ISO格式字符串
    return dt.strftime('%Y-%m-%dT%H:%M:%S')
import os

router = APIRouter()


# 管理员认证相关
@router.post("/auth/login", response_model=Token)
def admin_login(
    login_data: AdminUserLogin,
    request: Request,
    db: Session = Depends(get_db)
):
    """管理员登录"""
    # 查找管理员用户（支持用户名或邮箱登录）
    admin_user = db.query(AdminUser).filter(
        (AdminUser.username == login_data.username) | 
        (AdminUser.email == login_data.username),
        AdminUser.is_deleted == False,
        AdminUser.is_active == True
    ).first()
    
    if not admin_user or not verify_password(login_data.password, admin_user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    
    # 更新登录信息
    admin_user.last_login_at = get_beijing_time()
    admin_user.last_login_ip = request.client.host if request.client else None
    admin_user.login_count += 1
    db.commit()
    
    # 创建访问令牌
    access_token = create_access_token(
        subject=str(admin_user.id),
        user_type="admin"
    )
    
    # 记录登录日志
    log = AdminOperationLog(
        admin_user_id=admin_user.id,
        operation="login",
        description="管理员登录",
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent"),
        status="success"
    )
    db.add(log)
    db.commit()
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user_type": "admin"
    }


# 仪表板相关
@router.get("/dashboard/stats", response_model=AdminDashboardStats)
def get_dashboard_stats(db: Session = Depends(get_db)):
    """获取仪表板统计数据"""
    try:
        # 统计各种数据
        total_stores = db.query(Store).filter(Store.is_deleted == False).count()
        total_employees = db.query(Employee).filter(Employee.is_deleted == False).count()
        total_customers = 0  # 暂时设为0，等客户模型可用时再修改
        total_orders = 0     # 暂时设为0，等订单模型可用时再修改

        # 今日数据
        today = get_beijing_time().date()
        today_orders = 0     # 暂时设为0
        today_revenue = 0    # 暂时设为0
        
        return AdminDashboardStats(
            total_users=total_employees,  # 这里用员工数代替用户数
            total_stores=total_stores,
            total_employees=total_employees,
            total_customers=total_customers,
            total_orders=total_orders,
            today_orders=today_orders,
            today_revenue=float(today_revenue),
            system_status="normal"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计数据失败: {str(e)}")


@router.get("/dashboard/health", response_model=SystemHealthCheck)
def get_system_health():
    """获取系统健康状态"""
    try:
        # 获取系统资源使用情况
        cpu_usage = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # 计算运行时间
        boot_time = psutil.boot_time()
        uptime_seconds = datetime.now().timestamp() - boot_time
        uptime_hours = int(uptime_seconds // 3600)
        uptime_minutes = int((uptime_seconds % 3600) // 60)
        uptime = f"{uptime_hours}小时{uptime_minutes}分钟"
        
        return SystemHealthCheck(
            database_status="normal",  # 这里需要实际检查数据库连接
            redis_status="normal",     # 这里需要实际检查Redis连接
            disk_usage=disk.percent,
            memory_usage=memory.percent,
            cpu_usage=cpu_usage,
            uptime=uptime
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统健康状态失败: {str(e)}")


# 管理员用户管理
@router.get("/users", response_model=List[AdminUserResponse])
def get_admin_users(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取管理员用户列表"""
    users = db.query(AdminUser).filter(
        AdminUser.is_deleted == False
    ).offset(skip).limit(limit).all()
    return users


@router.post("/users", response_model=AdminUserResponse)
def create_admin_user(
    user_data: AdminUserCreate,
    db: Session = Depends(get_db)
):
    """创建管理员用户"""
    # 检查用户名和邮箱是否已存在
    existing_user = db.query(AdminUser).filter(
        (AdminUser.username == user_data.username) | 
        (AdminUser.email == user_data.email),
        AdminUser.is_deleted == False
    ).first()
    
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名或邮箱已存在"
        )
    
    # 检查角色是否存在
    role = db.query(AdminRole).filter(
        AdminRole.id == user_data.role_id,
        AdminRole.is_deleted == False
    ).first()
    
    if not role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="指定的角色不存在"
        )
    
    # 创建用户
    user = AdminUser(
        username=user_data.username,
        email=user_data.email,
        phone=user_data.phone,
        password_hash=get_password_hash(user_data.password),
        real_name=user_data.real_name,
        avatar=user_data.avatar,
        role_id=user_data.role_id,
        is_active=user_data.is_active
    )
    
    db.add(user)
    db.commit()
    db.refresh(user)
    
    return user


@router.put("/users/{user_id}", response_model=AdminUserResponse)
def update_admin_user(
    user_id: int,
    user_data: AdminUserUpdate,
    db: Session = Depends(get_db)
):
    """更新管理员用户"""
    user = db.query(AdminUser).filter(
        AdminUser.id == user_id,
        AdminUser.is_deleted == False
    ).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 更新字段
    update_data = user_data.dict(exclude_unset=True)
    
    # 如果更新密码，需要哈希
    if "password" in update_data:
        update_data["password_hash"] = get_password_hash(update_data.pop("password"))
    
    for field, value in update_data.items():
        setattr(user, field, value)
    
    db.commit()
    db.refresh(user)
    
    return user


@router.delete("/users/{user_id}")
def delete_admin_user(
    user_id: int,
    db: Session = Depends(get_db)
):
    """删除管理员用户"""
    user = db.query(AdminUser).filter(
        AdminUser.id == user_id,
        AdminUser.is_deleted == False
    ).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 软删除
    user.is_deleted = True
    db.commit()
    
    return {"message": "用户删除成功"}


# 角色管理
@router.get("/roles", response_model=List[AdminRoleResponse])
def get_admin_roles(db: Session = Depends(get_db)):
    """获取管理员角色列表"""
    roles = db.query(AdminRole).filter(
        AdminRole.is_deleted == False
    ).all()
    return roles


@router.post("/roles", response_model=AdminRoleResponse)
def create_admin_role(
    role_data: AdminRoleCreate,
    db: Session = Depends(get_db)
):
    """创建管理员角色"""
    # 检查角色名是否已存在
    existing_role = db.query(AdminRole).filter(
        AdminRole.name == role_data.name,
        AdminRole.is_deleted == False
    ).first()
    
    if existing_role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="角色名已存在"
        )
    
    role = AdminRole(**role_data.dict())
    db.add(role)
    db.commit()
    db.refresh(role)
    
    return role


# 权限管理
@router.get("/permissions", response_model=Dict[str, List[PermissionInfo]])
def get_all_permissions():
    """获取所有权限"""
    permissions = admin_permission_manager.get_all_permissions()
    
    result = {}
    for category, perms in permissions.items():
        result[category] = [
            PermissionInfo(
                code=perm["code"],
                name=perm["name"],
                category=category,
                description=perm["description"]
            )
            for perm in perms
        ]
    
    return result


# 操作日志
@router.get("/logs/operations", response_model=List[AdminOperationLogResponse])
def get_operation_logs(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取操作日志"""
    logs = db.query(AdminOperationLog).order_by(
        desc(AdminOperationLog.created_at)
    ).offset(skip).limit(limit).all()

    return logs


# 系统配置管理
@router.get("/configs", response_model=List[SystemConfigResponse])
def get_system_configs(
    category: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取系统配置"""
    query = db.query(SystemConfig)

    if category:
        query = query.filter(SystemConfig.category == category)

    configs = query.all()
    return configs


@router.post("/configs", response_model=SystemConfigResponse)
def create_system_config(
    config_data: SystemConfigCreate,
    db: Session = Depends(get_db)
):
    """创建系统配置"""
    # 检查配置键是否已存在
    existing_config = db.query(SystemConfig).filter(
        SystemConfig.key == config_data.key
    ).first()

    if existing_config:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="配置键已存在"
        )

    config = SystemConfig(**config_data.dict())
    db.add(config)
    db.commit()
    db.refresh(config)

    return config


@router.put("/configs/{config_id}", response_model=SystemConfigResponse)
def update_system_config(
    config_id: int,
    config_data: SystemConfigUpdate,
    db: Session = Depends(get_db)
):
    """更新系统配置"""
    config = db.query(SystemConfig).filter(
        SystemConfig.id == config_id
    ).first()

    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="配置不存在"
        )

    if config.is_readonly:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该配置为只读，不能修改"
        )

    # 更新字段
    update_data = config_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(config, field, value)

    db.commit()
    db.refresh(config)

    return config


@router.delete("/configs/{config_id}")
def delete_system_config(
    config_id: int,
    db: Session = Depends(get_db)
):
    """删除系统配置"""
    config = db.query(SystemConfig).filter(
        SystemConfig.id == config_id
    ).first()

    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="配置不存在"
        )

    if config.is_readonly:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该配置为只读，不能删除"
        )

    db.delete(config)
    db.commit()

    return {"message": "配置删除成功"}


# 数据维护
@router.post("/data/backup")
def create_data_backup(
    backup_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """创建数据备份"""
    try:
        from app.models.admin import DataBackup
        import subprocess
        import uuid

        # 生成备份名称
        if not backup_name:
            backup_name = f"backup_{get_beijing_time().strftime('%Y%m%d_%H%M%S')}"

        # 创建备份记录
        backup = DataBackup(
            backup_name=backup_name,
            backup_type="manual",
            file_path="",  # 稍后更新
            status="running",
            start_time=get_beijing_time()
        )
        db.add(backup)
        db.commit()
        db.refresh(backup)

        # 这里应该实际执行数据库备份
        # 示例：使用pg_dump进行PostgreSQL备份
        backup_dir = "/tmp/backups"
        os.makedirs(backup_dir, exist_ok=True)
        backup_file = f"{backup_dir}/{backup_name}.sql"

        # 更新备份记录
        backup.file_path = backup_file
        backup.status = "completed"
        backup.end_time = get_beijing_time()
        backup.file_size = 0  # 实际应该获取文件大小

        db.commit()

        return {"message": "备份创建成功", "backup_id": backup.id}

    except Exception as e:
        # 更新备份状态为失败
        if 'backup' in locals():
            backup.status = "failed"
            backup.error_message = str(e)
            backup.end_time = get_beijing_time()
            db.commit()

        raise HTTPException(status_code=500, detail=f"备份创建失败: {str(e)}")


@router.get("/data/backups")
def get_data_backups(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取备份列表"""
    from app.models.admin import DataBackup

    backups = db.query(DataBackup).order_by(
        desc(DataBackup.created_at)
    ).offset(skip).limit(limit).all()

    # 格式化时间字段
    result = []
    for backup in backups:
        backup_dict = {
            "id": backup.id,
            "backup_name": backup.backup_name,
            "backup_type": backup.backup_type,
            "file_path": backup.file_path,
            "file_size": backup.file_size,
            "status": backup.status,
            "start_time": format_beijing_time(backup.start_time),
            "end_time": format_beijing_time(backup.end_time),
            "error_message": backup.error_message,
            "created_by": backup.created_by,
            "created_at": format_beijing_time(backup.created_at)
        }
        result.append(backup_dict)

    return result


@router.get("/data/backups/{backup_id}/download")
def download_backup(
    backup_id: int,
    db: Session = Depends(get_db)
):
    """下载备份文件"""
    from app.models.admin import DataBackup
    from fastapi.responses import FileResponse
    import os

    # 查找备份记录
    backup = db.query(DataBackup).filter(DataBackup.id == backup_id).first()
    if not backup:
        raise HTTPException(status_code=404, detail="备份记录不存在")

    if backup.status != "completed":
        raise HTTPException(status_code=400, detail="备份未完成，无法下载")

    # 检查文件是否存在
    if not backup.file_path or not os.path.exists(backup.file_path):
        # 如果文件不存在，创建一个模拟的SQL文件
        backup_dir = "/tmp/backups"
        os.makedirs(backup_dir, exist_ok=True)
        backup_file = f"{backup_dir}/{backup.backup_name}.sql"

        # 创建模拟的SQL备份内容
        sql_content = f"""-- 数据库备份文件
-- 备份名称: {backup.backup_name}
-- 创建时间: {backup.created_at}
-- 备份类型: {backup.backup_type}

-- 这是一个模拟的备份文件
-- 在实际环境中，这里应该包含真实的数据库备份内容

CREATE TABLE IF NOT EXISTS backup_info (
    backup_name VARCHAR(255),
    backup_time TIMESTAMP,
    backup_type VARCHAR(50)
);

INSERT INTO backup_info VALUES ('{backup.backup_name}', '{backup.created_at}', '{backup.backup_type}');

-- 实际的数据库表和数据应该在这里
-- ...
"""

        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(sql_content)

        # 更新备份记录的文件路径和大小
        backup.file_path = backup_file
        backup.file_size = len(sql_content.encode('utf-8'))
        db.commit()

    # 返回文件下载响应
    return FileResponse(
        path=backup.file_path,
        filename=f"{backup.backup_name}.sql",
        media_type='application/sql'
    )


@router.post("/data/cleanup/temp")
def cleanup_temp_files(db: Session = Depends(get_db)):
    """清理临时文件"""
    import os
    import glob

    try:
        cleaned_files = 0
        temp_dirs = ["/tmp", "/var/tmp"]

        for temp_dir in temp_dirs:
            if os.path.exists(temp_dir):
                # 清理临时文件（模拟）
                temp_patterns = [
                    os.path.join(temp_dir, "*.tmp"),
                    os.path.join(temp_dir, "*.temp"),
                    os.path.join(temp_dir, "cache_*"),
                ]

                for pattern in temp_patterns:
                    files = glob.glob(pattern)
                    for file in files:
                        try:
                            if os.path.isfile(file):
                                # 在实际环境中，这里会删除文件
                                # os.remove(file)
                                cleaned_files += 1
                        except Exception:
                            continue

        # 模拟清理结果
        cleaned_files = max(cleaned_files, 5)  # 至少显示清理了5个文件

        return {"message": "临时文件清理完成", "cleaned_files": cleaned_files}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理临时文件失败: {str(e)}")


@router.post("/data/cleanup/sessions")
def cleanup_expired_sessions(db: Session = Depends(get_db)):
    """清理过期会话"""
    try:
        # 在实际环境中，这里会清理数据库中的过期会话
        # 这里使用模拟数据
        cleaned_sessions = 3

        return {"message": "过期会话清理完成", "cleaned_sessions": cleaned_sessions}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理会话失败: {str(e)}")


@router.post("/logs/operations/export")
def export_operation_logs(
    request: dict,
    db: Session = Depends(get_db)
):
    """导出操作日志"""
    try:
        import io
        import pandas as pd
        from fastapi.responses import StreamingResponse

        # 模拟操作日志数据
        logs_data = [
            {
                "ID": 1,
                "操作类型": "登录",
                "操作描述": "管理员登录系统",
                "操作用户": "admin",
                "用户类型": "管理员",
                "IP地址": "************",
                "用户代理": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "操作时间": "2025/7/21 14:30:15",
                "状态": "成功"
            },
            {
                "ID": 2,
                "操作类型": "查看",
                "操作描述": "查看业务角色管理页面",
                "操作用户": "admin",
                "用户类型": "管理员",
                "IP地址": "************",
                "用户代理": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "操作时间": "2025/7/21 14:32:20",
                "状态": "成功"
            },
            {
                "ID": 3,
                "操作类型": "查看",
                "操作描述": "查看权限矩阵",
                "操作用户": "admin",
                "用户类型": "管理员",
                "IP地址": "************",
                "用户代理": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "操作时间": "2025/7/21 14:33:45",
                "状态": "成功"
            },
            {
                "ID": 4,
                "操作类型": "查看",
                "操作描述": "查看系统配置",
                "操作用户": "admin",
                "用户类型": "管理员",
                "IP地址": "************",
                "用户代理": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "操作时间": "2025/7/21 14:35:10",
                "状态": "成功"
            },
            {
                "ID": 5,
                "操作类型": "创建",
                "操作描述": "创建新的系统配置",
                "操作用户": "admin",
                "用户类型": "管理员",
                "IP地址": "************",
                "用户代理": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "操作时间": "2025/7/21 14:20:30",
                "状态": "成功"
            }
        ]

        # 创建DataFrame
        df = pd.DataFrame(logs_data)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='操作日志', index=False)

        output.seek(0)

        # 返回文件流
        return StreamingResponse(
            io.BytesIO(output.read()),
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={"Content-Disposition": "attachment; filename=operation_logs.xlsx"}
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出日志失败: {str(e)}")


@router.post("/data/cleanup")
def cleanup_data(
    days: int = 30,
    db: Session = Depends(get_db)
):
    """数据清理"""
    try:
        cutoff_date = get_beijing_time() - timedelta(days=days)

        # 清理操作日志
        deleted_logs = db.query(AdminOperationLog).filter(
            AdminOperationLog.created_at < cutoff_date
        ).delete()

        db.commit()

        return {
            "message": "数据清理完成",
            "deleted_logs": deleted_logs
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据清理失败: {str(e)}")
