from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from datetime import datetime

from app import schemas, models
from app.api import deps
from app.schemas.marketing import (
    MarketingActivityCreate, MarketingActivityUpdate, MarketingActivityInDB,
    MarketingActivityWithRelations, ActivityStatus
)
from app.models.marketing import MarketingActivity, activity_store, activity_item

router = APIRouter()


@router.post("/", response_model=MarketingActivityInDB)
def create_marketing_activity(
    *,
    db: Session = Depends(deps.get_db),
    activity_in: MarketingActivityCreate,
    current_employee: models.Employee = Depends(deps.get_current_employee)
) -> Any:
    """
    创建营销活动
    """
    # 检查门店是否存在
    for store_id in activity_in.store_ids:
        store = db.query(models.Store).filter(models.Store.id == store_id).first()
        if not store:
            raise HTTPException(status_code=404, detail=f"门店ID {store_id} 不存在")
    
    # 检查项目是否存在
    for item_id in activity_in.item_ids:
        item = db.query(models.GroupItem).filter(models.GroupItem.id == item_id).first()
        if not item:
            raise HTTPException(status_code=404, detail=f"项目ID {item_id} 不存在")
    
    # 检查优惠券模板是否存在
    if activity_in.coupon_template_id:
        coupon_template = db.query(models.CouponTemplate).filter(
            models.CouponTemplate.id == activity_in.coupon_template_id
        ).first()
        if not coupon_template:
            raise HTTPException(status_code=404, detail="优惠券模板不存在")
    
    # 创建活动
    activity_data = activity_in.dict(exclude={"store_ids", "item_ids"})
    activity = MarketingActivity(**activity_data)
    
    # 设置初始状态
    now = datetime.now()
    if activity.start_time <= now < activity.end_time:
        activity.status = ActivityStatus.ACTIVE
    elif activity.start_time > now:
        activity.status = ActivityStatus.SCHEDULED
    else:
        activity.status = ActivityStatus.ENDED
    
    db.add(activity)
    db.flush()  # 获取活动ID
    
    # 添加门店关联
    for store_id in activity_in.store_ids:
        db.execute(activity_store.insert().values(activity_id=activity.id, store_id=store_id))
    
    # 添加项目关联
    for item_id in activity_in.item_ids:
        db.execute(activity_item.insert().values(activity_id=activity.id, item_id=item_id))
    
    db.commit()
    db.refresh(activity)
    return activity


@router.get("/", response_model=List[MarketingActivityInDB])
def get_marketing_activities(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    status: Optional[ActivityStatus] = None,
    store_id: Optional[int] = None,
    current_employee: models.Employee = Depends(deps.get_current_employee)
) -> Any:
    """
    获取营销活动列表，支持按状态和门店筛选
    """
    query = db.query(MarketingActivity)
    
    if status:
        query = query.filter(MarketingActivity.status == status)
    
    if store_id:
        query = query.join(activity_store).filter(activity_store.c.store_id == store_id)
    
    activities = query.order_by(MarketingActivity.created_at.desc()).offset(skip).limit(limit).all()
    return activities


@router.get("/active", response_model=List[MarketingActivityWithRelations])
def get_active_marketing_activities(
    *,
    db: Session = Depends(deps.get_db),
    store_id: Optional[int] = None,
    current_employee: models.Employee = Depends(deps.get_current_employee)
) -> Any:
    """
    获取当前进行中的营销活动
    """
    now = datetime.now()
    query = db.query(MarketingActivity).filter(
        MarketingActivity.status == ActivityStatus.ACTIVE,
        MarketingActivity.start_time <= now,
        MarketingActivity.end_time > now
    )
    
    if store_id:
        query = query.join(activity_store).filter(activity_store.c.store_id == store_id)
    
    activities = query.all()
    
    # 加载关联数据
    result = []
    for activity in activities:
        stores = db.query(models.Store).join(
            activity_store, activity_store.c.store_id == models.Store.id
        ).filter(activity_store.c.activity_id == activity.id).all()
        
        items = db.query(models.GroupItem).join(
            activity_item, activity_item.c.item_id == models.GroupItem.id
        ).filter(activity_item.c.activity_id == activity.id).all()
        
        coupon_template = None
        if activity.coupon_template_id:
            coupon_template = db.query(models.CouponTemplate).filter(
                models.CouponTemplate.id == activity.coupon_template_id
            ).first()
        
        activity_dict = {
            **activity.__dict__,
            "stores": [{"id": store.id, "name": store.name} for store in stores],
            "items": [{"id": item.id, "name": item.name} for item in items],
            "coupon_template": coupon_template
        }
        result.append(activity_dict)
    
    return result


@router.get("/{activity_id}", response_model=MarketingActivityWithRelations)
def get_marketing_activity(
    *,
    db: Session = Depends(deps.get_db),
    activity_id: int = Path(..., gt=0, description="activity_id"),
    current_employee: models.Employee = Depends(deps.get_current_employee)
) -> Any:
    """
    获取营销活动详情
    """
    activity = db.query(MarketingActivity).filter(MarketingActivity.id == activity_id).first()
    if not activity:
        raise HTTPException(status_code=404, detail="营销活动不存在")
    
    # 加载关联数据
    stores = db.query(models.Store).join(
        activity_store, activity_store.c.store_id == models.Store.id
    ).filter(activity_store.c.activity_id == activity.id).all()
    
    items = db.query(models.GroupItem).join(
        activity_item, activity_item.c.item_id == models.GroupItem.id
    ).filter(activity_item.c.activity_id == activity.id).all()
    
    coupon_template = None
    if activity.coupon_template_id:
        coupon_template = db.query(models.CouponTemplate).filter(
            models.CouponTemplate.id == activity.coupon_template_id
        ).first()
    
    result = {
        **activity.__dict__,
        "stores": [{"id": store.id, "name": store.name} for store in stores],
        "items": [{"id": item.id, "name": item.name} for item in items],
        "coupon_template": coupon_template
    }
    
    return result


@router.put("/{activity_id}", response_model=MarketingActivityInDB)
def update_marketing_activity(
    *,
    db: Session = Depends(deps.get_db),
    activity_id: int = Path(..., gt=0, description="activity_id"),
    activity_in: MarketingActivityUpdate,
    current_employee: models.Employee = Depends(deps.get_current_employee)
) -> Any:
    """
    更新营销活动
    """
    activity = db.query(MarketingActivity).filter(MarketingActivity.id == activity_id).first()
    if not activity:
        raise HTTPException(status_code=404, detail="营销活动不存在")
    
    # 检查优惠券模板是否存在
    if activity_in.coupon_template_id:
        coupon_template = db.query(models.CouponTemplate).filter(
            models.CouponTemplate.id == activity_in.coupon_template_id
        ).first()
        if not coupon_template:
            raise HTTPException(status_code=404, detail="优惠券模板不存在")
    
    # 更新活动基本信息
    update_data = activity_in.dict(exclude={"store_ids", "item_ids"}, exclude_unset=True)
    for field, value in update_data.items():
        setattr(activity, field, value)
    
    # 更新活动状态
    if activity_in.status is None:  # 如果没有明确设置状态，根据时间自动更新
        now = datetime.now()
        if activity.start_time <= now < activity.end_time:
            activity.status = ActivityStatus.ACTIVE
        elif activity.start_time > now:
            activity.status = ActivityStatus.SCHEDULED
        else:
            activity.status = ActivityStatus.ENDED
    
    # 更新门店关联
    if activity_in.store_ids is not None:
        # 删除现有关联
        db.execute(activity_store.delete().where(activity_store.c.activity_id == activity.id))
        # 添加新关联
        for store_id in activity_in.store_ids:
            store = db.query(models.Store).filter(models.Store.id == store_id).first()
            if not store:
                raise HTTPException(status_code=404, detail=f"门店ID {store_id} 不存在")
            db.execute(activity_store.insert().values(activity_id=activity.id, store_id=store_id))
    
    # 更新项目关联
    if activity_in.item_ids is not None:
        # 删除现有关联
        db.execute(activity_item.delete().where(activity_item.c.activity_id == activity.id))
        # 添加新关联
        for item_id in activity_in.item_ids:
            item = db.query(models.GroupItem).filter(models.GroupItem.id == item_id).first()
            if not item:
                raise HTTPException(status_code=404, detail=f"项目ID {item_id} 不存在")
            db.execute(activity_item.insert().values(activity_id=activity.id, item_id=item_id))
    
    db.add(activity)
    db.commit()
    db.refresh(activity)
    return activity


@router.delete("/{activity_id}", response_model=MarketingActivityInDB)
def delete_marketing_activity(
    *,
    db: Session = Depends(deps.get_db),
    activity_id: int = Path(..., gt=0, description="activity_id"),
    current_employee: models.Employee = Depends(deps.get_current_employee)
) -> Any:
    """
    删除营销活动
    """
    activity = db.query(MarketingActivity).filter(MarketingActivity.id == activity_id).first()
    if not activity:
        raise HTTPException(status_code=404, detail="营销活动不存在")
    
    # 如果活动正在进行中，不允许删除
    if activity.status == ActivityStatus.ACTIVE:
        raise HTTPException(status_code=400, detail="活动正在进行中，无法删除")
    
    # 删除关联数据
    db.execute(activity_store.delete().where(activity_store.c.activity_id == activity.id))
    db.execute(activity_item.delete().where(activity_item.c.activity_id == activity.id))
    
    db.delete(activity)
    db.commit()
    return activity


@router.post("/{activity_id}/cancel", response_model=MarketingActivityInDB)
def cancel_marketing_activity(
    *,
    db: Session = Depends(deps.get_db),
    activity_id: int = Path(..., gt=0, description="activity_id"),
    current_employee: models.Employee = Depends(deps.get_current_employee)
) -> Any:
    """
    取消营销活动
    """
    activity = db.query(MarketingActivity).filter(MarketingActivity.id == activity_id).first()
    if not activity:
        raise HTTPException(status_code=404, detail="营销活动不存在")
    
    activity.status = ActivityStatus.CANCELLED
    db.add(activity)
    db.commit()
    db.refresh(activity)
    return activity 