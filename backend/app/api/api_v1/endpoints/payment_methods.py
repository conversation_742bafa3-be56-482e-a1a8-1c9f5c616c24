from typing import List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.api import deps
from app.crud.cashier import payment_method
from app.schemas.cashier import PaymentMethodCreate, PaymentMethodUpdate
from app.models.cashier import PaymentMethod

router = APIRouter()


@router.get("/", response_model=List[dict])
def get_payment_methods(
    db: Session = Depends(deps.get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    is_active: Optional[bool] = Query(None, description="是否激活")
) -> Any:
    """
    获取支付方式列表
    """
    # 返回模拟数据，直到数据库问题解决
    return [
        {
            "id": 1,
            "name": "现金支付",
            "type": "cash",
            "config": {"description": "现金支付"},
            "is_active": True,
            "sort_order": 1,
            "created_at": "2025-07-22T12:00:00",
            "updated_at": "2025-07-22T12:00:00"
        },
        {
            "id": 2,
            "name": "支付宝",
            "type": "alipay",
            "config": {
                "app_id": "202100**********",
                "private_key": "请配置您的RSA私钥",
                "public_key": "请配置支付宝公钥"
            },
            "is_active": True,
            "sort_order": 2,
            "created_at": "2025-07-22T12:00:00",
            "updated_at": "2025-07-22T12:00:00"
        },
        {
            "id": 3,
            "name": "微信支付",
            "type": "wechat",
            "config": {
                "app_id": "wx**********",
                "mch_id": "**********",
                "api_key": "请配置您的API密钥"
            },
            "is_active": True,
            "sort_order": 3,
            "created_at": "2025-07-22T12:00:00",
            "updated_at": "2025-07-22T12:00:00"
        },
        {
            "id": 4,
            "name": "银联POS机",
            "type": "pos",
            "config": {
                "bank_name": "中国银联",
                "fee_rate": "0.6%",
                "description": "POS机刷卡支付"
            },
            "is_active": True,
            "sort_order": 4,
            "created_at": "2025-07-22T12:00:00",
            "updated_at": "2025-07-22T12:00:00"
        },
        {
            "id": 5,
            "name": "数字人民币",
            "type": "digital",
            "config": {
                "currency_type": "dcep",
                "wallet_address": "请配置钱包地址",
                "description": "央行数字货币支付"
            },
            "is_active": True,
            "sort_order": 5,
            "created_at": "2025-07-22T12:00:00",
            "updated_at": "2025-07-22T12:00:00"
        }
    ]


@router.get("/{method_id}", response_model=dict)
def get_payment_method(
    method_id: int,
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    获取单个支付方式
    """
    method = payment_method.get(db, id=method_id)
    if not method:
        raise HTTPException(status_code=404, detail="支付方式不存在")
    
    return {
        "id": method.id,
        "name": method.name,
        "type": method.type.value if hasattr(method.type, 'value') else str(method.type).lower(),
        "config": method.config,
        "is_active": method.is_active,
        "sort_order": method.sort_order,
        "created_at": method.created_at,
        "updated_at": method.updated_at
    }


@router.post("/", response_model=dict)
def create_payment_method(
    *,
    db: Session = Depends(deps.get_db),
    method_in: dict
) -> Any:
    """
    创建支付方式
    """
    # 模拟创建成功，返回新的ID
    new_id = 100 + len([1, 2, 3, 4, 5])  # 简单的ID生成

    return {
        "id": new_id,
        "name": getattr(method_in, "name", "新支付方式"),
        "type": getattr(method_in, "type", "custom"),
        "config": getattr(method_in, "config", {}),
        "is_active": getattr(method_in, "is_active", True),
        "sort_order": getattr(method_in, "sort_order", 99),
        "created_at": "2025-07-22T12:00:00",
        "updated_at": "2025-07-22T12:00:00"
    }


@router.put("/{method_id}", response_model=dict)
def update_payment_method(
    *,
    db: Session = Depends(deps.get_db),
    method_id: int,
    method_in: dict
) -> Any:
    """
    更新支付方式
    """
    # 模拟更新成功
    return {
        "id": method_id,
        "name": method_in.get("name", "支付方式"),
        "type": method_in.get("type", "custom"),
        "config": method_in.get("config", {}),
        "is_active": method_in.get("is_active", True),
        "sort_order": method_in.get("sort_order", 1),
        "created_at": "2025-07-22T12:00:00",
        "updated_at": "2025-07-22T12:30:00"
    }


@router.delete("/{method_id}")
def delete_payment_method(
    *,
    db: Session = Depends(deps.get_db),
    method_id: int
) -> Any:
    """
    删除支付方式
    """
    method_obj = payment_method.get(db, id=method_id)
    if not method_obj:
        raise HTTPException(status_code=404, detail="支付方式不存在")
    
    payment_method.remove(db, id=method_id)
    return {"message": "支付方式删除成功"}


@router.post("/{method_id}/toggle")
def toggle_payment_method(
    *,
    db: Session = Depends(deps.get_db),
    method_id: int
) -> Any:
    """
    切换支付方式启用状态
    """
    # 模拟状态切换成功
    
    return {
        "id": method_id,
        "message": "支付方式状态切换成功",
        "is_active": False  # 模拟切换为禁用状态
    }


@router.post("/test-config")
def test_payment_config(
    *,
    db: Session = Depends(deps.get_db),
    method_type: str,
    config: dict
) -> Any:
    """
    测试支付配置
    """
    try:
        # 这里可以添加具体的配置测试逻辑
        # 比如测试支付宝、微信支付的API连接
        
        if method_type == "alipay":
            # 验证支付宝配置
            required_fields = ["app_id", "private_key", "public_key"]
            for field in required_fields:
                if not config.get(field):
                    raise HTTPException(status_code=400, detail=f"缺少必需的配置项: {field}")
        
        elif method_type == "wechat":
            # 验证微信支付配置
            required_fields = ["app_id", "mch_id", "api_key"]
            for field in required_fields:
                if not config.get(field):
                    raise HTTPException(status_code=400, detail=f"缺少必需的配置项: {field}")
        
        return {"status": "success", "message": "配置测试通过"}
        
    except Exception as e:
        return {"status": "error", "message": str(e)}
