from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session
from datetime import datetime
from pydantic import BaseModel

from app.db.session import get_db
from app.models.commission_scheme import CommissionScheme
from app.models.employee import Employee
from app.models.store_item import StoreItem
from app.models.group_item import GroupItem
from app.schemas.commission_scheme import CommissionScheme as CommissionSchemeSchema
from app.schemas.commission_scheme import CommissionSchemeCreate, CommissionSchemeUpdate
from app.schemas.commission_scheme import CommissionCalculationRequest, CommissionCalculationResponse
from app.api.deps import get_current_active_employee

router = APIRouter()


@router.get("/", response_model=List[CommissionSchemeSchema])
def read_commission_schemes(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    获取提成方案列表
    """
    schemes = db.query(CommissionScheme).filter(
        CommissionScheme.is_deleted == False
    ).offset(skip).limit(limit).all()
    return schemes


@router.post("/", response_model=CommissionSchemeSchema, status_code=status.HTTP_201_CREATED)
def create_commission_scheme(
    *,
    db: Session = Depends(get_db),
    scheme_in: CommissionSchemeCreate,
) -> Any:
    """
    创建提成方案
    """
    # 如果设置为默认方案，需要取消其他默认方案
    if scheme_in.is_default:
        db.query(CommissionScheme).filter(
            CommissionScheme.is_default == True,
            CommissionScheme.is_deleted == False
        ).update({"is_default": False})
    
    scheme = CommissionScheme(
        name=scheme_in.name,
        description=scheme_in.description,
        base_type=scheme_in.base_type,
        rules=scheme_in.rules,
        scope=scheme_in.scope,
        priority=scheme_in.priority,
        is_default=scheme_in.is_default,
        effective_date=scheme_in.effective_date,
        expiry_date=scheme_in.expiry_date,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
    )
    
    db.add(scheme)
    db.commit()
    db.refresh(scheme)
    
    return scheme


@router.get("/{scheme_id}", response_model=CommissionSchemeSchema)
def read_commission_scheme(
    *,
    db: Session = Depends(get_db),
    scheme_id: int = Path(..., gt=0, description="scheme_id"),
) -> Any:
    """
    获取提成方案详情
    """
    scheme = db.query(CommissionScheme).filter(
        CommissionScheme.id == scheme_id,
        CommissionScheme.is_deleted == False
    ).first()
    
    if not scheme:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Commission scheme with ID {scheme_id} not found"
        )
    
    return scheme


@router.put("/{scheme_id}", response_model=CommissionSchemeSchema)
def update_commission_scheme(
    *,
    db: Session = Depends(get_db),
    scheme_id: int = Path(..., gt=0, description="scheme_id"),
    scheme_in: CommissionSchemeUpdate,
) -> Any:
    """
    更新提成方案
    """
    scheme = db.query(CommissionScheme).filter(
        CommissionScheme.id == scheme_id,
        CommissionScheme.is_deleted == False
    ).first()
    
    if not scheme:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Commission scheme with ID {scheme_id} not found"
        )
    
    # 如果设置为默认方案，需要取消其他默认方案
    if scheme_in.is_default:
        db.query(CommissionScheme).filter(
            CommissionScheme.id != scheme_id,
            CommissionScheme.is_default == True,
            CommissionScheme.is_deleted == False
        ).update({"is_default": False})
    
    update_data = scheme_in.model_dump(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(scheme, field, value)
    
    scheme.updated_at = datetime.utcnow()
    
    db.add(scheme)
    db.commit()
    db.refresh(scheme)
    
    return scheme


@router.delete("/{scheme_id}", response_model=CommissionSchemeSchema)
def delete_commission_scheme(
    *,
    db: Session = Depends(get_db),
    scheme_id: int = Path(..., gt=0, description="scheme_id"),
) -> Any:
    """
    删除提成方案 (软删除)
    """
    scheme = db.query(CommissionScheme).filter(
        CommissionScheme.id == scheme_id,
        CommissionScheme.is_deleted == False
    ).first()
    
    if not scheme:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Commission scheme with ID {scheme_id} not found"
        )
    
    # 检查是否有员工正在使用该方案
    employees_using_scheme = db.query(Employee).filter(
        Employee.commission_scheme_id == scheme_id,
        Employee.is_deleted == False
    ).count()
    
    if employees_using_scheme > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot delete scheme as it is being used by {employees_using_scheme} employees"
        )
    
    scheme.is_deleted = True
    scheme.updated_at = datetime.utcnow()
    
    db.add(scheme)
    db.commit()
    db.refresh(scheme)
    
    return scheme


@router.post("/calculate", response_model=CommissionCalculationResponse)
def calculate_commission(
    *,
    db: Session = Depends(get_db),
    calculation_request: CommissionCalculationRequest,
) -> Any:
    """
    计算提成
    """
    # 获取提成方案
    scheme = db.query(CommissionScheme).filter(
        CommissionScheme.id == calculation_request.scheme_id,
        CommissionScheme.is_deleted == False
    ).first()
    
    if not scheme:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Commission scheme with ID {calculation_request.scheme_id} not found"
        )
    
    # 根据提成基数类型确定计算基数
    base_amount = calculation_request.amount
    
    if scheme.base_type == "original_price" and calculation_request.original_price is not None:
        base_amount = calculation_request.original_price
    elif scheme.base_type == "actual_price" and calculation_request.actual_price is not None:
        base_amount = calculation_request.actual_price
    elif scheme.base_type == "profit" and calculation_request.actual_price is not None and calculation_request.cost is not None:
        base_amount = calculation_request.actual_price - calculation_request.cost
    
    # 根据规则计算提成
    commission_amount = 0
    calculation_details = {
        "base_type": scheme.base_type,
        "base_amount": base_amount,
        "rule_type": scheme.rules.get("type")
    }
    
    rule_type = scheme.rules.get("type")
    
    if rule_type == "fixed":
        # 固定金额
        commission_amount = float(scheme.rules.get("amount", 0))
        calculation_details["fixed_amount"] = commission_amount
        
    elif rule_type == "percentage":
        # 百分比
        percentage = float(scheme.rules.get("percentage", 0))
        commission_amount = base_amount * percentage / 100
        calculation_details["percentage"] = percentage
        calculation_details["calculation"] = f"{base_amount} * {percentage}% = {commission_amount}"
        
    elif rule_type == "tiered":
        # 阶梯提成
        tiers = scheme.rules.get("tiers", [])
        applicable_tier = None
        
        for tier in tiers:
            min_value = float(tier.get("min", 0))
            max_value = float(tier.get("max")) if tier.get("max") is not None else float('inf')
            
            if min_value <= base_amount < max_value:
                applicable_tier = tier
                break
        
        if applicable_tier:
            tier_type = applicable_tier.get("type")
            tier_value = float(applicable_tier.get("value", 0))
            
            if tier_type == "percentage":
                commission_amount = base_amount * tier_value / 100
                calculation_details["tier"] = applicable_tier
                calculation_details["calculation"] = f"{base_amount} * {tier_value}% = {commission_amount}"
            else:
                commission_amount = tier_value
                calculation_details["tier"] = applicable_tier
                calculation_details["calculation"] = f"Fixed amount: {commission_amount}"
    
    else:
        # 自定义规则
        calculation_details["custom_rules"] = scheme.rules
        # 对于自定义规则，这里只返回一个默认值，实际应用中可能需要更复杂的逻辑
        commission_amount = base_amount * 0.1
    
    return {
        "commission": commission_amount,
        "details": calculation_details
    }


@router.get("/applicable/{employee_id}", response_model=List[CommissionSchemeSchema])
def get_applicable_schemes(
    *,
    db: Session = Depends(get_db),
    employee_id: int = Path(..., gt=0, description="员工ID"),
) -> Any:
    """
    获取适用于特定员工的提成方案列表
    """
    # 获取员工信息
    employee = db.query(Employee).filter(
        Employee.id == employee_id,
        Employee.is_deleted == False
    ).first()
    
    if not employee:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Employee with ID {employee_id} not found"
        )
    
    # 查询所有有效的提成方案
    schemes = db.query(CommissionScheme).filter(
        CommissionScheme.is_deleted == False
    ).all()
    
    # 过滤出适用于该员工的方案
    applicable_schemes = []
    
    for scheme in schemes:
        # 检查是否是默认方案
        if scheme.is_default:
            applicable_schemes.append(scheme)
            continue
        
        # 检查是否是全局方案
        if scheme.scope and scheme.scope.get("global", False):
            applicable_schemes.append(scheme)
            continue
        
        # 检查门店适用性
        if scheme.scope and "stores" in scheme.scope:
            if employee.store_id in scheme.scope["stores"]:
                applicable_schemes.append(scheme)
                continue
        
        # 检查角色适用性
        if scheme.scope and "roles" in scheme.scope:
            if employee.role in scheme.scope["roles"]:
                applicable_schemes.append(scheme)
                continue
    
    # 按优先级排序
    applicable_schemes.sort(key=lambda x: x.priority, reverse=True)

    return applicable_schemes


class DynamicCommissionRequest(BaseModel):
    """动态提成方案请求模型"""
    scheme_name: str
    description: str
    calculation_base: str  # actual_amount, original_amount, profit
    time_based_rules: List[dict]  # 时间段规则
    performance_tiers: List[dict]  # 业绩阶梯
    team_rules: Optional[dict] = None  # 团队提成规则
    seasonal_adjustments: Optional[List[dict]] = None  # 季节性调整
    is_active: bool = True


@router.post("/dynamic")
def create_dynamic_commission_scheme(
    request: DynamicCommissionRequest,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_active_employee)
):
    """
    创建动态提成方案
    """
    try:
        from app.services.dynamic_commission import DynamicCommissionService

        service = DynamicCommissionService(db)
        result = service.create_dynamic_scheme(
            scheme_name=request.scheme_name,
            description=request.description,
            calculation_base=request.calculation_base,
            time_based_rules=request.time_based_rules,
            performance_tiers=request.performance_tiers,
            team_rules=request.team_rules,
            seasonal_adjustments=request.seasonal_adjustments,
            is_active=request.is_active
        )

        return {
            "success": True,
            "data": result
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建动态提成方案失败: {str(e)}")


@router.get("/dynamic/{scheme_id}/simulate")
def simulate_dynamic_commission(
    scheme_id: int,
    employee_id: int = Query(..., description="员工ID"),
    amount: float = Query(..., description="业绩金额"),
    date: str = Query(..., description="日期 YYYY-MM-DD"),
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_active_employee)
):
    """
    模拟动态提成计算
    """
    try:
        from app.services.dynamic_commission import DynamicCommissionService
        from datetime import datetime

        # 解析日期
        calculation_date = datetime.strptime(date, '%Y-%m-%d')

        service = DynamicCommissionService(db)
        result = service.simulate_commission(
            scheme_id=scheme_id,
            employee_id=employee_id,
            amount=amount,
            calculation_date=calculation_date
        )

        return {
            "success": True,
            "data": result
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"模拟提成计算失败: {str(e)}")


@router.get("/dynamic/calculation-bases")
def get_calculation_bases(
    current_employee: Employee = Depends(get_current_active_employee)
):
    """
    获取提成计算基数选项
    """
    try:
        calculation_bases = [
            {
                "value": "actual_amount",
                "label": "实收金额",
                "description": "基于客户实际支付的金额计算提成",
                "example": "客户支付200元，按200元计算提成"
            },
            {
                "value": "original_amount",
                "label": "原价金额",
                "description": "基于服务原价计算提成，不受折扣影响",
                "example": "服务原价300元，打8折后240元，按300元计算提成"
            },
            {
                "value": "profit",
                "label": "利润金额",
                "description": "基于利润金额计算提成",
                "example": "收入200元，成本80元，利润120元，按120元计算提成"
            },
            {
                "value": "weighted_amount",
                "label": "加权金额",
                "description": "基于加权计算的金额，可设置不同服务的权重",
                "example": "高端服务权重1.2，普通服务权重1.0"
            }
        ]

        return {
            "success": True,
            "data": {
                "calculation_bases": calculation_bases,
                "total_count": len(calculation_bases)
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取计算基数选项失败: {str(e)}")


@router.get("/dynamic/templates")
def get_commission_templates(
    current_employee: Employee = Depends(get_current_active_employee)
):
    """
    获取提成方案模板
    """
    try:
        templates = [
            {
                "template_id": "template_001",
                "name": "标准阶梯提成",
                "description": "基于业绩阶梯的标准提成方案",
                "calculation_base": "actual_amount",
                "performance_tiers": [
                    {"min_amount": 0, "max_amount": 5000, "rate": 0.08},
                    {"min_amount": 5000, "max_amount": 10000, "rate": 0.10},
                    {"min_amount": 10000, "max_amount": 20000, "rate": 0.12},
                    {"min_amount": 20000, "max_amount": None, "rate": 0.15}
                ],
                "suitable_for": ["普通技师", "新员工"]
            },
            {
                "template_id": "template_002",
                "name": "时段差异提成",
                "description": "根据不同时段设置不同提成比例",
                "calculation_base": "actual_amount",
                "time_based_rules": [
                    {"period": "peak_hours", "multiplier": 1.2},
                    {"period": "off_peak_hours", "multiplier": 1.0},
                    {"period": "holiday_period", "multiplier": 1.5}
                ],
                "suitable_for": ["全职技师", "资深员工"]
            }
        ]

        return {
            "success": True,
            "data": {
                "templates": templates,
                "total_count": len(templates)
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取提成模板失败: {str(e)}")