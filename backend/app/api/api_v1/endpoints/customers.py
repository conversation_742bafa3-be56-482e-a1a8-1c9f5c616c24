from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, or_

from app.db.session import get_db
from app.models.customer import Customer, MemberCard
from app.models.operation import Order
from app.models.appointment import Appointment
from app.schemas.customer import (
    CustomerCreate, CustomerUpdate, CustomerResponse, CustomerListResponse
)
# from app.core.deps import get_current_user

router = APIRouter()


@router.get("/", response_model=CustomerListResponse)
def get_customers(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    keyword: Optional[str] = Query(None, description="搜索关键词"),
    member_level: Optional[str] = Query(None, description="会员等级"),
    status: Optional[str] = Query(None, description="客户状态"),
    store_id: Optional[int] = Query(None, description="门店ID"),
    db: Session = Depends(get_db)
):
    """获取客户列表"""
    query = db.query(Customer).filter(Customer.is_deleted == False)
    
    # 关键词搜索
    if keyword:
        query = query.filter(
            or_(
                Customer.name.contains(keyword),
                Customer.phone.contains(keyword)
            )
        )
    
    # 会员等级筛选
    if member_level:
        query = query.filter(Customer.member_level == member_level)
    
    # 状态筛选
    if status:
        query = query.filter(Customer.status == status)
    
    # 门店筛选
    if store_id:
        query = query.filter(Customer.store_id == store_id)
    
    # 获取总数
    total = query.count()
    
    # 分页查询
    customers = query.order_by(desc(Customer.created_at)).offset(skip).limit(limit).all()
    
    return CustomerListResponse(
        items=customers,
        total=total,
        page=skip // limit + 1,
        size=limit
    )


@router.get("/{customer_id}", response_model=CustomerResponse)
def get_customer(
    customer_id: int,
    db: Session = Depends(get_db)
):
    """获取客户详情"""
    customer = db.query(Customer).filter(
        Customer.id == customer_id,
        Customer.is_deleted == False
    ).first()
    
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )
    
    return customer


@router.post("/", response_model=CustomerResponse)
def create_customer(
    customer_data: CustomerCreate,
    db: Session = Depends(get_db)
):
    """创建客户"""
    # 检查手机号是否已存在
    existing_customer = db.query(Customer).filter(
        Customer.phone == customer_data.phone,
        Customer.is_deleted == False
    ).first()

    if existing_customer:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="手机号已存在"
        )

    # 创建客户
    customer_dict = customer_data.dict()

    # 处理不存在的字段
    member_level = customer_dict.pop('member_level', None)  # 需要映射到member_level_id
    status = customer_dict.pop('status', None)  # Customer模型中没有status字段

    # 创建客户对象
    customer = Customer(**customer_dict)

    # TODO: 如果需要设置会员等级，需要根据member_level字符串查找对应的member_level_id
    # 暂时不设置会员等级，使用默认值

    db.add(customer)
    db.commit()
    db.refresh(customer)

    return customer


@router.put("/{customer_id}", response_model=CustomerResponse)
def update_customer(
    customer_id: int,
    customer_data: CustomerUpdate,
    db: Session = Depends(get_db)
):
    """更新客户"""
    customer = db.query(Customer).filter(
        Customer.id == customer_id,
        Customer.is_deleted == False
    ).first()
    
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )
    
    # 检查手机号是否被其他客户使用
    if customer_data.phone and customer_data.phone != customer.phone:
        existing_customer = db.query(Customer).filter(
            Customer.phone == customer_data.phone,
            Customer.id != customer_id,
            Customer.is_deleted == False
        ).first()
        
        if existing_customer:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="手机号已被其他客户使用"
            )
    
    # 更新客户信息
    update_data = customer_data.dict(exclude_unset=True)

    # 处理不存在的字段
    member_level = update_data.pop('member_level', None)  # 需要映射到member_level_id
    status = update_data.pop('status', None)  # Customer模型中没有status字段

    # 更新其他字段
    for field, value in update_data.items():
        setattr(customer, field, value)

    # TODO: 如果需要更新会员等级，需要根据member_level字符串查找对应的member_level_id

    db.commit()
    db.refresh(customer)

    return customer


@router.delete("/{customer_id}")
def delete_customer(
    customer_id: int,
    db: Session = Depends(get_db)
):
    """删除客户"""
    customer = db.query(Customer).filter(
        Customer.id == customer_id,
        Customer.is_deleted == False
    ).first()
    
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )
    
    # 检查是否有未完成的订单
    pending_orders = db.query(Order).filter(
        Order.customer_id == customer_id,
        Order.status.in_(['pending', 'paid']),
        Order.is_deleted == False
    ).count()
    
    if pending_orders > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="客户有未完成的订单，无法删除"
        )
    
    # 软删除
    customer.is_deleted = True

    db.commit()

    return {"message": "客户删除成功"}


@router.get("/{customer_id}/orders")
def get_customer_orders(
    customer_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=50),
    db: Session = Depends(get_db)
):
    """获取客户订单记录"""
    # 检查客户是否存在
    customer = db.query(Customer).filter(
        Customer.id == customer_id,
        Customer.is_deleted == False
    ).first()
    
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )
    
    # 查询订单
    query = db.query(Order).filter(
        Order.customer_id == customer_id,
        Order.is_deleted == False
    )
    
    total = query.count()
    orders = query.order_by(desc(Order.created_at)).offset(skip).limit(limit).all()
    
    return {
        "items": orders,
        "total": total,
        "page": skip // limit + 1,
        "size": limit
    }


@router.get("/{customer_id}/appointments")
def get_customer_appointments(
    customer_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=50),
    db: Session = Depends(get_db)
):
    """获取客户预约记录"""
    # 检查客户是否存在
    customer = db.query(Customer).filter(
        Customer.id == customer_id,
        Customer.is_deleted == False
    ).first()
    
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )
    
    # 查询预约
    query = db.query(Appointment).filter(
        Appointment.customer_id == customer_id,
        Appointment.is_deleted == False
    )
    
    total = query.count()
    appointments = query.order_by(desc(Appointment.appointment_time)).offset(skip).limit(limit).all()
    
    return {
        "items": appointments,
        "total": total,
        "page": skip // limit + 1,
        "size": limit
    }


@router.get("/{customer_id}/membership")
def get_customer_membership(
    customer_id: int,
    db: Session = Depends(get_db)
):
    """获取客户会员信息"""
    # 检查客户是否存在
    customer = db.query(Customer).filter(
        Customer.id == customer_id,
        Customer.is_deleted == False
    ).first()
    
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )
    
    # 查询会员卡
    membership = db.query(MemberCard).filter(
        MemberCard.customer_id == customer_id,
        MemberCard.is_deleted == False
    ).first()
    
    return membership


@router.get("/stats")
def get_customer_stats(
    db: Session = Depends(get_db)
):
    """获取客户统计信息"""
    # 总客户数
    total_customers = db.query(Customer).filter(Customer.is_deleted == False).count()
    
    # 活跃客户数（最近30天有消费）
    from datetime import datetime, timedelta
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    
    active_customers = db.query(Customer).join(Order).filter(
        Customer.is_deleted == False,
        Order.created_at >= thirty_days_ago,
        Order.is_deleted == False
    ).distinct().count()
    
    # 会员客户数
    member_customers = db.query(Customer).filter(
        Customer.is_deleted == False,
        Customer.member_level != 'normal'
    ).count()
    
    # 新增客户数（本月）
    current_month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    new_customers = db.query(Customer).filter(
        Customer.is_deleted == False,
        Customer.created_at >= current_month_start
    ).count()
    
    return {
        "total_customers": total_customers,
        "active_customers": active_customers,
        "member_customers": member_customers,
        "new_customers": new_customers
    }


@router.delete("/batch")
def batch_delete_customers(
    customer_ids: List[int],
    db: Session = Depends(get_db)
):
    """批量删除客户"""
    # 检查是否有未完成的订单
    pending_orders = db.query(Order).filter(
        Order.customer_id.in_(customer_ids),
        Order.status.in_(['pending', 'paid']),
        Order.is_deleted == False
    ).count()
    
    if pending_orders > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="部分客户有未完成的订单，无法删除"
        )
    
    # 批量软删除
    deleted_count = db.query(Customer).filter(
        Customer.id.in_(customer_ids),
        Customer.is_deleted == False
    ).update({
        "is_deleted": True
    }, synchronize_session=False)
    
    db.commit()
    
    return {"message": f"成功删除 {deleted_count} 个客户"}
