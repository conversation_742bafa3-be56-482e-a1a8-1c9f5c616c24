from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from decimal import Decimal

from app.api.deps import get_db
from app.crud.member_level import member_level, member_level_benefit
from app.schemas.member_level import (
    MemberLevelResponse, MemberLevelCreate, MemberLevelUpdate, MemberLevelListResponse,
    MemberLevelBenefitResponse, MemberLevelBenefitCreate, MemberLevelBenefitUpdate,
    MemberLevelUpgradeResult, MemberPriceResult, MemberLevelAnalytics,
    MemberLevelStatistics
)

router = APIRouter()


# 添加两个路由以支持不同的路径格式
@router.get("/", response_model=MemberLevelListResponse)
@router.get("", response_model=MemberLevelListResponse)
def get_member_levels(
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    is_active: Optional[bool] = Query(None, description="是否启用"),
    db: Session = Depends(get_db)
):
    """获取会员等级列表"""
    levels = member_level.get_multi(db, skip=skip, limit=limit, is_active=is_active)
    total = member_level.get_count(db, is_active=is_active)
    
    return MemberLevelListResponse(
        items=levels,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )


@router.get("/{level_id}", response_model=MemberLevelResponse)
def get_member_level(level_id: int, db: Session = Depends(get_db)):
    """获取指定会员等级详情"""
    level = member_level.get(db, level_id)
    if not level:
        raise HTTPException(status_code=404, detail="会员等级不存在")
    return level


@router.post("/", response_model=MemberLevelResponse)
def create_member_level(level_in: MemberLevelCreate, db: Session = Depends(get_db)):
    """创建会员等级"""
    try:
        level = member_level.create(db, level_in)
        return level
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{level_id}", response_model=MemberLevelResponse)
def update_member_level(
    level_id: int, 
    level_in: MemberLevelUpdate, 
    db: Session = Depends(get_db)
):
    """更新会员等级"""
    level = member_level.get(db, level_id)
    if not level:
        raise HTTPException(status_code=404, detail="会员等级不存在")
    
    try:
        level = member_level.update(db, level, level_in)
        return level
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/{level_id}")
def delete_member_level(level_id: int, db: Session = Depends(get_db)):
    """删除会员等级"""
    level = member_level.get(db, level_id)
    if not level:
        raise HTTPException(status_code=404, detail="会员等级不存在")
    
    try:
        member_level.remove(db, level_id)
        return {"message": "会员等级删除成功"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/{level_id}/benefits", response_model=List[MemberLevelBenefitResponse])
def get_level_benefits(level_id: int, db: Session = Depends(get_db)):
    """获取会员等级权益列表"""
    level = member_level.get(db, level_id)
    if not level:
        raise HTTPException(status_code=404, detail="会员等级不存在")
    
    benefits = member_level_benefit.get_by_level(db, level_id)
    return benefits


@router.post("/{level_id}/benefits", response_model=MemberLevelBenefitResponse)
def create_level_benefit(
    level_id: int, 
    benefit_in: MemberLevelBenefitCreate, 
    db: Session = Depends(get_db)
):
    """为会员等级添加权益"""
    level = member_level.get(db, level_id)
    if not level:
        raise HTTPException(status_code=404, detail="会员等级不存在")
    
    benefit = member_level_benefit.create(db, level_id, benefit_in)
    return benefit


@router.put("/benefits/{benefit_id}", response_model=MemberLevelBenefitResponse)
def update_level_benefit(
    benefit_id: int, 
    benefit_in: MemberLevelBenefitUpdate, 
    db: Session = Depends(get_db)
):
    """更新会员等级权益"""
    benefit = member_level_benefit.get(db, benefit_id)
    if not benefit:
        raise HTTPException(status_code=404, detail="权益不存在")
    
    benefit = member_level_benefit.update(db, benefit, benefit_in)
    return benefit


@router.delete("/benefits/{benefit_id}")
def delete_level_benefit(benefit_id: int, db: Session = Depends(get_db)):
    """删除会员等级权益"""
    benefit = member_level_benefit.get(db, benefit_id)
    if not benefit:
        raise HTTPException(status_code=404, detail="权益不存在")
    
    member_level_benefit.remove(db, benefit_id)
    return {"message": "权益删除成功"}


@router.post("/check-upgrade/{customer_id}", response_model=MemberLevelUpgradeResult)
def check_customer_upgrade(customer_id: int, db: Session = Depends(get_db)):
    """检查客户是否可以升级会员等级"""
    try:
        upgrade_info = member_level.check_upgrade(db, customer_id)
        return MemberLevelUpgradeResult(
            can_upgrade=upgrade_info["can_upgrade"],
            current_level=upgrade_info["current_level"],
            target_level=upgrade_info["target_level"],
            upgrade_reason=upgrade_info["upgrade_reason"]
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.post("/upgrade/{customer_id}", response_model=MemberLevelUpgradeResult)
def upgrade_customer_level(customer_id: int, db: Session = Depends(get_db)):
    """升级客户会员等级"""
    try:
        upgrade_info = member_level.upgrade_customer_level(db, customer_id)
        return MemberLevelUpgradeResult(
            can_upgrade=upgrade_info["can_upgrade"],
            current_level=upgrade_info.get("current_level"),
            target_level=upgrade_info.get("target_level"),
            upgrade_reason=upgrade_info["upgrade_reason"]
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.get("/calculate-price/{customer_id}")
def calculate_member_price(
    customer_id: int,
    original_price: Decimal = Query(..., description="原价"),
    db: Session = Depends(get_db)
):
    """计算客户会员价格"""
    from app.models.customer import Customer
    
    customer = db.query(Customer).filter(Customer.id == customer_id).first()
    if not customer:
        raise HTTPException(status_code=404, detail="客户不存在")
    
    discount_rate = customer.get_discount_rate()
    discounted_price = customer.calculate_price(original_price)
    discount_amount = float(original_price) - discounted_price
    
    return MemberPriceResult(
        original_price=original_price,
        discount_rate=discount_rate,
        discounted_price=discounted_price,
        discount_amount=discount_amount,
        member_level=customer.member_level
    )


@router.get("/suitable-level")
def get_suitable_level(
    recharge_amount: Decimal = Query(0, ge=0, description="充值金额"),
    consumption_amount: Decimal = Query(0, ge=0, description="消费金额"),
    db: Session = Depends(get_db)
):
    """根据充值和消费金额获取合适的会员等级"""
    suitable_level = member_level.get_suitable_level(db, recharge_amount, consumption_amount)
    if not suitable_level:
        raise HTTPException(status_code=404, detail="没有找到合适的会员等级")
    
    return suitable_level


@router.get("/statistics", response_model=List[MemberLevelStatistics])
def get_member_level_statistics(db: Session = Depends(get_db)):
    """获取会员等级统计信息"""
    stats = member_level.get_statistics(db)
    return stats


@router.get("/analytics", response_model=MemberLevelAnalytics)
def get_member_level_analytics(db: Session = Depends(get_db)):
    """获取会员等级分析数据"""
    from app.models.customer import Customer
    from sqlalchemy import func
    
    # 获取基础统计
    stats = member_level.get_statistics(db)
    total_levels = member_level.get_count(db, is_active=True)
    total_customers = db.query(Customer).count()
    
    # 计算升级趋势（最近30天）
    upgrade_trends = {
        "recent_upgrades": 0,  # 这里可以添加更复杂的趋势分析
        "upgrade_rate": 0.0
    }
    
    # 计算各等级营收分布
    revenue_by_level = {}
    for stat in stats:
        revenue_by_level[stat.level_name] = stat.total_consumption_amount
    
    return MemberLevelAnalytics(
        total_levels=total_levels,
        total_customers=total_customers,
        level_distribution=stats,
        upgrade_trends=upgrade_trends,
        revenue_by_level=revenue_by_level
    )
