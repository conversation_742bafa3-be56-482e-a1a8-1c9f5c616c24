from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session
from datetime import datetime, date, time

from app.db.session import get_db
from app.models.appointment import Appointment, Resource, Schedule, FollowUp, ScheduleRule
from app.schemas.appointment import (
    AppointmentCreate, AppointmentUpdate, AppointmentInDB, AppointmentWithDetails,
    ResourceCreate, ResourceUpdate, ResourceInDB, ResourceWithDetails,
    ScheduleCreate, ScheduleUpdate, ScheduleInDB, ScheduleWithDetails,
    FollowUpCreate, FollowUpUpdate, FollowUpInDB, FollowUpWithDetails,
    ScheduleRuleCreate, ScheduleRuleUpdate, ScheduleRuleInDB, ScheduleRuleWithDetails,
    AutoScheduleRequest
)
from app.crud import appointment as crud


router = APIRouter()


# 预约管理API
@router.post("/appointments", response_model=AppointmentInDB, status_code=status.HTTP_201_CREATED)
def create_appointment(
    appointment: AppointmentCreate,
    db: Session = Depends(get_db)
):
    """创建新预约"""
    # 检查预约冲突
    conflict = crud.check_appointment_conflict(
        db=db,
        employee_id=appointment.employee_id,
        start_time=appointment.start_time,
        end_time=appointment.end_time,
        resource_id=appointment.resource_id
    )
    if conflict:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="预约时间冲突，该时间段内技师或资源已被预约"
        )
    
    return crud.create_appointment(db=db, appointment=appointment)


@router.get("/appointments", response_model=List[AppointmentInDB])
def read_appointments(
    store_id: Optional[int] = None,
    customer_id: Optional[int] = None,
    employee_id: Optional[int] = None,
    status: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取预约列表，支持多种过滤条件"""
    appointments = crud.get_appointments(
        db=db,
        store_id=store_id,
        customer_id=customer_id,
        employee_id=employee_id,
        status=status,
        start_date=start_date,
        end_date=end_date,
        skip=skip,
        limit=limit
    )
    return appointments


@router.get("/appointments/{appointment_id}", response_model=AppointmentWithDetails)
def read_appointment(
    appointment_id: int = Path(..., title="预约ID", description="预约ID"),
    db: Session = Depends(get_db)
):
    """获取单个预约详情"""
    appointment = crud.get_appointment(db=db, appointment_id=appointment_id)
    if not appointment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"预约ID {appointment_id} 不存在"
        )
    return appointment


@router.put("/appointments/{appointment_id}", response_model=AppointmentInDB)
def update_appointment(
    appointment_update: AppointmentUpdate,
    appointment_id: int = Path(..., title="预约ID", description="预约ID"),
    db: Session = Depends(get_db)
):
    """更新预约信息"""
    # 先检查预约是否存在
    db_appointment = crud.get_appointment(db=db, appointment_id=appointment_id)
    if not db_appointment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"预约ID {appointment_id} 不存在"
        )
    
    # 如果更新了时间或技师/资源，需要检查冲突
    if (appointment_update.start_time or appointment_update.end_time or 
        appointment_update.employee_id or appointment_update.resource_id):
        
        start_time = appointment_update.start_time or db_appointment.start_time
        end_time = appointment_update.end_time or db_appointment.end_time
        employee_id = appointment_update.employee_id or db_appointment.employee_id
        resource_id = appointment_update.resource_id or db_appointment.resource_id
        
        conflict = crud.check_appointment_conflict(
            db=db,
            employee_id=employee_id,
            start_time=start_time,
            end_time=end_time,
            resource_id=resource_id,
            exclude_appointment_id=appointment_id
        )
        if conflict:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="预约时间冲突，该时间段内技师或资源已被预约"
            )
    
    return crud.update_appointment(db=db, appointment_id=appointment_id, appointment_update=appointment_update)


@router.delete("/appointments/{appointment_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_appointment(
    appointment_id: int = Path(..., title="预约ID", description="预约ID"),
    db: Session = Depends(get_db)
):
    """删除预约"""
    success = crud.delete_appointment(db=db, appointment_id=appointment_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"预约ID {appointment_id} 不存在"
        )
    return {"status": "success"}


@router.put("/appointments/{appointment_id}/status", response_model=AppointmentInDB)
def update_appointment_status(
    status: str,
    appointment_id: int = Path(..., title="预约ID", description="预约ID"),
    db: Session = Depends(get_db)
):
    """更新预约状态"""
    appointment = crud.update_appointment_status(db=db, appointment_id=appointment_id, status=status)
    if not appointment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"预约ID {appointment_id} 不存在"
        )
    return appointment


# 资源管理API
@router.post("/resources", response_model=ResourceInDB, status_code=status.HTTP_201_CREATED)
def create_resource(
    resource: ResourceCreate,
    db: Session = Depends(get_db)
):
    """创建新资源"""
    return crud.create_resource(db=db, resource=resource)


@router.get("/resources", response_model=List[ResourceInDB])
def read_resources(
    store_id: Optional[int] = None,
    resource_type: Optional[str] = None,
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取资源列表，支持多种过滤条件"""
    resources = crud.get_resources(
        db=db,
        store_id=store_id,
        resource_type=resource_type,
        status=status,
        skip=skip,
        limit=limit
    )
    return resources


@router.get("/resources/{resource_id}", response_model=ResourceWithDetails)
def read_resource(
    resource_id: int = Path(..., title="资源ID", description="resource_id"),
    db: Session = Depends(get_db)
):
    """获取单个资源详情"""
    resource = crud.get_resource(db=db, resource_id=resource_id)
    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"资源ID {resource_id} 不存在"
        )
    return resource


@router.put("/resources/{resource_id}", response_model=ResourceInDB)
def update_resource(
    resource_update: ResourceUpdate,
    resource_id: int = Path(..., title="资源ID", description="resource_id"),
    db: Session = Depends(get_db)
):
    """更新资源信息"""
    resource = crud.update_resource(db=db, resource_id=resource_id, resource_update=resource_update)
    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"资源ID {resource_id} 不存在"
        )
    return resource


@router.delete("/resources/{resource_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_resource(
    resource_id: int = Path(..., title="资源ID", description="resource_id"),
    db: Session = Depends(get_db)
):
    """删除资源"""
    success = crud.delete_resource(db=db, resource_id=resource_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"资源ID {resource_id} 不存在"
        )
    return {"status": "success"}


# 排班管理API
@router.post("/schedules", response_model=ScheduleInDB, status_code=status.HTTP_201_CREATED)
def create_schedule(
    schedule: ScheduleCreate,
    db: Session = Depends(get_db)
):
    """创建新排班"""
    return crud.create_schedule(db=db, schedule=schedule)


@router.get("/schedules", response_model=List[ScheduleInDB])
def read_schedules(
    store_id: Optional[int] = None,
    employee_id: Optional[int] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取排班列表，支持多种过滤条件"""
    schedules = crud.get_schedules(
        db=db,
        store_id=store_id,
        employee_id=employee_id,
        start_date=start_date,
        end_date=end_date,
        status=status,
        skip=skip,
        limit=limit
    )
    return schedules


@router.get("/schedules/{schedule_id}", response_model=ScheduleWithDetails)
def read_schedule(
    schedule_id: int = Path(..., title="排班ID", description="schedule_id"),
    db: Session = Depends(get_db)
):
    """获取单个排班详情"""
    schedule = crud.get_schedule(db=db, schedule_id=schedule_id)
    if not schedule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"排班ID {schedule_id} 不存在"
        )
    return schedule


@router.put("/schedules/{schedule_id}", response_model=ScheduleInDB)
def update_schedule(
    schedule_update: ScheduleUpdate,
    schedule_id: int = Path(..., title="排班ID", description="schedule_id"),
    db: Session = Depends(get_db)
):
    """更新排班信息"""
    schedule = crud.update_schedule(db=db, schedule_id=schedule_id, schedule_update=schedule_update)
    if not schedule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"排班ID {schedule_id} 不存在"
        )
    return schedule


@router.delete("/schedules/{schedule_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_schedule(
    schedule_id: int = Path(..., title="排班ID", description="schedule_id"),
    db: Session = Depends(get_db)
):
    """删除排班"""
    success = crud.delete_schedule(db=db, schedule_id=schedule_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"排班ID {schedule_id} 不存在"
        )
    return {"status": "success"}


# 排班规则API
@router.post("/schedule-rules", response_model=ScheduleRuleInDB, status_code=status.HTTP_201_CREATED)
def create_schedule_rule(
    rule: ScheduleRuleCreate,
    db: Session = Depends(get_db)
):
    """创建排班规则"""
    return crud.create_schedule_rule(db=db, rule=rule)


@router.get("/schedule-rules", response_model=List[ScheduleRuleInDB])
def read_schedule_rules(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取排班规则列表"""
    rules = crud.get_schedule_rules(db=db, skip=skip, limit=limit)
    return rules


@router.get("/schedule-rules/{rule_id}", response_model=ScheduleRuleWithDetails)
def read_schedule_rule(
    rule_id: int = Path(..., title="规则ID", description="rule_id"),
    db: Session = Depends(get_db)
):
    """获取单个排班规则详情"""
    rule = crud.get_schedule_rule(db=db, rule_id=rule_id)
    if not rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"排班规则ID {rule_id} 不存在"
        )
    return rule


@router.get("/schedule-rules/store/{store_id}", response_model=ScheduleRuleInDB)
def read_schedule_rule_by_store(
    store_id: int = Path(..., title="门店ID", description="门店ID"),
    db: Session = Depends(get_db)
):
    """通过门店ID获取排班规则"""
    rule = crud.get_schedule_rule_by_store(db=db, store_id=store_id)
    if not rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"门店ID {store_id} 的排班规则不存在"
        )
    return rule


@router.put("/schedule-rules/{rule_id}", response_model=ScheduleRuleInDB)
def update_schedule_rule(
    rule_update: ScheduleRuleUpdate,
    rule_id: int = Path(..., title="规则ID", description="rule_id"),
    db: Session = Depends(get_db)
):
    """更新排班规则"""
    rule = crud.update_schedule_rule(db=db, rule_id=rule_id, rule_update=rule_update)
    if not rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"排班规则ID {rule_id} 不存在"
        )
    return rule


@router.delete("/schedule-rules/{rule_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_schedule_rule(
    rule_id: int = Path(..., title="规则ID", description="rule_id"),
    db: Session = Depends(get_db)
):
    """删除排班规则"""
    success = crud.delete_schedule_rule(db=db, rule_id=rule_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"排班规则ID {rule_id} 不存在"
        )
    return {"status": "success"}


# 自动排班API
@router.post("/auto-schedule", response_model=List[ScheduleInDB], status_code=status.HTTP_201_CREATED)
def auto_schedule(
    auto_schedule_request: AutoScheduleRequest,
    db: Session = Depends(get_db)
):
    """自动生成排班"""
    schedules = crud.generate_auto_schedule(db=db, auto_schedule=auto_schedule_request)
    return schedules


# 随访管理API
@router.post("/follow-ups", response_model=FollowUpInDB, status_code=status.HTTP_201_CREATED)
def create_follow_up(
    follow_up: FollowUpCreate,
    db: Session = Depends(get_db)
):
    """创建新随访"""
    return crud.create_follow_up(db=db, follow_up=follow_up)


@router.get("/follow-ups", response_model=List[FollowUpInDB])
def read_follow_ups(
    appointment_id: Optional[int] = None,
    employee_id: Optional[int] = None,
    customer_id: Optional[int] = None,
    status: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取随访列表，支持多种过滤条件"""
    follow_ups = crud.get_follow_ups(
        db=db,
        appointment_id=appointment_id,
        employee_id=employee_id,
        customer_id=customer_id,
        status=status,
        start_date=start_date,
        end_date=end_date,
        skip=skip,
        limit=limit
    )
    return follow_ups


@router.get("/follow-ups/{follow_up_id}", response_model=FollowUpWithDetails)
def read_follow_up(
    follow_up_id: int = Path(..., title="随访ID", description="follow_up_id"),
    db: Session = Depends(get_db)
):
    """获取单个随访详情"""
    follow_up = crud.get_follow_up(db=db, follow_up_id=follow_up_id)
    if not follow_up:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"随访ID {follow_up_id} 不存在"
        )
    return follow_up


@router.put("/follow-ups/{follow_up_id}", response_model=FollowUpInDB)
def update_follow_up(
    follow_up_update: FollowUpUpdate,
    follow_up_id: int = Path(..., title="随访ID", description="follow_up_id"),
    db: Session = Depends(get_db)
):
    """更新随访信息"""
    follow_up = crud.update_follow_up(db=db, follow_up_id=follow_up_id, follow_up_update=follow_up_update)
    if not follow_up:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"随访ID {follow_up_id} 不存在"
        )
    return follow_up


@router.delete("/follow-ups/{follow_up_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_follow_up(
    follow_up_id: int = Path(..., title="随访ID", description="follow_up_id"),
    db: Session = Depends(get_db)
):
    """删除随访"""
    success = crud.delete_follow_up(db=db, follow_up_id=follow_up_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"随访ID {follow_up_id} 不存在"
        )
    return {"status": "success"} 