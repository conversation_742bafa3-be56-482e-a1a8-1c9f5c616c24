from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from datetime import datetime
from pydantic import BaseModel

from app import schemas, models
from app.api import deps
from app.db.session import get_db
from app.models.employee import Employee
from app.api.deps import get_current_active_employee
from app.schemas.referral import (
    ReferralRewardCreate, ReferralRewardUpdate, ReferralRewardInDB,
    ReferralSettingCreate, ReferralSettingUpdate, ReferralSettingInDB
)
from app.models.referral import (
    ReferralRelationship, ReferralReward, ReferralSetting
)

router = APIRouter()


@router.post("/rewards/", response_model=ReferralRewardInDB)
def create_referral_reward(
    *,
    db: Session = Depends(deps.get_db),
    reward_in: ReferralRewardCreate,
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["manage_referrals"]))
) -> Any:
    """
    创建推荐奖励记录
    """
    # 检查推荐关系是否存在
    relationship = db.query(ReferralRelationship).filter(
        ReferralRelationship.id == reward_in.referral_id,
        ReferralRelationship.is_deleted == False
    ).first()
    
    if not relationship:
        raise HTTPException(status_code=404, detail="推荐关系不存在")
    
    # 检查客户是否存在
    customer = db.query(models.Customer).filter(models.Customer.id == reward_in.customer_id).first()
    if not customer:
        raise HTTPException(status_code=404, detail="客户不存在")
    
    # 创建奖励记录
    reward = ReferralReward(
        referral_id=reward_in.referral_id,
        customer_id=reward_in.customer_id,
        reward_type=reward_in.reward_type,
        reward_value=reward_in.reward_value,
        reward_detail=reward_in.reward_detail,
        trigger_event=reward_in.trigger_event,
        status=reward_in.status
    )
    
    db.add(reward)
    db.commit()
    db.refresh(reward)
    
    return reward


@router.get("/rewards/", response_model=List[ReferralRewardInDB])
def get_referral_rewards(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    customer_id: Optional[int] = None,
    referral_id: Optional[int] = None,
    status: Optional[str] = None,
    reward_type: Optional[str] = None,
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["view_referrals"]))
) -> Any:
    """
    获取推荐奖励记录列表，支持按客户、推荐关系、状态和奖励类型筛选
    """
    query = db.query(ReferralReward).filter(ReferralReward.is_deleted == False)
    
    if customer_id:
        query = query.filter(ReferralReward.customer_id == customer_id)
    if referral_id:
        query = query.filter(ReferralReward.referral_id == referral_id)
    if status:
        query = query.filter(ReferralReward.status == status)
    if reward_type:
        query = query.filter(ReferralReward.reward_type == reward_type)
    
    rewards = query.offset(skip).limit(limit).all()
    return rewards


@router.get("/rewards/{reward_id}", response_model=ReferralRewardInDB)
def get_referral_reward(
    *,
    db: Session = Depends(deps.get_db),
    reward_id: int = Path(..., gt=0, description="reward_id"),
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["view_referrals"]))
) -> Any:
    """
    获取推荐奖励记录详情
    """
    reward = db.query(ReferralReward).filter(
        ReferralReward.id == reward_id,
        ReferralReward.is_deleted == False
    ).first()
    
    if not reward:
        raise HTTPException(status_code=404, detail="奖励记录不存在")
    
    return reward


@router.put("/rewards/{reward_id}", response_model=ReferralRewardInDB)
def update_referral_reward(
    *,
    db: Session = Depends(deps.get_db),
    reward_id: int = Path(..., gt=0, description="reward_id"),
    reward_in: ReferralRewardUpdate,
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["manage_referrals"]))
) -> Any:
    """
    更新推荐奖励记录状态
    """
    reward = db.query(ReferralReward).filter(
        ReferralReward.id == reward_id,
        ReferralReward.is_deleted == False
    ).first()
    
    if not reward:
        raise HTTPException(status_code=404, detail="奖励记录不存在")
    
    update_data = reward_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(reward, field, value)
    
    # 如果状态变更为已发放，设置发放时间
    if reward_in.status == "issued" and not reward.issued_time:
        reward.issued_time = datetime.utcnow()
    
    db.add(reward)
    db.commit()
    db.refresh(reward)
    
    return reward


@router.post("/issue-reward/{referral_id}", response_model=ReferralRewardInDB)
def issue_referral_reward(
    *,
    db: Session = Depends(deps.get_db),
    referral_id: int = Path(..., gt=0, description="referral_id"),
    trigger_event: str = Query(..., description="触发事件类型"),
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["manage_referrals"]))
) -> Any:
    """
    根据推荐关系和触发事件发放奖励
    """
    # 检查推荐关系是否存在
    relationship = db.query(ReferralRelationship).filter(
        ReferralRelationship.id == referral_id,
        ReferralRelationship.status == "confirmed",
        ReferralRelationship.is_deleted == False
    ).first()
    
    if not relationship:
        raise HTTPException(status_code=404, detail="推荐关系不存在或未确认")
    
    # 查找对应的奖励设置
    setting = db.query(ReferralSetting).filter(
        ReferralSetting.event_type == trigger_event,
        ReferralSetting.is_active == True,
        ReferralSetting.is_deleted == False
    ).first()
    
    if not setting:
        raise HTTPException(status_code=404, detail="未找到对应的奖励设置")
    
    # 检查设置是否在有效期内
    now = datetime.utcnow()
    if (setting.start_date and setting.start_date > now) or (setting.end_date and setting.end_date < now):
        raise HTTPException(status_code=400, detail="奖励设置不在有效期内")
    
    # 检查是否已经发放过该事件的奖励
    existing_reward = db.query(ReferralReward).filter(
        ReferralReward.referral_id == referral_id,
        ReferralReward.trigger_event == trigger_event,
        ReferralReward.is_deleted == False
    ).first()
    
    if existing_reward:
        raise HTTPException(status_code=400, detail="该推荐关系已发放过此类型的奖励")
    
    # 创建奖励记录
    reward = ReferralReward(
        referral_id=referral_id,
        customer_id=relationship.referrer_id,  # 奖励给推荐人
        reward_type=setting.reward_type,
        reward_value=setting.reward_value,
        reward_detail=setting.reward_detail,
        trigger_event=trigger_event,
        status="pending"  # 初始状态为待发放
    )
    
    db.add(reward)
    db.commit()
    db.refresh(reward)
    
    # 根据奖励类型进行实际发放
    try:
        if reward.reward_type == "points":
            # 发放积分
            customer = db.query(models.Customer).filter(models.Customer.id == relationship.referrer_id).first()
            if customer:
                # 创建积分记录
                point_record = models.PointRecord(
                    customer_id=customer.id,
                    store_id=current_employee.store_id,  # 使用当前员工的门店ID
                    type="earn",
                    points=int(reward.reward_value),
                    points_before=customer.points,
                    points_after=customer.points + int(reward.reward_value),
                    remark=f"推荐奖励 - {trigger_event}"
                )
                
                # 更新客户积分
                customer.points += int(reward.reward_value)
                
                db.add(point_record)
                db.add(customer)
        
        elif reward.reward_type == "balance":
            # 发放储值余额
            card = db.query(models.MemberCard).filter(
                models.MemberCard.customer_id == relationship.referrer_id,
                models.MemberCard.status == "active"
            ).first()
            
            if card:
                # 创建交易记录
                transaction = models.CardTransaction(
                    card_id=card.id,
                    store_id=current_employee.store_id,
                    employee_id=current_employee.id,
                    type="recharge",
                    amount=float(reward.reward_value),
                    balance_before=float(card.balance),
                    balance_after=float(card.balance) + float(reward.reward_value),
                    remark=f"推荐奖励 - {trigger_event}"
                )
                
                # 更新卡余额
                card.balance += float(reward.reward_value)
                
                db.add(transaction)
                db.add(card)
        
        elif reward.reward_type == "coupon":
            # 发放优惠券，需要优惠券模板ID
            if not reward.reward_detail or "template_id" not in reward.reward_detail:
                raise ValueError("缺少优惠券模板ID")
            
            template_id = reward.reward_detail["template_id"]
            template = db.query(models.CouponTemplate).filter(models.CouponTemplate.id == template_id).first()
            
            if not template:
                raise ValueError("优惠券模板不存在")
            
            # 生成优惠券码
            from app.api.api_v1.endpoints.coupons import generate_coupon_code
            code = generate_coupon_code()
            
            # 计算有效期
            valid_start = datetime.utcnow()
            valid_end = None
            if template.valid_days:
                from datetime import timedelta
                valid_end = valid_start + timedelta(days=template.valid_days)
            elif template.valid_end:
                valid_end = template.valid_end
            
            # 创建优惠券
            coupon = models.Coupon(
                code=code,
                template_id=template.id,
                customer_id=relationship.referrer_id,
                status="unused",
                issue_type="referral",
                valid_start=valid_start,
                valid_end=valid_end
            )
            
            # 更新模板发放数量
            template.issued_count += 1
            
            db.add(coupon)
            db.add(template)
        
        # 更新奖励状态为已发放
        reward.status = "issued"
        reward.issued_time = datetime.utcnow()
        db.add(reward)
        
        db.commit()
        db.refresh(reward)
        
        return reward
    
    except Exception as e:
        db.rollback()
        # 更新奖励状态为失败
        reward.status = "failed"
        db.add(reward)
        db.commit()
        
        raise HTTPException(status_code=500, detail=f"奖励发放失败: {str(e)}")


@router.post("/settings/", response_model=ReferralSettingInDB)
def create_referral_setting(
    *,
    db: Session = Depends(deps.get_db),
    setting_in: ReferralSettingCreate,
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["manage_referrals"]))
) -> Any:
    """
    创建推荐奖励设置
    """
    # 检查是否已存在相同事件类型的设置
    existing_setting = db.query(ReferralSetting).filter(
        ReferralSetting.event_type == setting_in.event_type,
        ReferralSetting.is_deleted == False
    ).first()
    
    if existing_setting:
        raise HTTPException(status_code=400, detail="已存在相同事件类型的奖励设置")
    
    # 创建设置
    setting = ReferralSetting(
        event_type=setting_in.event_type,
        reward_type=setting_in.reward_type,
        reward_value=setting_in.reward_value,
        reward_detail=setting_in.reward_detail,
        is_active=setting_in.is_active,
        start_date=setting_in.start_date,
        end_date=setting_in.end_date
    )
    
    db.add(setting)
    db.commit()
    db.refresh(setting)
    
    return setting


@router.get("/settings/", response_model=List[ReferralSettingInDB])
def get_referral_settings(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    is_active: Optional[bool] = None,
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["view_referrals"]))
) -> Any:
    """
    获取推荐奖励设置列表
    """
    query = db.query(ReferralSetting).filter(ReferralSetting.is_deleted == False)
    
    if is_active is not None:
        query = query.filter(ReferralSetting.is_active == is_active)
    
    settings = query.offset(skip).limit(limit).all()
    return settings


@router.get("/settings/{setting_id}", response_model=ReferralSettingInDB)
def get_referral_setting(
    *,
    db: Session = Depends(deps.get_db),
    setting_id: int = Path(..., gt=0, description="setting_id"),
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["view_referrals"]))
) -> Any:
    """
    获取推荐奖励设置详情
    """
    setting = db.query(ReferralSetting).filter(
        ReferralSetting.id == setting_id,
        ReferralSetting.is_deleted == False
    ).first()
    
    if not setting:
        raise HTTPException(status_code=404, detail="奖励设置不存在")
    
    return setting


@router.put("/settings/{setting_id}", response_model=ReferralSettingInDB)
def update_referral_setting(
    *,
    db: Session = Depends(deps.get_db),
    setting_id: int = Path(..., gt=0, description="setting_id"),
    setting_in: ReferralSettingUpdate,
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["manage_referrals"]))
) -> Any:
    """
    更新推荐奖励设置
    """
    setting = db.query(ReferralSetting).filter(
        ReferralSetting.id == setting_id,
        ReferralSetting.is_deleted == False
    ).first()
    
    if not setting:
        raise HTTPException(status_code=404, detail="奖励设置不存在")
    
    update_data = setting_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(setting, field, value)
    
    db.add(setting)
    db.commit()
    db.refresh(setting)
    
    return setting


@router.delete("/settings/{setting_id}", response_model=ReferralSettingInDB)
def delete_referral_setting(
    *,
    db: Session = Depends(deps.get_db),
    setting_id: int = Path(..., gt=0, description="setting_id"),
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["manage_referrals"]))
) -> Any:
    """
    删除推荐奖励设置
    """
    setting = db.query(ReferralSetting).filter(
        ReferralSetting.id == setting_id,
        ReferralSetting.is_deleted == False
    ).first()
    
    if not setting:
        raise HTTPException(status_code=404, detail="奖励设置不存在")
    
    setting.is_deleted = True
    db.add(setting)
    db.commit()

    return setting


class ReferralCodeRequest(BaseModel):
    """推荐码生成请求模型"""
    customer_id: int


class UseReferralCodeRequest(BaseModel):
    """使用推荐码请求模型"""
    referral_code: str
    referee_phone: str
    referee_name: str


class FirstOrderRequest(BaseModel):
    """首单处理请求模型"""
    referee_phone: str
    order_amount: float


@router.post("/generate-code")
def generate_referral_code(
    request: ReferralCodeRequest,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_active_employee)
):
    """
    生成推荐码
    """
    try:
        from app.services.referral_reward_system import ReferralRewardSystem

        service = ReferralRewardSystem(db)
        result = service.generate_referral_code(request.customer_id)

        return {
            "success": True,
            "data": result
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成推荐码失败: {str(e)}")


@router.post("/use-code")
def use_referral_code(
    request: UseReferralCodeRequest,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_active_employee)
):
    """
    使用推荐码
    """
    try:
        from app.services.referral_reward_system import ReferralRewardSystem

        service = ReferralRewardSystem(db)
        result = service.use_referral_code(
            referral_code=request.referral_code,
            referee_phone=request.referee_phone,
            referee_name=request.referee_name
        )

        return {
            "success": True,
            "data": result
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"使用推荐码失败: {str(e)}")


@router.get("/statistics/{customer_id}")
def get_referral_statistics(
    customer_id: int,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_active_employee)
):
    """
    获取推荐统计
    """
    try:
        from app.services.referral_reward_system import ReferralRewardSystem

        service = ReferralRewardSystem(db)
        result = service.get_referral_statistics(customer_id)

        return {
            "success": True,
            "data": result
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取推荐统计失败: {str(e)}")


@router.get("/reward-rules")
def get_reward_rules(
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_active_employee)
):
    """
    获取奖励规则
    """
    try:
        from app.services.referral_reward_system import ReferralRewardSystem

        service = ReferralRewardSystem(db)
        result = service.get_reward_rules()

        return {
            "success": True,
            "data": result
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取奖励规则失败: {str(e)}")


@router.get("/leaderboard")
def get_referral_leaderboard(
    limit: int = Query(10, description="排行榜数量"),
    period: str = Query("month", description="统计周期: month, quarter, year"),
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_active_employee)
):
    """
    获取推荐排行榜
    """
    try:
        # 这里应该从数据库查询真实数据
        # 现在返回模拟数据

        leaderboard = [
            {
                "rank": 1,
                "customer_id": 101,
                "customer_name": "张三",
                "total_referrals": 15,
                "successful_referrals": 12,
                "total_rewards": 1200.0,
                "success_rate": 80.0
            },
            {
                "rank": 2,
                "customer_id": 102,
                "customer_name": "李四",
                "total_referrals": 12,
                "successful_referrals": 10,
                "total_rewards": 950.0,
                "success_rate": 83.3
            },
            {
                "rank": 3,
                "customer_id": 103,
                "customer_name": "王五",
                "total_referrals": 10,
                "successful_referrals": 9,
                "total_rewards": 850.0,
                "success_rate": 90.0
            }
        ]

        return {
            "success": True,
            "data": {
                "leaderboard": leaderboard[:limit],
                "period": period,
                "total_count": len(leaderboard),
                "statistics": {
                    "total_referrers": len(leaderboard),
                    "total_referrals": sum(item["total_referrals"] for item in leaderboard),
                    "total_rewards": sum(item["total_rewards"] for item in leaderboard),
                    "avg_success_rate": sum(item["success_rate"] for item in leaderboard) / len(leaderboard)
                }
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取推荐排行榜失败: {str(e)}")