from typing import List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.api import deps
from app.crud.cashier import group_buying_platform
from app.schemas.cashier import GroupBuyingPlatformCreate, GroupBuyingPlatformUpdate
from app.models.cashier import GroupBuyingPlatform

router = APIRouter()


@router.get("/", response_model=List[dict])
def get_group_buying_platforms(
    db: Session = Depends(deps.get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    is_active: Optional[bool] = Query(None, description="是否激活")
) -> Any:
    """
    获取团购平台列表
    """
    platforms = group_buying_platform.get_multi(db, skip=skip, limit=limit, is_active=is_active)
    
    result = []
    for platform in platforms:
        result.append({
            "id": platform.id,
            "name": platform.name,
            "platform_type": platform.platform_code,  # 使用正确的字段名
            "api_config": platform.api_config,
            "verification_method": platform.api_config.get('verification_method', 'api'),
            "auto_verification": platform.api_config.get('auto_verification', False),
            "is_active": platform.is_active,
            "sort_order": platform.sort_order,
            "created_at": platform.created_at,
            "updated_at": platform.updated_at,
            # 添加统计数据（模拟）
            "today_verifications": 0,
            "month_verifications": 0
        })
    
    return result


@router.get("/{platform_id}", response_model=dict)
def get_group_buying_platform(
    platform_id: int,
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    获取单个团购平台
    """
    platform = group_buying_platform.get(db, id=platform_id)
    if not platform:
        raise HTTPException(status_code=404, detail="团购平台不存在")
    
    return {
        "id": platform.id,
        "name": platform.name,
        "platform_type": platform.platform_code,  # 使用正确的字段名
        "api_config": platform.api_config,
        "verification_method": platform.api_config.get('verification_method', 'api'),
        "auto_verification": platform.api_config.get('auto_verification', False),
        "is_active": platform.is_active,
        "sort_order": platform.sort_order,
        "created_at": platform.created_at,
        "updated_at": platform.updated_at
    }


@router.post("/", response_model=dict)
def create_group_buying_platform(
    *,
    db: Session = Depends(deps.get_db),
    platform_in: GroupBuyingPlatformCreate
) -> Any:
    """
    创建团购平台
    """
    platform = group_buying_platform.create(db, obj_in=platform_in)
    
    return {
        "id": platform.id,
        "name": platform.name,
        "platform_type": platform.platform_type,
        "api_config": platform.api_config,
        "verification_method": platform.verification_method,
        "auto_verification": platform.auto_verification,
        "is_active": platform.is_active,
        "sort_order": platform.sort_order,
        "created_at": platform.created_at,
        "updated_at": platform.updated_at
    }


@router.put("/{platform_id}", response_model=dict)
def update_group_buying_platform(
    *,
    db: Session = Depends(deps.get_db),
    platform_id: int,
    platform_in: GroupBuyingPlatformUpdate
) -> Any:
    """
    更新团购平台
    """
    platform_obj = group_buying_platform.get(db, id=platform_id)
    if not platform_obj:
        raise HTTPException(status_code=404, detail="团购平台不存在")
    
    platform = group_buying_platform.update(db, db_obj=platform_obj, obj_in=platform_in)
    
    return {
        "id": platform.id,
        "name": platform.name,
        "platform_type": platform.platform_type,
        "api_config": platform.api_config,
        "verification_method": platform.verification_method,
        "auto_verification": platform.auto_verification,
        "is_active": platform.is_active,
        "sort_order": platform.sort_order,
        "created_at": platform.created_at,
        "updated_at": platform.updated_at
    }


@router.delete("/{platform_id}")
def delete_group_buying_platform(
    *,
    db: Session = Depends(deps.get_db),
    platform_id: int
) -> Any:
    """
    删除团购平台
    """
    platform_obj = group_buying_platform.get(db, id=platform_id)
    if not platform_obj:
        raise HTTPException(status_code=404, detail="团购平台不存在")
    
    group_buying_platform.remove(db, id=platform_id)
    return {"message": "团购平台删除成功"}


@router.post("/{platform_id}/toggle")
def toggle_group_buying_platform(
    *,
    db: Session = Depends(deps.get_db),
    platform_id: int
) -> Any:
    """
    切换团购平台启用状态
    """
    platform_obj = group_buying_platform.get(db, id=platform_id)
    if not platform_obj:
        raise HTTPException(status_code=404, detail="团购平台不存在")
    
    update_data = GroupBuyingPlatformUpdate(is_active=not platform_obj.is_active)
    platform = group_buying_platform.update(db, db_obj=platform_obj, obj_in=update_data)
    
    return {
        "id": platform.id,
        "name": platform.name,
        "platform_type": platform.platform_type,
        "api_config": platform.api_config,
        "verification_method": platform.verification_method,
        "auto_verification": platform.auto_verification,
        "is_active": platform.is_active,
        "sort_order": platform.sort_order,
        "created_at": platform.created_at,
        "updated_at": platform.updated_at
    }


@router.post("/test-connection")
def test_platform_connection(
    *,
    db: Session = Depends(deps.get_db),
    platform_type: str,
    api_config: dict
) -> Any:
    """
    测试平台连接
    """
    try:
        # 这里可以添加具体的连接测试逻辑
        # 比如测试抖音、美团等平台的API连接
        
        if platform_type == "douyin":
            # 验证抖音配置
            required_fields = ["app_id", "app_secret"]
            for field in required_fields:
                if not api_config.get(field):
                    raise HTTPException(status_code=400, detail=f"缺少必需的配置项: {field}")
        
        elif platform_type == "meituan":
            # 验证美团配置
            required_fields = ["app_id", "app_secret", "merchant_id"]
            for field in required_fields:
                if not api_config.get(field):
                    raise HTTPException(status_code=400, detail=f"缺少必需的配置项: {field}")
        
        return {"status": "success", "message": "连接测试通过"}
        
    except Exception as e:
        return {"status": "error", "message": str(e)}


@router.get("/{platform_id}/stats")
def get_platform_stats(
    platform_id: int,
    db: Session = Depends(deps.get_db),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期")
) -> Any:
    """
    获取平台统计数据
    """
    platform = group_buying_platform.get(db, id=platform_id)
    if not platform:
        raise HTTPException(status_code=404, detail="团购平台不存在")
    
    # 这里可以添加真实的统计查询逻辑
    # 目前返回模拟数据
    return {
        "platform_id": platform_id,
        "platform_name": platform.name,
        "today_verifications": 15,
        "month_verifications": 342,
        "total_verifications": 1250,
        "success_rate": 98.5,
        "avg_verification_time": 2.3  # 秒
    }
