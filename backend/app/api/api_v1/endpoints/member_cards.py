from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from app import schemas, models
from app.api import deps
from app.schemas.customer import (
    MemberCardCreate, MemberCardUpdate, MemberCardResponse, MemberCardListResponse,
    CardTransactionCreate, CardTransactionResponse, CardTransactionListResponse
)

router = APIRouter()


@router.post("/", response_model=MemberCardResponse, status_code=status.HTTP_201_CREATED)
def create_member_card(
    *,
    db: Session = Depends(deps.get_db),
    card_in: MemberCardCreate,
    current_user: models.Employee = Depends(deps.get_current_user),
) -> Any:
    """
    创建会员卡
    """
    # 检查权限
    if current_user.role not in ["admin", "manager"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限创建会员卡",
        )
    
    # 检查客户是否存在
    customer = db.query(models.Customer).filter(
        models.Customer.id == card_in.customer_id,
        models.Customer.is_deleted == False
    ).first()
    
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在",
        )
    
    # 检查会员等级是否存在
    member_level = db.query(models.MemberLevel).filter(
        models.MemberLevel.id == card_in.level_id,
        models.MemberLevel.is_deleted == False
    ).first()
    
    if not member_level:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会员等级不存在",
        )
    
    # 检查卡号是否已存在
    existing_card = db.query(models.MemberCard).filter(models.MemberCard.card_number == card_in.card_number).first()
    if existing_card:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="卡号已存在",
        )
    
    # 设置默认过期时间（一年后）
    expire_time = card_in.expire_time or datetime.now() + timedelta(days=365)
    
    # 创建会员卡
    member_card = models.MemberCard(
        customer_id=card_in.customer_id,
        card_number=card_in.card_number,
        level_id=card_in.level_id,
        balance=card_in.balance,
        points=card_in.points,
        status=card_in.status,
        expire_time=expire_time,
    )
    db.add(member_card)
    db.commit()
    db.refresh(member_card)
    return member_card


# 添加两个路由以支持不同的路径格式
@router.get("/", response_model=MemberCardListResponse)
@router.get("", response_model=MemberCardListResponse)
def read_member_cards(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.Employee = Depends(deps.get_current_user),
    # 支持两种分页方式
    skip: int = Query(0, description="偏移量"),
    limit: int = Query(100, description="限制数量"),
    page: Optional[int] = Query(None, description="页码"),
    size: Optional[int] = Query(None, description="页面大小"),
    # 搜索条件
    customer_id: Optional[str] = Query(None, description="客户ID"),
    level_id: Optional[str] = Query(None, description="等级ID"),
    status: Optional[str] = Query(None, description="状态"),
    card_number: Optional[str] = Query(None, description="卡号"),
    customer_name: Optional[str] = Query(None, description="客户名称"),
) -> Any:
    """
    获取会员卡列表
    """
    try:
        # 处理分页参数
        if page is not None and size is not None:
            skip = (page - 1) * size
            limit = size

        # 过滤和转换参数
        card_number = card_number.strip() if card_number else None
        customer_name = customer_name.strip() if customer_name else None
        status = status.strip() if status else None

        # 转换ID参数
        customer_id_int = None
        if customer_id and customer_id.strip():
            try:
                customer_id_int = int(customer_id.strip())
            except ValueError:
                pass

        level_id_int = None
        if level_id and level_id.strip():
            try:
                level_id_int = int(level_id.strip())
            except ValueError:
                pass

        # 记录参数日志
        print(f"API参数: page={page}, size={size}, skip={skip}, limit={limit}")
        print(f"搜索参数: card_number={card_number}, customer_name={customer_name}, level_id={level_id_int}, status={status}, customer_id={customer_id_int}")

        # 查询会员卡
        query = db.query(models.MemberCard).filter(models.MemberCard.is_deleted == False)

        # 按客户ID筛选
        if customer_id_int:
            query = query.filter(models.MemberCard.customer_id == customer_id_int)

        # 按卡号筛选
        if card_number:
            query = query.filter(models.MemberCard.card_number.ilike(f"%{card_number}%"))

        # 按客户名称筛选
        if customer_name:
            query = query.join(models.Customer).filter(
                models.Customer.name.ilike(f"%{customer_name}%")
            )

        # 按等级筛选
        if level_id_int:
            query = query.filter(models.MemberCard.level_id == level_id_int)

        # 按状态筛选
        if status:
            query = query.filter(models.MemberCard.status == status)

        # 获取总数
        total = query.count()

        # 分页
        member_cards = query.order_by(models.MemberCard.created_at.desc()).offset(skip).limit(limit).all()

        print(f"查询结果: total={total}, items_count={len(member_cards)}")
        return {"total": total, "items": member_cards}

    except Exception as e:
        print(f"会员卡API错误: {e}")
        import traceback
        traceback.print_exc()
        raise


@router.get("/{card_id}", response_model=MemberCardResponse)
def read_member_card(
    *,
    db: Session = Depends(deps.get_db),
    card_id: int,
    current_user: models.Employee = Depends(deps.get_current_user),
) -> Any:
    """
    获取会员卡详情
    """
    member_card = db.query(models.MemberCard).filter(
        models.MemberCard.id == card_id,
        models.MemberCard.is_deleted == False
    ).first()
    
    if not member_card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会员卡不存在",
        )
    
    return member_card


@router.put("/{card_id}", response_model=MemberCardResponse)
def update_member_card(
    *,
    db: Session = Depends(deps.get_db),
    card_id: int,
    card_in: MemberCardUpdate,
    current_user: models.Employee = Depends(deps.get_current_user),
) -> Any:
    """
    更新会员卡
    """
    # 检查权限
    if current_user.role not in ["admin", "manager"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限更新会员卡",
        )
    
    # 获取会员卡
    member_card = db.query(models.MemberCard).filter(
        models.MemberCard.id == card_id,
        models.MemberCard.is_deleted == False
    ).first()
    
    if not member_card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会员卡不存在",
        )
    
    # 检查会员等级是否存在
    if card_in.level_id is not None:
        member_level = db.query(models.MemberLevel).filter(
            models.MemberLevel.id == card_in.level_id,
            models.MemberLevel.is_deleted == False
        ).first()
        
        if not member_level:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="会员等级不存在",
            )
    
    # 更新会员卡
    update_data = card_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(member_card, field, value)
    
    db.commit()
    db.refresh(member_card)
    return member_card


@router.delete("/{card_id}", response_model=MemberCardResponse)
def delete_member_card(
    *,
    db: Session = Depends(deps.get_db),
    card_id: int,
    current_user: models.Employee = Depends(deps.get_current_user),
) -> Any:
    """
    删除会员卡（软删除）
    """
    # 检查权限
    if current_user.role not in ["admin", "manager"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除会员卡",
        )
    
    # 获取会员卡
    member_card = db.query(models.MemberCard).filter(
        models.MemberCard.id == card_id,
        models.MemberCard.is_deleted == False
    ).first()
    
    if not member_card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会员卡不存在",
        )
    
    # 检查是否有交易记录
    transactions_count = db.query(models.CardTransaction).filter(
        models.CardTransaction.card_id == card_id
    ).count()
    
    if transactions_count > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"该会员卡已有 {transactions_count} 条交易记录，无法删除",
        )
    
    # 软删除
    member_card.is_deleted = True
    db.commit()
    db.refresh(member_card)
    return member_card


@router.post("/{card_id}/recharge", response_model=CardTransactionResponse)
def recharge_card(
    *,
    db: Session = Depends(deps.get_db),
    card_id: int,
    amount: float,
    remark: Optional[str] = None,
    current_user: models.Employee = Depends(deps.get_current_user),
) -> Any:
    """
    会员卡充值
    """
    # 检查权限
    if current_user.role not in ["admin", "manager", "staff"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限进行充值操作",
        )
    
    # 检查金额是否合法
    if amount <= 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="充值金额必须大于0",
        )
    
    # 获取会员卡
    member_card = db.query(models.MemberCard).filter(
        models.MemberCard.id == card_id,
        models.MemberCard.is_deleted == False
    ).first()
    
    if not member_card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会员卡不存在",
        )
    
    # 检查会员卡状态
    if member_card.status != "active":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"会员卡状态为 {member_card.status}，无法充值",
        )
    
    # 计算充值后余额
    balance_before = member_card.balance
    balance_after = balance_before + amount
    
    # 更新会员卡余额
    member_card.balance = balance_after
    
    # 创建交易记录
    transaction = models.CardTransaction(
        card_id=card_id,
        store_id=current_user.store_id,
        employee_id=current_user.id,
        type="recharge",
        amount=amount,
        balance_before=balance_before,
        balance_after=balance_after,
        remark=remark,
    )
    
    db.add(transaction)
    db.commit()
    db.refresh(transaction)
    
    return transaction


@router.post("/{card_id}/consume", response_model=CardTransactionResponse)
def consume_card(
    *,
    db: Session = Depends(deps.get_db),
    card_id: int,
    amount: float,
    order_id: Optional[int] = None,
    remark: Optional[str] = None,
    current_user: models.Employee = Depends(deps.get_current_user),
) -> Any:
    """
    会员卡消费
    """
    # 检查权限
    if current_user.role not in ["admin", "manager", "staff"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限进行消费操作",
        )
    
    # 检查金额是否合法
    if amount <= 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="消费金额必须大于0",
        )
    
    # 获取会员卡
    member_card = db.query(models.MemberCard).filter(
        models.MemberCard.id == card_id,
        models.MemberCard.is_deleted == False
    ).first()
    
    if not member_card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会员卡不存在",
        )
    
    # 检查会员卡状态
    if member_card.status != "active":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"会员卡状态为 {member_card.status}，无法消费",
        )
    
    # 检查余额是否足够
    if member_card.balance < amount:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"会员卡余额不足，当前余额: {member_card.balance}",
        )
    
    # 计算消费后余额
    balance_before = member_card.balance
    balance_after = balance_before - amount
    
    # 更新会员卡余额
    member_card.balance = balance_after
    
    # 创建交易记录
    transaction = models.CardTransaction(
        card_id=card_id,
        order_id=order_id,
        store_id=current_user.store_id,
        employee_id=current_user.id,
        type="consume",
        amount=amount,
        balance_before=balance_before,
        balance_after=balance_after,
        remark=remark,
    )
    
    db.add(transaction)
    db.commit()
    db.refresh(transaction)
    
    return transaction


@router.get("/{card_id}/transactions", response_model=CardTransactionListResponse)
def read_card_transactions(
    *,
    db: Session = Depends(deps.get_db),
    card_id: int,
    current_user: models.Employee = Depends(deps.get_current_user),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    获取会员卡交易记录
    """
    # 获取会员卡
    member_card = db.query(models.MemberCard).filter(
        models.MemberCard.id == card_id,
        models.MemberCard.is_deleted == False
    ).first()
    
    if not member_card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会员卡不存在",
        )
    
    # 查询交易记录
    query = db.query(models.CardTransaction).filter(models.CardTransaction.card_id == card_id)
    
    # 获取总数
    total = query.count()
    
    # 分页
    transactions = query.order_by(models.CardTransaction.created_at.desc()).offset(skip).limit(limit).all()
    
    return {"total": total, "items": transactions} 