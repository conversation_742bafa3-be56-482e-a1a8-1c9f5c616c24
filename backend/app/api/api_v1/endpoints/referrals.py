from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from sqlalchemy import func, or_, case
from datetime import datetime
import random
import string

from app.db.session import get_db
from app.api import deps
from app.schemas import referral as schemas
from app.models.referral import ReferralRelationship, ReferralCode, ReferralReward, ReferralSetting
import app.models as models

router = APIRouter()


def generate_referral_code(length: int = 8) -> str:
    """
    生成随机推荐码
    """
    chars = string.ascii_uppercase + string.digits
    return ''.join(random.choice(chars) for _ in range(length))


@router.post("/relationships/", response_model=schemas.ReferralRelationshipInDB)
def create_referral_relationship(
    *,
    db: Session = Depends(deps.get_db),
    relationship_in: schemas.ReferralRelationshipCreate,
    current_employee: models.Employee = Depends(deps.get_current_employee)
) -> Any:
    """
    创建推荐关系
    """
    # 检查推荐人是否存在
    referrer = db.query(models.Customer).filter(models.Customer.id == relationship_in.referrer_id).first()
    if not referrer:
        raise HTTPException(status_code=404, detail="推荐人不存在")
    
    # 检查被推荐人是否存在
    referred = db.query(models.Customer).filter(models.Customer.id == relationship_in.referred_id).first()
    if not referred:
        raise HTTPException(status_code=404, detail="被推荐人不存在")
    
    # 检查是否已存在推荐关系
    existing_relationship = db.query(ReferralRelationship).filter(
        ReferralRelationship.referred_id == relationship_in.referred_id,
        ReferralRelationship.is_deleted == False
    ).first()
    
    if existing_relationship:
        raise HTTPException(status_code=400, detail="该客户已有推荐人")
    
    # 创建推荐关系
    relationship = ReferralRelationship(
        referrer_id=relationship_in.referrer_id,
        referred_id=relationship_in.referred_id,
        referral_code=relationship_in.referral_code,
        status=relationship_in.status,
        binding_time=datetime.utcnow()
    )
    
    db.add(relationship)
    db.commit()
    db.refresh(relationship)
    
    return relationship


@router.post("/by-code/", response_model=schemas.ReferralRelationshipInDB)
def create_referral_by_code(
    *,
    db: Session = Depends(deps.get_db),
    request_in: schemas.CreateReferralRequest,
    current_employee: models.Employee = Depends(deps.get_current_employee)
) -> Any:
    """
    通过推荐码创建推荐关系
    """
    # 查找推荐码
    referral_code = db.query(ReferralCode).filter(
        ReferralCode.code == request_in.referrer_code,
        ReferralCode.is_active == True,
        ReferralCode.is_deleted == False
    ).first()
    
    if not referral_code:
        raise HTTPException(status_code=404, detail="推荐码不存在或已失效")
    
    # 检查推荐码是否过期
    if referral_code.expiry_date and referral_code.expiry_date < datetime.utcnow():
        raise HTTPException(status_code=400, detail="推荐码已过期")
    
    # 检查使用次数是否超限
    if referral_code.usage_limit > 0 and referral_code.usage_count >= referral_code.usage_limit:
        raise HTTPException(status_code=400, detail="推荐码使用次数已达上限")
    
    # 检查被推荐人是否存在
    referred = db.query(models.Customer).filter(models.Customer.id == request_in.referred_id).first()
    if not referred:
        raise HTTPException(status_code=404, detail="被推荐人不存在")
    
    # 检查是否已存在推荐关系
    existing_relationship = db.query(ReferralRelationship).filter(
        ReferralRelationship.referred_id == request_in.referred_id,
        ReferralRelationship.is_deleted == False
    ).first()
    
    if existing_relationship:
        raise HTTPException(status_code=400, detail="该客户已有推荐人")
    
    # 创建推荐关系
    relationship = ReferralRelationship(
        referrer_id=referral_code.customer_id,
        referred_id=request_in.referred_id,
        referral_code=referral_code.code,
        status="confirmed",
        binding_time=datetime.utcnow()
    )
    
    # 更新推荐码使用次数
    referral_code.usage_count += 1
    
    db.add(relationship)
    db.add(referral_code)
    db.commit()
    db.refresh(relationship)
    
    return relationship


@router.get("/relationships/", response_model=List[schemas.ReferralRelationshipWithNames])
def get_referral_relationships(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    referrer_id: Optional[int] = None,
    referred_id: Optional[int] = None,
    status: Optional[str] = None,
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["view_referrals"]))
) -> Any:
    """
    获取推荐关系列表，支持按推荐人、被推荐人和状态筛选
    """
    query = db.query(
        ReferralRelationship,
        models.Customer.name.label("referrer_name"),
        models.Customer.name.label("referred_name")
    ).join(
        models.Customer, 
        ReferralRelationship.referrer_id == models.Customer.id
    ).join(
        models.Customer, 
        ReferralRelationship.referred_id == models.Customer.id,
        isouter=True
    ).filter(
        ReferralRelationship.is_deleted == False
    )
    
    if referrer_id:
        query = query.filter(ReferralRelationship.referrer_id == referrer_id)
    if referred_id:
        query = query.filter(ReferralRelationship.referred_id == referred_id)
    if status:
        query = query.filter(ReferralRelationship.status == status)
    
    results = query.offset(skip).limit(limit).all()
    
    relationships = []
    for result in results:
        relationship_dict = {
            **result[0].__dict__,
            "referrer_name": result[1],
            "referred_name": result[2]
        }
        relationships.append(relationship_dict)
    
    return relationships


@router.get("/relationships/{relationship_id}", response_model=schemas.ReferralRelationshipInDB)
def get_referral_relationship(
    *,
    db: Session = Depends(deps.get_db),
    relationship_id: int = Path(..., gt=0, description="relationship_id"),
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["view_referrals"]))
) -> Any:
    """
    获取推荐关系详情
    """
    relationship = db.query(ReferralRelationship).filter(
        ReferralRelationship.id == relationship_id,
        ReferralRelationship.is_deleted == False
    ).first()
    
    if not relationship:
        raise HTTPException(status_code=404, detail="推荐关系不存在")
    
    return relationship


@router.put("/relationships/{relationship_id}", response_model=schemas.ReferralRelationshipInDB)
def update_referral_relationship(
    *,
    db: Session = Depends(deps.get_db),
    relationship_id: int = Path(..., gt=0, description="relationship_id"),
    relationship_in: schemas.ReferralRelationshipUpdate,
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["manage_referrals"]))
) -> Any:
    """
    更新推荐关系状态
    """
    relationship = db.query(ReferralRelationship).filter(
        ReferralRelationship.id == relationship_id,
        ReferralRelationship.is_deleted == False
    ).first()
    
    if not relationship:
        raise HTTPException(status_code=404, detail="推荐关系不存在")
    
    update_data = relationship_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(relationship, field, value)
    
    db.add(relationship)
    db.commit()
    db.refresh(relationship)
    
    return relationship


@router.delete("/relationships/{relationship_id}", response_model=schemas.ReferralRelationshipInDB)
def delete_referral_relationship(
    *,
    db: Session = Depends(deps.get_db),
    relationship_id: int = Path(..., gt=0, description="relationship_id"),
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["manage_referrals"]))
) -> Any:
    """
    删除推荐关系
    """
    relationship = db.query(ReferralRelationship).filter(
        ReferralRelationship.id == relationship_id,
        ReferralRelationship.is_deleted == False
    ).first()
    
    if not relationship:
        raise HTTPException(status_code=404, detail="推荐关系不存在")
    
    relationship.is_deleted = True
    db.add(relationship)
    db.commit()
    
    return relationship


@router.post("/codes/", response_model=schemas.ReferralCodeInDB)
def create_referral_code(
    *,
    db: Session = Depends(deps.get_db),
    request_in: schemas.GenerateReferralCodeRequest,
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["manage_referrals"]))
) -> Any:
    """
    为客户生成推荐码
    """
    # 检查客户是否存在
    customer = db.query(models.Customer).filter(models.Customer.id == request_in.customer_id).first()
    if not customer:
        raise HTTPException(status_code=404, detail="客户不存在")
    
    # 生成唯一推荐码
    code = generate_referral_code()
    while db.query(ReferralCode).filter(ReferralCode.code == code).first():
        code = generate_referral_code()
    
    # 创建推荐码
    referral_code = ReferralCode(
        customer_id=request_in.customer_id,
        code=code,
        type=request_in.type,
        campaign_id=request_in.campaign_id,
        expiry_date=request_in.expiry_date,
        usage_limit=request_in.usage_limit
    )
    
    db.add(referral_code)
    db.commit()
    db.refresh(referral_code)
    
    return referral_code


@router.get("/codes/", response_model=List[schemas.ReferralCodeInDB])
def get_referral_codes(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    customer_id: Optional[int] = None,
    is_active: Optional[bool] = None,
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["view_referrals"]))
) -> Any:
    """
    获取推荐码列表，支持按客户和状态筛选
    """
    query = db.query(ReferralCode).filter(ReferralCode.is_deleted == False)
    
    if customer_id:
        query = query.filter(ReferralCode.customer_id == customer_id)
    if is_active is not None:
        query = query.filter(ReferralCode.is_active == is_active)
    
    codes = query.offset(skip).limit(limit).all()
    return codes


@router.get("/codes/{code}", response_model=schemas.ReferralCodeInDB)
def get_referral_code_by_code(
    *,
    db: Session = Depends(deps.get_db),
    code: str,
    current_employee: models.Employee = Depends(deps.get_current_employee)
) -> Any:
    """
    通过推荐码获取详情
    """
    referral_code = db.query(ReferralCode).filter(
        ReferralCode.code == code,
        ReferralCode.is_deleted == False
    ).first()
    
    if not referral_code:
        raise HTTPException(status_code=404, detail="推荐码不存在")
    
    return referral_code


@router.get("/customer/{customer_id}/stats", response_model=schemas.ReferralStats)
def get_customer_referral_stats(
    *,
    db: Session = Depends(deps.get_db),
    customer_id: int = Path(..., gt=0, description="客户ID"),
    current_employee: models.Employee = Depends(deps.get_current_employee)
) -> Any:
    """
    获取客户的推荐统计数据
    """
    # 检查客户是否存在
    customer = db.query(models.Customer).filter(models.Customer.id == customer_id).first()
    if not customer:
        raise HTTPException(status_code=404, detail="客户不存在")
    
    # 获取推荐总数
    total_referrals = db.query(func.count(ReferralRelationship.id)).filter(
        ReferralRelationship.referrer_id == customer_id,
        ReferralRelationship.is_deleted == False
    ).scalar() or 0
    
    # 获取成功推荐数
    successful_referrals = db.query(func.count(ReferralRelationship.id)).filter(
        ReferralRelationship.referrer_id == customer_id,
        ReferralRelationship.status == "confirmed",
        ReferralRelationship.is_deleted == False
    ).scalar() or 0
    
    # 获取待确认推荐数
    pending_referrals = db.query(func.count(ReferralRelationship.id)).filter(
        ReferralRelationship.referrer_id == customer_id,
        ReferralRelationship.status == "pending",
        ReferralRelationship.is_deleted == False
    ).scalar() or 0
    
    # 获取总奖励金额
    total_rewards = db.query(func.sum(ReferralReward.reward_value)).filter(
        ReferralReward.customer_id == customer_id,
        ReferralReward.status == "issued",
        ReferralReward.is_deleted == False
    ).scalar() or 0
    
    # 获取各类型奖励金额
    rewards_by_type = {}
    reward_types = db.query(
        ReferralReward.reward_type,
        func.sum(ReferralReward.reward_value)
    ).filter(
        ReferralReward.customer_id == customer_id,
        ReferralReward.status == "issued",
        ReferralReward.is_deleted == False
    ).group_by(ReferralReward.reward_type).all()
    
    for reward_type, amount in reward_types:
        rewards_by_type[reward_type] = float(amount)
    
    return {
        "total_referrals": total_referrals,
        "successful_referrals": successful_referrals,
        "pending_referrals": pending_referrals,
        "total_rewards": float(total_rewards),
        "rewards_by_type": rewards_by_type
    }


@router.get("/global-stats", response_model=schemas.ReferralGlobalStats)
def get_global_referral_stats(
    *,
    db: Session = Depends(deps.get_db),
    start_date: Optional[datetime] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[datetime] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    store_id: Optional[int] = Query(None, description="门店ID"),
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["view_referrals"]))
) -> Any:
    """
    获取全局推荐关系统计数据
    """
    # 构建基础查询条件
    relationship_query = db.query(ReferralRelationship).filter(
        ReferralRelationship.is_deleted == False
    )
    
    reward_query = db.query(ReferralReward).filter(
        ReferralReward.is_deleted == False
    )
    
    # 添加时间筛选
    if start_date:
        relationship_query = relationship_query.filter(ReferralRelationship.binding_time >= start_date)
        reward_query = reward_query.filter(ReferralReward.created_at >= start_date)
    
    if end_date:
        relationship_query = relationship_query.filter(ReferralRelationship.binding_time <= end_date)
        reward_query = reward_query.filter(ReferralReward.created_at <= end_date)
    
    # 添加门店筛选（通过客户关联的门店）
    if store_id:
        # 获取门店所有客户ID
        store_customers = db.query(models.Customer.id).join(
            models.Order, models.Customer.id == models.Order.customer_id
        ).filter(
            models.Order.store_id == store_id,
            models.Order.is_deleted == False
        ).distinct().subquery()
        
        relationship_query = relationship_query.filter(
            or_(
                ReferralRelationship.referrer_id.in_(store_customers),
                ReferralRelationship.referred_id.in_(store_customers)
            )
        )
        
        reward_query = reward_query.filter(
            ReferralReward.customer_id.in_(store_customers)
        )
    
    # 获取推荐关系统计
    total_relationships = relationship_query.count()
    confirmed_relationships = relationship_query.filter(ReferralRelationship.status == "confirmed").count()
    pending_relationships = relationship_query.filter(ReferralRelationship.status == "pending").count()
    rejected_relationships = relationship_query.filter(ReferralRelationship.status == "rejected").count()
    
    # 计算成功率
    success_rate = round(confirmed_relationships / total_relationships * 100, 2) if total_relationships > 0 else 0
    
    # 获取奖励统计
    total_rewards = reward_query.filter(ReferralReward.status == "issued").count()
    total_reward_amount = db.query(func.sum(ReferralReward.reward_value)).filter(
        ReferralReward.status == "issued",
        ReferralReward.is_deleted == False
    ).scalar() or 0
    
    # 按奖励类型分组统计
    rewards_by_type = {}
    reward_type_stats = db.query(
        ReferralReward.reward_type,
        func.count(ReferralReward.id).label("count"),
        func.sum(ReferralReward.reward_value).label("amount")
    ).filter(
        ReferralReward.status == "issued",
        ReferralReward.is_deleted == False
    )
    
    # 添加时间和门店筛选
    if start_date:
        reward_type_stats = reward_type_stats.filter(ReferralReward.created_at >= start_date)
    if end_date:
        reward_type_stats = reward_type_stats.filter(ReferralReward.created_at <= end_date)
    if store_id:
        reward_type_stats = reward_type_stats.filter(ReferralReward.customer_id.in_(store_customers))
    
    reward_type_stats = reward_type_stats.group_by(ReferralReward.reward_type).all()
    
    for reward_type, count, amount in reward_type_stats:
        rewards_by_type[reward_type] = {
            "count": count,
            "amount": float(amount) if amount else 0
        }
    
    # 按时间统计推荐趋势
    trend_data = []
    
    # 确定开始和结束日期
    actual_start_date = start_date if start_date else db.query(func.min(ReferralRelationship.binding_time)).scalar()
    actual_end_date = end_date if end_date else datetime.utcnow()
    
    if actual_start_date and actual_end_date:
        # 默认按月统计
        current_date = actual_start_date.replace(day=1)
        while current_date <= actual_end_date:
            next_month = current_date.replace(
                year=current_date.year + (current_date.month == 12),
                month=current_date.month % 12 + 1,
                day=1
            )
            
            # 查询该月的推荐数据
            monthly_query = relationship_query.filter(
                ReferralRelationship.binding_time >= current_date,
                ReferralRelationship.binding_time < next_month
            )
            
            monthly_total = monthly_query.count()
            monthly_confirmed = monthly_query.filter(ReferralRelationship.status == "confirmed").count()
            
            # 查询该月的奖励数据
            monthly_reward_query = reward_query.filter(
                ReferralReward.created_at >= current_date,
                ReferralReward.created_at < next_month,
                ReferralReward.status == "issued"
            )
            
            monthly_reward_amount = db.query(func.sum(ReferralReward.reward_value)).filter(
                ReferralReward.created_at >= current_date,
                ReferralReward.created_at < next_month,
                ReferralReward.status == "issued",
                ReferralReward.is_deleted == False
            ).scalar() or 0
            
            # 添加到趋势数据中
            if monthly_total > 0 or monthly_reward_amount > 0:
                trend_data.append({
                    "period": current_date.strftime("%Y-%m"),
                    "total_referrals": monthly_total,
                    "confirmed_referrals": monthly_confirmed,
                    "success_rate": round(monthly_confirmed / monthly_total * 100, 2) if monthly_total > 0 else 0,
                    "reward_amount": float(monthly_reward_amount)
                })
            
            current_date = next_month
    
    # 获取最活跃推荐人排行
    top_referrers = db.query(
        ReferralRelationship.referrer_id,
        models.Customer.name.label("referrer_name"),
        func.count(ReferralRelationship.id).label("referral_count"),
        func.sum(case(
            [(ReferralRelationship.status == "confirmed", 1)],
            else_=0
        )).label("confirmed_count")
    ).join(
        models.Customer, 
        ReferralRelationship.referrer_id == models.Customer.id
    ).filter(
        ReferralRelationship.is_deleted == False
    )
    
    # 添加时间筛选
    if start_date:
        top_referrers = top_referrers.filter(ReferralRelationship.binding_time >= start_date)
    if end_date:
        top_referrers = top_referrers.filter(ReferralRelationship.binding_time <= end_date)
    
    # 分组并获取前10名
    top_referrers = top_referrers.group_by(
        ReferralRelationship.referrer_id,
        models.Customer.name
    ).order_by(
        func.count(ReferralRelationship.id).desc()
    ).limit(10).all()
    
    # 转换为响应格式
    top_referrers_data = []
    for referrer_id, referrer_name, referral_count, confirmed_count in top_referrers:
        # 查询该推荐人获得的奖励总额
        referrer_reward_amount = db.query(func.sum(ReferralReward.reward_value)).filter(
            ReferralReward.customer_id == referrer_id,
            ReferralReward.status == "issued",
            ReferralReward.is_deleted == False
        )
        
        # 添加时间筛选
        if start_date:
            referrer_reward_amount = referrer_reward_amount.filter(ReferralReward.created_at >= start_date)
        if end_date:
            referrer_reward_amount = referrer_reward_amount.filter(ReferralReward.created_at <= end_date)
        
        referrer_reward_amount = referrer_reward_amount.scalar() or 0
        
        top_referrers_data.append({
            "referrer_id": referrer_id,
            "referrer_name": referrer_name,
            "referral_count": referral_count,
            "confirmed_count": confirmed_count,
            "success_rate": round(confirmed_count / referral_count * 100, 2) if referral_count > 0 else 0,
            "reward_amount": float(referrer_reward_amount)
        })
    
    return {
        "total_relationships": total_relationships,
        "confirmed_relationships": confirmed_relationships,
        "pending_relationships": pending_relationships,
        "rejected_relationships": rejected_relationships,
        "success_rate": success_rate,
        "total_rewards": total_rewards,
        "total_reward_amount": float(total_reward_amount),
        "rewards_by_type": rewards_by_type,
        "trend_data": trend_data,
        "top_referrers": top_referrers_data
    }


@router.get("/export-stats", response_model=List[schemas.ReferralExportData])
def export_referral_stats(
    *,
    db: Session = Depends(deps.get_db),
    start_date: Optional[datetime] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[datetime] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    store_id: Optional[int] = Query(None, description="门店ID"),
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["view_referrals"]))
) -> Any:
    """
    导出推荐关系统计数据，包括详细的推荐关系和奖励信息
    """
    # 构建查询
    query = db.query(
        ReferralRelationship,
        models.Customer.name.label("referrer_name"),
        models.Customer.phone.label("referrer_phone"),
        models.Customer.name.label("referred_name"),
        models.Customer.phone.label("referred_phone")
    ).join(
        models.Customer, 
        ReferralRelationship.referrer_id == models.Customer.id
    ).join(
        models.Customer, 
        ReferralRelationship.referred_id == models.Customer.id,
        isouter=True
    ).filter(
        ReferralRelationship.is_deleted == False
    )
    
    # 添加筛选条件
    if start_date:
        query = query.filter(ReferralRelationship.binding_time >= start_date)
    
    if end_date:
        query = query.filter(ReferralRelationship.binding_time <= end_date)
    
    if store_id:
        # 获取门店所有客户ID
        store_customers = db.query(models.Customer.id).join(
            models.Order, models.Customer.id == models.Order.customer_id
        ).filter(
            models.Order.store_id == store_id,
            models.Order.is_deleted == False
        ).distinct().subquery()
        
        query = query.filter(
            or_(
                ReferralRelationship.referrer_id.in_(store_customers),
                ReferralRelationship.referred_id.in_(store_customers)
            )
        )
    
    # 执行查询
    results = query.order_by(ReferralRelationship.binding_time.desc()).all()
    
    # 准备导出数据
    export_data = []
    for result in results:
        relationship = result[0]
        referrer_name = result[1]
        referrer_phone = result[2]
        referred_name = result[3]
        referred_phone = result[4]
        
        # 查询与该推荐关系相关的奖励
        rewards = db.query(ReferralReward).filter(
            ReferralReward.referral_id == relationship.id,
            ReferralReward.is_deleted == False
        ).all()
        
        reward_info = []
        for reward in rewards:
            reward_info.append({
                "reward_id": reward.id,
                "reward_type": reward.reward_type,
                "reward_value": float(reward.reward_value),
                "status": reward.status,
                "trigger_event": reward.trigger_event,
                "issued_time": reward.issued_time.isoformat() if reward.issued_time else None
            })
        
        export_data.append({
            "relationship_id": relationship.id,
            "referrer_id": relationship.referrer_id,
            "referrer_name": referrer_name,
            "referrer_phone": referrer_phone,
            "referred_id": relationship.referred_id,
            "referred_name": referred_name,
            "referred_phone": referred_phone,
            "referral_code": relationship.referral_code,
            "status": relationship.status,
            "binding_time": relationship.binding_time.isoformat(),
            "rewards": reward_info
        })
    
    return export_data 