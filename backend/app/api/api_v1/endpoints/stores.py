from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.store import Store
from app.schemas.store import StoreCreate, StoreUpdate, StoreResponse

router = APIRouter()


@router.get("/", response_model=List[StoreResponse], summary="获取门店列表")
def get_stores(
    db: Session = Depends(get_db),
    skip: int = Query(0, description="跳过的记录数"),
    limit: int = Query(100, description="返回的记录数"),
    status: Optional[str] = Query(None, description="门店状态筛选")
):
    """
    获取门店列表

    支持分页查询和状态筛选，返回门店的基本信息包括名称、地址、联系方式等
    """
    query = db.query(Store).filter(Store.is_deleted == False)
    
    if status:
        query = query.filter(Store.status == status)
    
    stores = query.offset(skip).limit(limit).all()
    return stores


@router.post("/", response_model=StoreResponse, summary="创建门店")
def create_store(
    store_in: StoreCreate,
    db: Session = Depends(get_db)
):
    """
    创建新门店

    创建新的门店记录，包括门店基本信息、地址、联系方式等
    """
    # 生成门店编码
    import uuid
    store_code = f"STORE_{uuid.uuid4().hex[:8].upper()}"

    # 确保编码唯一
    while db.query(Store).filter(Store.code == store_code).first():
        store_code = f"STORE_{uuid.uuid4().hex[:8].upper()}"

    store_data = store_in.model_dump()
    store_data['code'] = store_code

    # 处理business_hours字段 - 数据库中是JSON类型，需要转换
    if 'business_hours' in store_data and store_data['business_hours']:
        if isinstance(store_data['business_hours'], str):
            # 将字符串转换为JSON格式存储
            store_data['business_hours'] = {"hours": store_data['business_hours']}

    db_store = Store(**store_data)
    db.add(db_store)
    db.commit()
    db.refresh(db_store)
    return db_store


@router.get("/{store_id}", response_model=StoreResponse, summary="获取门店详情")
def get_store(
    *,
    store_id: int = Path(..., description="门店ID"),
    db: Session = Depends(get_db)
):
    """
    获取门店详情

    根据门店ID获取门店的详细信息，包括基本信息、营业时间、联系方式等
    """
    store = db.query(Store).filter(Store.id == store_id, Store.is_deleted == False).first()
    if not store:
        raise HTTPException(status_code=404, detail="门店不存在")
    return store


@router.put("/{store_id}", response_model=StoreResponse, summary="更新门店信息")
def update_store(
    *,
    store_id: int = Path(..., description="门店ID"),
    store_in: StoreUpdate,
    db: Session = Depends(get_db)
):
    """
    更新门店信息

    更新指定门店的信息，支持部分字段更新
    """
    store = db.query(Store).filter(Store.id == store_id, Store.is_deleted == False).first()
    if not store:
        raise HTTPException(status_code=404, detail="门店不存在")
    
    update_data = store_in.model_dump(exclude_unset=True)

    # 处理business_hours字段 - 数据库中是JSON类型，需要转换
    if 'business_hours' in update_data and update_data['business_hours']:
        if isinstance(update_data['business_hours'], str):
            # 将字符串转换为JSON格式存储
            update_data['business_hours'] = {"hours": update_data['business_hours']}

    for field, value in update_data.items():
        setattr(store, field, value)
    
    db.commit()
    db.refresh(store)
    return store


@router.delete("/{store_id}", summary="删除门店")
def delete_store(
    *,
    store_id: int = Path(..., description="门店ID"),
    db: Session = Depends(get_db)
):
    """
    删除门店（软删除）

    软删除指定的门店记录，门店数据不会真正删除，只是标记为已删除状态
    """
    store = db.query(Store).filter(Store.id == store_id, Store.is_deleted == False).first()
    if not store:
        raise HTTPException(status_code=404, detail="门店不存在")
    
    store.is_deleted = True
    db.commit()
    return {"message": "门店已删除"} 