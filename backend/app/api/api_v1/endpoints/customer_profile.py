from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Body, Path
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from app.api import deps
from app.services.customer_profile import CustomerProfileService
from app.schemas.customer_profile import (
    CustomerTag, CustomerTagCreate, CustomerTagUpdate,
    CustomerSegment, CustomerSegmentCreate, CustomerSegmentUpdate,
    CustomerBehavior, CustomerBehaviorCreate,
    CustomerValueMetric, CustomerValueMetricUpdate,
    CustomerPreference, CustomerPreferenceUpdate,
    CustomerProfile, CustomerAnalysisRequest, CustomerAnalysisResult,
    CustomerChurnRisk
)

router = APIRouter()


# 客户标签相关API
@router.post("/tags/", response_model=CustomerTag, status_code=201)
def create_tag(
    tag: CustomerTagCreate,
    db: Session = Depends(deps.get_db)
):
    """创建客户标签"""
    return CustomerProfileService.create_customer_tag(db, tag)


@router.get("/tags/", response_model=List[CustomerTag])
def get_tags(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(deps.get_db)
):
    """获取客户标签列表"""
    return CustomerProfileService.get_customer_tags(db, skip, limit)


@router.get("/tags/{tag_id}", response_model=CustomerTag)
def get_tag(
    tag_id: int = Path(..., title="标签ID", description="tag_id"),
    db: Session = Depends(deps.get_db)
):
    """获取客户标签详情"""
    tag = CustomerProfileService.get_customer_tag_by_id(db, tag_id)
    if not tag:
        raise HTTPException(status_code=404, detail="标签不存在")
    return tag


@router.put("/tags/{tag_id}", response_model=CustomerTag)
def update_tag(
    tag_id: int = Path(..., title="标签ID", description="tag_id"),
    tag_update: CustomerTagUpdate = Body(...),
    db: Session = Depends(deps.get_db)
):
    """更新客户标签"""
    updated_tag = CustomerProfileService.update_customer_tag(db, tag_id, tag_update.dict(exclude_unset=True))
    if not updated_tag:
        raise HTTPException(status_code=404, detail="标签不存在")
    return updated_tag


@router.delete("/tags/{tag_id}", status_code=204)
def delete_tag(
    tag_id: int = Path(..., title="标签ID", description="tag_id"),
    db: Session = Depends(deps.get_db)
):
    """删除客户标签"""
    success = CustomerProfileService.delete_customer_tag(db, tag_id)
    if not success:
        raise HTTPException(status_code=404, detail="标签不存在")
    return None


@router.post("/customers/{customer_id}/tags/{tag_id}", status_code=204)
def add_tag_to_customer(
    customer_id: int = Path(..., title="客户ID", description="客户ID"),
    tag_id: int = Path(..., title="标签ID", description="tag_id"),
    db: Session = Depends(deps.get_db)
):
    """为客户添加标签"""
    success = CustomerProfileService.add_tag_to_customer(db, customer_id, tag_id)
    if not success:
        raise HTTPException(status_code=404, detail="客户或标签不存在")
    return None


@router.delete("/customers/{customer_id}/tags/{tag_id}", status_code=204)
def remove_tag_from_customer(
    customer_id: int = Path(..., title="客户ID", description="客户ID"),
    tag_id: int = Path(..., title="标签ID", description="tag_id"),
    db: Session = Depends(deps.get_db)
):
    """从客户中移除标签"""
    success = CustomerProfileService.remove_tag_from_customer(db, customer_id, tag_id)
    if not success:
        raise HTTPException(status_code=404, detail="客户或标签不存在，或客户未拥有该标签")
    return None


# 客户分群相关API
@router.post("/segments/", response_model=CustomerSegment, status_code=201)
def create_segment(
    segment: CustomerSegmentCreate,
    db: Session = Depends(deps.get_db)
):
    """创建客户分群"""
    return CustomerProfileService.create_customer_segment(db, segment)


@router.get("/segments/", response_model=List[CustomerSegment])
def get_segments(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(deps.get_db)
):
    """获取客户分群列表"""
    return CustomerProfileService.get_customer_segments(db, skip, limit)


@router.get("/segments/{segment_id}", response_model=CustomerSegment)
def get_segment(
    segment_id: int = Path(..., title="分群ID", description="segment_id"),
    db: Session = Depends(deps.get_db)
):
    """获取客户分群详情"""
    segment = CustomerProfileService.get_customer_segment_by_id(db, segment_id)
    if not segment:
        raise HTTPException(status_code=404, detail="分群不存在")
    return segment


@router.put("/segments/{segment_id}", response_model=CustomerSegment)
def update_segment(
    segment_id: int = Path(..., title="分群ID", description="segment_id"),
    segment_update: CustomerSegmentUpdate = Body(...),
    db: Session = Depends(deps.get_db)
):
    """更新客户分群"""
    updated_segment = CustomerProfileService.update_customer_segment(db, segment_id, segment_update.dict(exclude_unset=True))
    if not updated_segment:
        raise HTTPException(status_code=404, detail="分群不存在")
    return updated_segment


@router.delete("/segments/{segment_id}", status_code=204)
def delete_segment(
    segment_id: int = Path(..., title="分群ID", description="segment_id"),
    db: Session = Depends(deps.get_db)
):
    """删除客户分群"""
    success = CustomerProfileService.delete_customer_segment(db, segment_id)
    if not success:
        raise HTTPException(status_code=404, detail="分群不存在")
    return None


@router.post("/segments/{segment_id}/update-customers", response_model=Dict[str, int])
def update_segment_customers(
    segment_id: int = Path(..., title="分群ID", description="segment_id"),
    db: Session = Depends(deps.get_db)
):
    """更新分群中的客户"""
    added, removed = CustomerProfileService.update_segment_customers(db, segment_id)
    return {"added_count": added, "removed_count": removed}


# 客户行为记录API
@router.post("/behaviors/", response_model=CustomerBehavior, status_code=201)
def record_behavior(
    behavior: CustomerBehaviorCreate,
    db: Session = Depends(deps.get_db)
):
    """记录客户行为"""
    return CustomerProfileService.record_customer_behavior(db, behavior)


@router.get("/customers/{customer_id}/behaviors/", response_model=List[CustomerBehavior])
def get_customer_behaviors(
    customer_id: int = Path(..., title="客户ID", description="客户ID"),
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(deps.get_db)
):
    """获取客户行为列表"""
    return CustomerProfileService.get_customer_behaviors(db, customer_id, skip, limit)


# 客户价值评估API
@router.post("/customers/{customer_id}/value-metrics/calculate", response_model=CustomerValueMetric)
def calculate_customer_value(
    customer_id: int = Path(..., title="客户ID", description="客户ID"),
    db: Session = Depends(deps.get_db)
):
    """计算客户价值评估指标"""
    metrics = CustomerProfileService.calculate_customer_value_metrics(db, customer_id)
    if not metrics:
        raise HTTPException(status_code=404, detail="客户不存在")
    return metrics


# 客户偏好分析API
@router.post("/customers/{customer_id}/preferences/analyze", response_model=CustomerPreference)
def analyze_customer_preferences(
    customer_id: int = Path(..., title="客户ID", description="客户ID"),
    db: Session = Depends(deps.get_db)
):
    """分析客户偏好"""
    preferences = CustomerProfileService.analyze_customer_preferences(db, customer_id)
    if not preferences:
        raise HTTPException(status_code=404, detail="客户不存在或无消费记录")
    return preferences


# 客户画像综合API
@router.get("/customers/{customer_id}/profile", response_model=CustomerProfile)
def get_customer_profile(
    customer_id: int = Path(..., title="客户ID", description="客户ID"),
    db: Session = Depends(deps.get_db)
):
    """获取客户完整画像"""
    profile = CustomerProfileService.get_customer_profile(db, customer_id)
    if not profile:
        raise HTTPException(status_code=404, detail="客户不存在")
    return profile


# 客户分析API
@router.post("/analysis", response_model=CustomerAnalysisResult)
def analyze_customers(
    analysis_request: CustomerAnalysisRequest,
    db: Session = Depends(deps.get_db)
):
    """客户数据分析"""
    # 这里可以根据不同的分析类型调用不同的分析方法
    # 暂时返回一个示例数据
    return {
        "analysis_type": analysis_request.analysis_type,
        "total_customers": 100,
        "data": {
            "distribution": {
                "gender": {"male": 40, "female": 60},
                "age": {"18-25": 20, "26-35": 40, "36-45": 25, "46+": 15}
            }
        },
        "charts": {
            "gender_pie": {"labels": ["男", "女"], "data": [40, 60]},
            "age_bar": {"labels": ["18-25", "26-35", "36-45", "46+"], "data": [20, 40, 25, 15]}
        },
        "insights": [
            "女性客户占比60%，是主要客户群体",
            "26-35岁年龄段客户最多，占比40%"
        ]
    }


# 客户流失预警API
@router.get("/churn-risks", response_model=List[CustomerChurnRisk])
def get_churn_risk_customers(
    risk_threshold: float = Query(0.7, ge=0, le=1),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(deps.get_db)
):
    """获取有流失风险的客户"""
    return CustomerProfileService.get_churn_risk_customers(db, risk_threshold, limit) 