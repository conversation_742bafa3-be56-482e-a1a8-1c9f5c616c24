from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session
from datetime import datetime

from app.db.session import get_db
from app.models.store_item import StoreItem
from app.models.store import Store
from app.models.group_item import GroupItem
from app.schemas.store_item import StoreItem as StoreItemSchema
from app.schemas.store_item import StoreItemCreate, StoreItemUpdate, StoreItemDetail

router = APIRouter()


@router.get("/", response_model=List[StoreItemDetail])
def read_store_items(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    store_id: Optional[int] = Query(None, description="门店ID筛选"),
    is_enabled: Optional[bool] = Query(None, description="是否启用筛选")
) -> Any:
    """
    获取门店项目列表
    """
    # 构建查询过滤条件
    query = db.query(StoreItem)
    
    if store_id:
        query = query.filter(StoreItem.store_id == store_id)
        
    if is_enabled is not None:
        query = query.filter(StoreItem.is_enabled == is_enabled)
    
    return query.offset(skip).limit(limit).all()


@router.post("/", response_model=StoreItemSchema, status_code=status.HTTP_201_CREATED)
def create_store_item(
    *,
    db: Session = Depends(get_db),
    item_in: StoreItemCreate,
) -> Any:
    """
    创建门店项目
    """
    # 检查门店是否存在
    store = db.query(Store).filter(Store.id == item_in.store_id, Store.is_deleted == False).first()
    if not store:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Store with ID {item_in.store_id} not found"
        )
        
    # 检查集团项目是否存在
    group_item = db.query(GroupItem).filter(GroupItem.id == item_in.group_item_id, GroupItem.is_deleted == False).first()
    if not group_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Group item with ID {item_in.group_item_id} not found"
        )
    
    # 检查该门店是否已有此项目
    existing_item = db.query(StoreItem).filter(
        StoreItem.store_id == item_in.store_id,
        StoreItem.group_item_id == item_in.group_item_id
    ).first()
    if existing_item:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="This store already has this group item"
        )
    
    # 创建门店项目
    item = StoreItem(
        store_id=item_in.store_id,
        group_item_id=item_in.group_item_id,
        store_price=item_in.store_price,
        is_enabled=item_in.is_enabled,
        commission_scheme_id=item_in.commission_scheme_id,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
    )
    
    db.add(item)
    db.commit()
    db.refresh(item)
    
    return item


@router.get("/{item_id}", response_model=StoreItemDetail)
def read_store_item(
    *,
    db: Session = Depends(get_db),
    item_id: int = Path(..., gt=0, description="项目ID"),
) -> Any:
    """
    获取门店项目详情
    """
    item = db.query(StoreItem).filter(StoreItem.id == item_id).first()
    
    if not item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Store item with ID {item_id} not found"
        )
    
    return item


@router.put("/{item_id}", response_model=StoreItemSchema)
def update_store_item(
    *,
    db: Session = Depends(get_db),
    item_id: int = Path(..., gt=0, description="项目ID"),
    item_in: StoreItemUpdate,
) -> Any:
    """
    更新门店项目
    """
    item = db.query(StoreItem).filter(StoreItem.id == item_id).first()
    
    if not item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Store item with ID {item_id} not found"
        )
    
    update_data = item_in.model_dump(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(item, field, value)
    
    item.updated_at = datetime.utcnow()
    
    db.add(item)
    db.commit()
    db.refresh(item)
    
    return item


@router.delete("/{item_id}")
def delete_store_item(
    *,
    db: Session = Depends(get_db),
    item_id: int = Path(..., gt=0, description="项目ID"),
) -> Any:
    """
    删除门店项目 (物理删除)
    """
    item = db.query(StoreItem).filter(StoreItem.id == item_id).first()
    
    if not item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Store item with ID {item_id} not found"
        )
    
    db.delete(item)
    db.commit()
    
    return {"message": "Store item deleted successfully"} 