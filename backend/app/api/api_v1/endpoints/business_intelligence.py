from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_, or_
import uuid
import time

from app.api.deps import get_db, get_current_employee, get_current_active_employee
from app.models.operation import Order, OrderItem
from app.models.employee import Employee
from app.models.store import Store
from app.models.customer import Customer, MemberCard
from app.models.store_item import StoreItem
from app.models.group_item import GroupItem
from app.schemas.employee import Employee as EmployeeSchema
from app.schemas.business_intelligence import (
    MultiDimensionalAnalysisRequest,
    MultiDimensionalAnalysisResponse,
    BusinessIndicatorRequest,
    BusinessIndicatorResponse,
    TrendPredictionRequest,
    TrendPredictionResponse,
    PredictionType,
    AnalysisDimension,
    AnalysisMetric,
    SharedReportCreate,
    SharedReportResponse,
    SharedReportDetail
)
from app.utils.redis_cache import RedisCache

router = APIRouter()
# 初始化Redis缓存
redis_cache = RedisCache()

# 缓存过期时间配置（秒）
CACHE_EXPIRE_TIME = {
    "multidimensional_report": 3600,  # 多维度报表缓存1小时
    "business_indicators": 1800,      # 业务指标缓存30分钟
    "trend_prediction": 7200,         # 趋势预测缓存2小时
    "shared_reports": 86400,          # 共享报表缓存24小时
}


@router.post("/multidimensional-analysis", response_model=MultiDimensionalAnalysisResponse)
def generate_multidimensional_report(
    config: MultiDimensionalAnalysisRequest,
    db: Session = Depends(get_db),
    current_employee: EmployeeSchema = Depends(get_current_employee),
    page: int = Query(1, description="页码，从1开始"),
    page_size: int = Query(100, description="每页记录数，默认100")
):
    """
    生成多维度分析报表
    
    可以按照不同维度（时间、门店、员工、服务项目、会员等级等）和不同指标（销售额、订单数、客单价等）
    进行数据分析和汇总
    
    支持分页查询，避免一次性加载过多数据
    """
    # 检查权限
    if current_employee.role not in ["admin", "manager", "owner"]:
        raise HTTPException(status_code=403, detail="没有权限访问此功能")
    
    # 构建缓存键参数
    cache_params = {
        "dimensions": [d.type for d in config.dimensions],
        "measures": [m.type for m in config.measures],
        "filters": [(f.field, str(f.value)) for f in (config.filters or [])],
        "title": config.title,
        "page": page,
        "page_size": page_size
    }
    
    # 尝试从缓存获取数据
    cached_data = redis_cache.get_bi_data("multidimensional_report", cache_params)
    if cached_data:
        return cached_data
    
    # 缓存未命中，执行查询
    start_time = time.time()
    
    # 解析配置
    dimensions = config.dimensions
    measures = config.measures
    filters = config.filters if config.filters else []
    
    # 构建查询
    query = db.query(Order)
    
    # 应用过滤条件
    for filter_item in filters:
        if filter_item.field == "date_range":
            start_date = datetime.fromisoformat(filter_item.value["start"])
            end_date = datetime.fromisoformat(filter_item.value["end"])
            query = query.filter(Order.order_time.between(start_date, end_date))
        elif filter_item.field == "store_id":
            query = query.filter(Order.store_id == filter_item.value)
        elif filter_item.field == "employee_id":
            # 这里需要关联OrderItem表来过滤员工
            query = query.join(OrderItem).filter(OrderItem.employee_id == filter_item.value)
        elif filter_item.field == "payment_method":
            query = query.filter(Order.payment_method == filter_item.value)
        elif filter_item.field == "service_type":
            query = query.join(OrderItem).filter(OrderItem.item_type == filter_item.value)
    
    # 添加排序
    query = query.order_by(desc(Order.order_time))
    
    # 获取总记录数
    total_count = query.count()
    
    # 应用分页
    orders = query.offset((page - 1) * page_size).limit(page_size).all()
    
    # 根据维度和指标构建结果
    result = {}
    
    # 处理时间维度
    if "time" in [d.type for d in dimensions]:
        time_dimension = next(d for d in dimensions if d.type == "time")
        time_granularity = time_dimension.granularity
        
        if time_granularity == "day":
            # 按天聚合
            daily_data = {}
            
            for op in orders:
                day_key = op.order_time.strftime("%Y-%m-%d")
                if day_key not in daily_data:
                    daily_data[day_key] = {
                        "sales_amount": 0,
                        "order_count": 0,
                        "customer_count": set()
                    }
                
                daily_data[day_key]["sales_amount"] += op.total_amount
                daily_data[day_key]["order_count"] += 1
                daily_data[day_key]["customer_count"].add(op.customer_id)
            
            # 转换集合为计数
            for day, data in daily_data.items():
                data["customer_count"] = len(data["customer_count"])
            
            result["time_dimension"] = {
                "granularity": "day",
                "data": daily_data
            }
        
        elif time_granularity == "month":
            # 按月聚合
            monthly_data = {}
            
            for op in orders:
                month_key = op.order_time.strftime("%Y-%m")
                if month_key not in monthly_data:
                    monthly_data[month_key] = {
                        "sales_amount": 0,
                        "order_count": 0,
                        "customer_count": set()
                    }
                
                monthly_data[month_key]["sales_amount"] += op.total_amount
                monthly_data[month_key]["order_count"] += 1
                monthly_data[month_key]["customer_count"].add(op.customer_id)
            
            # 转换集合为计数
            for month, data in monthly_data.items():
                data["customer_count"] = len(data["customer_count"])
            
            result["time_dimension"] = {
                "granularity": "month",
                "data": monthly_data
            }
    
    # 处理门店维度
    if "store" in [d.type for d in dimensions]:
        store_data = {}
        
        for op in orders:
            store_id = op.store_id
            if store_id not in store_data:
                store_data[store_id] = {
                    "sales_amount": 0,
                    "order_count": 0,
                    "customer_count": set()
                }
            
            store_data[store_id]["sales_amount"] += op.total_amount
            store_data[store_id]["order_count"] += 1
            store_data[store_id]["customer_count"].add(op.customer_id)
        
        # 转换集合为计数
        for store_id, data in store_data.items():
            data["customer_count"] = len(data["customer_count"])
            
            # 获取门店名称
            store = db.query(Store).filter(Store.id == store_id).first()
            if store:
                data["store_name"] = store.name
        
        result["store_dimension"] = {
            "data": store_data
        }
    
    # 处理员工维度
    if "employee" in [d.type for d in dimensions]:
        # 这里需要关联OrderItem表来获取员工数据
        employee_data = {}
        
        for op in orders:
            # 获取订单项
            order_items = db.query(OrderItem).filter(OrderItem.order_id == op.id).all()
            
            for item in order_items:
                employee_id = item.employee_id
                if not employee_id:
                    continue
                    
                if employee_id not in employee_data:
                    employee_data[employee_id] = {
                        "service_amount": 0,
                        "service_count": 0,
                        "customer_count": set()
                    }
                
                employee_data[employee_id]["service_amount"] += item.amount
                employee_data[employee_id]["service_count"] += 1
                employee_data[employee_id]["customer_count"].add(op.customer_id)
        
        # 转换集合为计数并获取员工名称
        for employee_id, data in employee_data.items():
            data["customer_count"] = len(data["customer_count"])
            
            # 获取员工名称
            employee = db.query(Employee).filter(Employee.id == employee_id).first()
            if employee:
                data["employee_name"] = employee.name
        
        result["employee_dimension"] = {
            "data": employee_data
        }
    
    # 处理服务项目维度
    if "service" in [d.type for d in dimensions]:
        service_data = {}
        
        for op in orders:
            # 获取订单项
            order_items = db.query(OrderItem).filter(OrderItem.order_id == op.id).all()
            
            for item in order_items:
                item_id = item.item_id
                item_type = item.item_type
                
                key = f"{item_type}_{item_id}"
                if key not in service_data:
                    service_data[key] = {
                        "sales_amount": 0,
                        "sales_count": 0,
                        "customer_count": set()
                    }
                
                service_data[key]["sales_amount"] += item.amount
                service_data[key]["sales_count"] += 1
                service_data[key]["customer_count"].add(op.customer_id)
                
                # 获取项目名称
                if item_type == "store_item":
                    service = db.query(StoreItem).filter(StoreItem.id == item_id).first()
                    if service:
                        service_data[key]["service_name"] = service.name
                        service_data[key]["service_type"] = "门店项目"
                elif item_type == "group_item":
                    service = db.query(GroupItem).filter(GroupItem.id == item_id).first()
                    if service:
                        service_data[key]["service_name"] = service.name
                        service_data[key]["service_type"] = "集团项目"
        
        # 转换集合为计数
        for key, data in service_data.items():
            data["customer_count"] = len(data["customer_count"])
        
        result["service_dimension"] = {
            "data": service_data
        }
    
    # 处理会员维度
    if "member" in [d.type for d in dimensions]:
        member_data = {}
        
        for op in orders:
            if not op.customer_id:
                continue
                
            customer = db.query(Customer).filter(Customer.id == op.customer_id).first()
            if not customer:
                continue
                
            # 获取会员卡信息
            member_cards = db.query(MemberCard).filter(MemberCard.customer_id == customer.id).all()
            
            if not member_cards:
                # 非会员
                key = "non_member"
                if key not in member_data:
                    member_data[key] = {
                        "sales_amount": 0,
                        "order_count": 0,
                        "customer_count": set()
                    }
                
                member_data[key]["sales_amount"] += op.total_amount
                member_data[key]["order_count"] += 1
                member_data[key]["customer_count"].add(op.customer_id)
            else:
                # 取最高等级的会员卡
                highest_level_card = max(member_cards, key=lambda x: x.level_id)
                key = f"level_{highest_level_card.level_id}"
                
                if key not in member_data:
                    member_data[key] = {
                        "sales_amount": 0,
                        "order_count": 0,
                        "customer_count": set(),
                        "level_name": highest_level_card.level.name if hasattr(highest_level_card, "level") else f"等级{highest_level_card.level_id}"
                    }
                
                member_data[key]["sales_amount"] += op.total_amount
                member_data[key]["order_count"] += 1
                member_data[key]["customer_count"].add(op.customer_id)
        
        # 转换集合为计数
        for key, data in member_data.items():
            data["customer_count"] = len(data["customer_count"])
        
        result["member_dimension"] = {
            "data": member_data
        }
    
    response_data = {
        "title": config.title,
        "dimensions": [d.type for d in dimensions],
        "measures": [m.type for m in measures],
        "data": result,
        "pagination": {
            "page": page,
            "page_size": page_size,
            "total_count": total_count,
            "total_pages": (total_count + page_size - 1) // page_size
        },
        "query_time": time.time() - start_time  # 添加查询耗时信息
    }
    
    # 将结果存入缓存
    redis_cache.set_bi_data(
        "multidimensional_report",
        cache_params,
        response_data,
        CACHE_EXPIRE_TIME["multidimensional_report"]
    )
    
    return response_data


@router.post("/business-indicators", response_model=BusinessIndicatorResponse)
def get_business_indicators(
    request: BusinessIndicatorRequest,
    db: Session = Depends(get_db),
    current_employee: EmployeeSchema = Depends(get_current_employee)
):
    """
    获取经营指标分析数据
    
    提供关键业务指标的分析和比较，包括销售额、订单数、客单价、新客数、复购率等指标，
    以及同比或环比的变化趋势
    """
    # 检查权限
    if current_employee.role not in ["admin", "manager", "owner"]:
        raise HTTPException(status_code=403, detail="没有权限访问此功能")
    
    # 从请求中提取参数
    store_ids = request.store_ids
    start_date = request.start_date
    end_date = request.end_date
    indicators = request.indicators
    comparison_period = request.comparison_period

    # 构建缓存键参数
    cache_params = {
        "store_ids": store_ids,
        "start_date": start_date.isoformat() if start_date else None,
        "end_date": end_date.isoformat() if end_date else None,
        "indicators": indicators,
        "comparison_period": comparison_period
    }
    
    # 尝试从缓存获取数据
    cached_data = redis_cache.get_bi_data("business_indicators", cache_params)
    if cached_data:
        return cached_data
        
    # 缓存未命中，执行查询
    start_time = time.time()
    
    # 处理日期范围
    if not start_date or not end_date:
        # 默认使用当前月
        now = datetime.now()
        start_date = datetime(now.year, now.month, 1)
        if now.month == 12:
            end_date = datetime(now.year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(now.year, now.month + 1, 1) - timedelta(days=1)
        end_date = end_date.replace(hour=23, minute=59, second=59)
    else:
        # 转换为datetime对象
        if isinstance(start_date, str):
            start_date = datetime.fromisoformat(start_date)
        if isinstance(end_date, str):
            end_date = datetime.fromisoformat(end_date)

        # 确保end_date包含全天
        end_date = end_date.replace(hour=23, minute=59, second=59)

    # 计算对比周期的日期范围
    period_days = (end_date - start_date).days + 1
    prev_end_date = start_date - timedelta(days=1)
    prev_start_date = prev_end_date - timedelta(days=period_days - 1)
    
    # 构建查询条件
    current_period_filter = Order.order_time.between(start_date, end_date)
    prev_period_filter = Order.order_time.between(prev_start_date, prev_end_date)
    
    if store_ids:
        current_period_filter = and_(current_period_filter, Order.store_id.in_(store_ids))
        prev_period_filter = and_(prev_period_filter, Order.store_id.in_(store_ids))
    
    # 查询当前周期数据
    current_orders = db.query(Order).filter(current_period_filter).all()
    prev_orders = db.query(Order).filter(prev_period_filter).all()
    
    # 计算销售指标
    current_sales = sum(op.total_amount for op in current_orders)
    prev_sales = sum(op.total_amount for op in prev_orders) if prev_orders else 0
    
    sales_trend = 0
    if prev_sales > 0:
        sales_trend = (current_sales - prev_sales) / prev_sales * 100
    
    # 计算订单数指标
    current_order_count = len(current_orders)
    prev_order_count = len(prev_orders)
    
    order_count_trend = 0
    if prev_order_count > 0:
        order_count_trend = (current_order_count - prev_order_count) / prev_order_count * 100
    
    # 计算客单价指标
    current_avg_order_value = current_sales / current_order_count if current_order_count > 0 else 0
    prev_avg_order_value = prev_sales / prev_order_count if prev_order_count > 0 else 0
    
    avg_order_value_trend = 0
    if prev_avg_order_value > 0:
        avg_order_value_trend = (current_avg_order_value - prev_avg_order_value) / prev_avg_order_value * 100
    
    # 计算客户数指标
    current_customer_ids = set(op.customer_id for op in current_orders if op.customer_id)
    prev_customer_ids = set(op.customer_id for op in prev_orders if op.customer_id)
    
    current_customer_count = len(current_customer_ids)
    prev_customer_count = len(prev_customer_ids)
    
    customer_count_trend = 0
    if prev_customer_count > 0:
        customer_count_trend = (current_customer_count - prev_customer_count) / prev_customer_count * 100
    
    # 计算新客户数
    # 获取当前周期之前的所有客户ID
    before_current_period_filter = Order.order_time < start_date
    before_current_orders = db.query(Order).filter(before_current_period_filter).all()
    before_current_customer_ids = set(op.customer_id for op in before_current_orders if op.customer_id)
    
    new_customer_ids = current_customer_ids - before_current_customer_ids
    new_customer_count = len(new_customer_ids)
    
    # 计算复购率
    repeat_customer_ids = set()
    customer_order_counts = {}
    
    for op in current_orders:
        if not op.customer_id:
            continue
        
        if op.customer_id not in customer_order_counts:
            customer_order_counts[op.customer_id] = 0
        
        customer_order_counts[op.customer_id] += 1
        
        if customer_order_counts[op.customer_id] > 1:
            repeat_customer_ids.add(op.customer_id)
    
    repeat_purchase_rate = len(repeat_customer_ids) / current_customer_count * 100 if current_customer_count > 0 else 0
    
    # 这些数据将在后面构建正确的响应结构时使用
    # 暂时保存计算结果，稍后构建符合模型的响应
    
    # 获取热门服务项目
    service_stats = {}
    for op in current_orders:
        order_items = db.query(OrderItem).filter(OrderItem.order_id == op.id).all()

        for item in order_items:
            # OrderItem模型使用store_item_id
            if item.store_item_id:
                item_key = f"store_item_{item.store_item_id}"
                if item_key not in service_stats:
                    service_stats[item_key] = {
                        "amount": 0,
                        "count": 0,
                        "type": "store_item",
                        "id": item.store_item_id,
                        "name": ""
                    }

                service_stats[item_key]["amount"] += item.subtotal or 0
                service_stats[item_key]["count"] += item.quantity or 1

                # 获取项目名称
                store_item = db.query(StoreItem).filter(StoreItem.id == item.store_item_id).first()
                if store_item and store_item.group_item:
                    service_stats[item_key]["name"] = store_item.group_item.name
                elif store_item:
                    service_stats[item_key]["name"] = f"商品{item.store_item_id}"
    
    # 按销售额排序获取前10个热门项目
    top_services = sorted(service_stats.values(), key=lambda x: x["amount"], reverse=True)[:10]
    
    # 构建图表数据
    charts = {
        "sales_trend": {
            "title": "销售趋势",
            "x_axis": [],
            "series": [
                {
                    "name": "销售额",
                    "data": []
                },
                {
                    "name": "订单数",
                    "data": []
                }
            ]
        },
        "top_services": {
            "title": "热门服务项目",
            "items": top_services
        },
        "customer_trend": {
            "title": "客户趋势",
            "x_axis": [],
            "series": [
                {
                    "name": "客户数",
                    "data": []
                },
                {
                    "name": "新客户数",
                    "data": []
                }
            ]
        }
    }
    
    # 生成时间序列数据
    if is_quarter:
        # 按月拆分季度数据
        months_in_quarter = 3
        month_start = (quarter - 1) * 3 + 1
        
        for i in range(months_in_quarter):
            current_month = month_start + i
            month_start_date = datetime(year, current_month, 1)
            
            if current_month == 12:
                month_end_date = datetime(year+1, 1, 1) - timedelta(seconds=1)
            else:
                month_end_date = datetime(year, current_month+1, 1) - timedelta(seconds=1)
            
            month_filter = Order.order_time.between(month_start_date, month_end_date)
            if store_id:
                month_filter = and_(month_filter, Order.store_id == store_id)

            month_orders = db.query(Order).filter(month_filter).all()
            month_sales = sum(op.total_amount for op in month_orders)
            month_order_count = len(month_orders)

            month_customer_ids = set(op.customer_id for op in month_orders if op.customer_id)
            month_customer_count = len(month_customer_ids)

            # 计算新客户
            before_month_filter = Order.order_time < month_start_date
            before_month_orders = db.query(Order).filter(before_month_filter).all()
            before_month_customer_ids = set(op.customer_id for op in before_month_orders if op.customer_id)
            
            month_new_customer_ids = month_customer_ids - before_month_customer_ids
            month_new_customer_count = len(month_new_customer_ids)
            
            # 添加到图表数据
            month_label = f"{year}-{current_month:02d}"
            charts["sales_trend"]["x_axis"].append(month_label)
            charts["sales_trend"]["series"][0]["data"].append(month_sales)
            charts["sales_trend"]["series"][1]["data"].append(month_order_count)
            
            charts["customer_trend"]["x_axis"].append(month_label)
            charts["customer_trend"]["series"][0]["data"].append(month_customer_count)
            charts["customer_trend"]["series"][1]["data"].append(month_new_customer_count)
    else:
        # 按天拆分月度数据
        days_in_month = (end_date - start_date).days + 1
        
        for i in range(days_in_month):
            day_date = start_date + timedelta(days=i)
            day_end = day_date.replace(hour=23, minute=59, second=59)
            
            day_filter = Order.order_time.between(day_date, day_end)
            if store_id:
                day_filter = and_(day_filter, Order.store_id == store_id)

            day_orders = db.query(Order).filter(day_filter).all()
            day_sales = sum(op.total_amount for op in day_orders)
            day_order_count = len(day_orders)

            day_customer_ids = set(op.customer_id for op in day_orders if op.customer_id)
            day_customer_count = len(day_customer_ids)

            # 计算新客户
            before_day_filter = Order.order_time < day_date
            before_day_orders = db.query(Order).filter(before_day_filter).all()
            before_day_customer_ids = set(op.customer_id for op in before_day_orders if op.customer_id)
            
            day_new_customer_ids = day_customer_ids - before_day_customer_ids
            day_new_customer_count = len(day_new_customer_ids)
            
            # 添加到图表数据
            day_label = day_date.strftime("%d")
            charts["sales_trend"]["x_axis"].append(day_label)
            charts["sales_trend"]["series"][0]["data"].append(day_sales)
            charts["sales_trend"]["series"][1]["data"].append(day_order_count)
            
            charts["customer_trend"]["x_axis"].append(day_label)
            charts["customer_trend"]["series"][0]["data"].append(day_customer_count)
            charts["customer_trend"]["series"][1]["data"].append(day_new_customer_count)
    
    # 构建响应数据，按照BusinessIndicatorResponse模型的要求
    indicators_dict = {}

    # 销售指标
    indicators_dict["sales_amount"] = {
        "current_value": current_sales,
        "previous_value": prev_sales,
        "change_percent": sales_trend,
        "trend": "up" if sales_trend > 0 else "down" if sales_trend < 0 else "stable"
    }

    indicators_dict["order_count"] = {
        "current_value": current_order_count,
        "previous_value": prev_order_count,
        "change_percent": order_count_trend,
        "trend": "up" if order_count_trend > 0 else "down" if order_count_trend < 0 else "stable"
    }

    indicators_dict["avg_order_value"] = {
        "current_value": current_avg_order_value,
        "previous_value": prev_avg_order_value,
        "change_percent": avg_order_value_trend,
        "trend": "up" if avg_order_value_trend > 0 else "down" if avg_order_value_trend < 0 else "stable"
    }

    indicators_dict["customer_count"] = {
        "current_value": current_customer_count,
        "previous_value": prev_customer_count,
        "change_percent": customer_count_trend,
        "trend": "up" if customer_count_trend > 0 else "down" if customer_count_trend < 0 else "stable"
    }

    # 预警信息
    warnings = []
    if sales_trend < -10:
        warnings.append({
            "type": "sales_decline",
            "message": f"销售额下降{abs(sales_trend):.1f}%，需要关注",
            "severity": "high"
        })

    if customer_count_trend < -5:
        warnings.append({
            "type": "customer_decline",
            "message": f"客户数下降{abs(customer_count_trend):.1f}%，需要关注",
            "severity": "medium"
        })

    # 趋势数据
    trend_data = {
        "sales_trend": charts["sales_trend"]["series"][0]["data"],
        "order_trend": charts["sales_trend"]["series"][1]["data"],
        "customer_trend": charts["customer_trend"]["series"][0]["data"],
        "new_customer_trend": charts["customer_trend"]["series"][1]["data"]
    }

    # 构建响应
    response_data = BusinessIndicatorResponse(
        indicators=indicators_dict,
        warnings=warnings,
        trend_data=trend_data,
        query_time=time.time() - start_time
    )
    
    # 将结果存入缓存
    redis_cache.set_bi_data(
        "business_indicators",
        cache_params,
        response_data.dict(),
        CACHE_EXPIRE_TIME["business_indicators"]
    )
    
    return response_data

@router.post("/trend-prediction", response_model=TrendPredictionResponse)
def predict_trend(
    request: TrendPredictionRequest,
    db: Session = Depends(get_db),
    current_employee: EmployeeSchema = Depends(get_current_employee)
):
    """
    趋势预测API
    
    基于历史数据进行趋势预测，支持营收、客流量、订单数等指标的预测
    """
    # 检查权限
    if current_employee.role not in ["admin", "manager", "owner"]:
        raise HTTPException(status_code=403, detail="没有权限访问此功能")
    
    # 解析请求参数
    prediction_type = request.prediction_type
    store_ids = request.store_ids
    historical_start_date = request.historical_start_date
    historical_end_date = request.historical_end_date
    prediction_periods = request.prediction_periods or 3
    period_unit = request.period_unit or "month"
    
    # 如果没有指定日期范围，默认使用过去12个月的数据
    if not historical_start_date:
        historical_end_date = datetime.now().date()
        if period_unit == "month":
            historical_start_date = historical_end_date.replace(year=historical_end_date.year - 1)
        elif period_unit == "week":
            historical_start_date = historical_end_date - timedelta(days=7 * 12)
        else:  # day
            historical_start_date = historical_end_date - timedelta(days=30)
    
    if not historical_end_date:
        historical_end_date = datetime.now().date()
    
    # 构建查询条件
    query = db.query(Order)
    
    # 添加日期过滤
    query = query.filter(
        Order.order_time >= datetime.combine(historical_start_date, datetime.min.time()),
        Order.order_time <= datetime.combine(historical_end_date, datetime.max.time())
    )
    
    # 添加门店过滤
    if store_ids:
        query = query.filter(Order.store_id.in_(store_ids))
    
    # 获取历史数据
    orders = query.all()
    
    # 按时间周期分组数据
    historical_data = {}
    
    if period_unit == "month":
        # 按月分组
        for op in orders:
            month_key = op.order_time.strftime("%Y-%m")
            if month_key not in historical_data:
                historical_data[month_key] = {
                    "revenue": 0,
                    "customer_flow": set(),
                    "order_count": 0
                }
            
            historical_data[month_key]["revenue"] += op.total_amount
            if op.customer_id:
                historical_data[month_key]["customer_flow"].add(op.customer_id)
            historical_data[month_key]["order_count"] += 1
        
        # 转换客流量集合为计数
        for key in historical_data:
            historical_data[key]["customer_flow"] = len(historical_data[key]["customer_flow"])
    
    elif period_unit == "week":
        # 按周分组
        for op in orders:
            week_key = op.order_time.strftime("%Y-%U")  # 格式：年-周数
            if week_key not in historical_data:
                historical_data[week_key] = {
                    "revenue": 0,
                    "customer_flow": set(),
                    "order_count": 0
                }
            
            historical_data[week_key]["revenue"] += op.total_amount
            if op.customer_id:
                historical_data[week_key]["customer_flow"].add(op.customer_id)
            historical_data[week_key]["order_count"] += 1
        
        # 转换客流量集合为计数
        for key in historical_data:
            historical_data[key]["customer_flow"] = len(historical_data[key]["customer_flow"])
    
    else:  # day
        # 按天分组
        for op in orders:
            day_key = op.order_time.strftime("%Y-%m-%d")
            if day_key not in historical_data:
                historical_data[day_key] = {
                    "revenue": 0,
                    "customer_flow": set(),
                    "order_count": 0
                }
            
            historical_data[day_key]["revenue"] += op.total_amount
            if op.customer_id:
                historical_data[day_key]["customer_flow"].add(op.customer_id)
            historical_data[day_key]["order_count"] += 1
        
        # 转换客流量集合为计数
        for key in historical_data:
            historical_data[key]["customer_flow"] = len(historical_data[key]["customer_flow"])
    
    # 将历史数据转换为时间序列
    time_keys = sorted(historical_data.keys())
    time_series = []
    values = []
    
    for key in time_keys:
        time_series.append(key)
        if prediction_type == PredictionType.revenue:
            values.append(historical_data[key]["revenue"])
        elif prediction_type == PredictionType.customer_flow:
            values.append(historical_data[key]["customer_flow"])
        else:  # order_count
            values.append(historical_data[key]["order_count"])
    
    # 使用简单的移动平均线和线性回归进行预测
    # 这里使用简化的预测模型，实际生产环境可以使用更复杂的算法
    
    # 计算移动平均线
    window_size = min(3, len(values))
    if window_size > 0:
        moving_avg = sum(values[-window_size:]) / window_size
    else:
        moving_avg = 0
    
    # 简单线性回归预测
    predicted_values = []
    
    if len(values) >= 2:
        # 计算简单的线性趋势
        x = list(range(len(values)))
        y = values
        
        # 计算斜率和截距
        n = len(x)
        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(x[i] * y[i] for i in range(n))
        sum_xx = sum(x[i] * x[i] for i in range(n))
        
        # 计算斜率
        if n * sum_xx - sum_x * sum_x != 0:
            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_xx - sum_x * sum_x)
        else:
            slope = 0
        
        # 计算截距
        intercept = (sum_y - slope * sum_x) / n
        
        # 预测未来值
        for i in range(1, prediction_periods + 1):
            next_value = intercept + slope * (len(values) + i - 1)
            # 确保预测值不为负
            predicted_values.append(max(0, next_value))
    else:
        # 如果历史数据不足，使用最后一个值或默认值
        last_value = values[-1] if values else 0
        for _ in range(prediction_periods):
            predicted_values.append(last_value)
    
    # 生成预测周期的时间标签
    predicted_time_keys = []
    last_time_key = time_keys[-1] if time_keys else None
    
    if last_time_key:
        if period_unit == "month":
            year, month = map(int, last_time_key.split("-"))
            for i in range(1, prediction_periods + 1):
                next_month = month + i
                next_year = year + (next_month - 1) // 12
                next_month = ((next_month - 1) % 12) + 1
                predicted_time_keys.append(f"{next_year}-{next_month:02d}")
        
        elif period_unit == "week":
            year, week = map(int, last_time_key.split("-"))
            for i in range(1, prediction_periods + 1):
                next_week = week + i
                next_year = year
                if next_week > 52:
                    next_week = next_week - 52
                    next_year += 1
                predicted_time_keys.append(f"{next_year}-{next_week:02d}")
        
        else:  # day
            from datetime import datetime, timedelta
            last_date = datetime.strptime(last_time_key, "%Y-%m-%d")
            for i in range(1, prediction_periods + 1):
                next_date = last_date + timedelta(days=i)
                predicted_time_keys.append(next_date.strftime("%Y-%m-%d"))
    
    # 分析影响因素
    factors = []
    
    # 计算季节性因素
    if len(values) >= 12 and period_unit == "month":
        # 检测是否有季节性模式
        monthly_averages = {}
        for i, key in enumerate(time_keys):
            month = int(key.split("-")[1])
            if month not in monthly_averages:
                monthly_averages[month] = {"sum": 0, "count": 0}
            monthly_averages[month]["sum"] += values[i]
            monthly_averages[month]["count"] += 1
        
        # 计算每个月的平均值
        for month in monthly_averages:
            if monthly_averages[month]["count"] > 0:
                monthly_averages[month]["avg"] = monthly_averages[month]["sum"] / monthly_averages[month]["count"]
        
        # 找出高峰和低谷月份
        if monthly_averages:
            peak_month = max(monthly_averages.items(), key=lambda x: x[1].get("avg", 0))
            low_month = min(monthly_averages.items(), key=lambda x: x[1].get("avg", 0))
            
            factors.append({
                "name": "季节性因素",
                "description": f"数据显示{peak_month[0]}月为高峰期，{low_month[0]}月为低谷期",
                "impact": "high" if peak_month[1].get("avg", 0) > 1.5 * low_month[1].get("avg", 0) else "medium"
            })
    
    # 计算趋势因素
    if len(values) >= 3:
        # 计算最近3个周期的平均增长率
        growth_rates = []
        for i in range(len(values) - 1, max(0, len(values) - 3) - 1, -1):
            if i > 0 and values[i-1] > 0:
                growth_rate = (values[i] - values[i-1]) / values[i-1]
                growth_rates.append(growth_rate)
        
        avg_growth_rate = sum(growth_rates) / len(growth_rates) if growth_rates else 0
        
        trend_description = "上升" if avg_growth_rate > 0.05 else ("下降" if avg_growth_rate < -0.05 else "平稳")
        factors.append({
            "name": "趋势因素",
            "description": f"近期{prediction_type}呈{trend_description}趋势，平均增长率为{avg_growth_rate:.2%}",
            "impact": "high" if abs(avg_growth_rate) > 0.1 else "medium"
        })
    
    # 构建响应数据
    formatted_historical_data = {
        "time_series": time_keys,
        "values": values
    }
    
    formatted_predicted_data = {
        "time_series": predicted_time_keys,
        "values": predicted_values
    }
    
    return {
        "prediction_type": prediction_type,
        "historical_data": formatted_historical_data,
        "predicted_data": formatted_predicted_data,
        "factors": factors
    } 

@router.post("/shared-reports", response_model=SharedReportResponse)
def create_shared_report(
    report: SharedReportCreate,
    db: Session = Depends(get_db),
    current_employee: EmployeeSchema = Depends(get_current_employee)
):
    """
    创建共享报表链接
    
    将报表配置保存到数据库，并生成一个唯一的分享ID，用于访问该报表
    """
    # 检查权限
    if current_employee.role not in ["admin", "manager", "owner"]:
        raise HTTPException(status_code=403, detail="没有权限访问此功能")
    
    # 生成唯一分享ID
    share_id = str(uuid.uuid4())
    
    # 创建共享报表记录
    new_shared_report = {
        "id": share_id,
        "report_config": report.report_config,
        "report_type": report.report_type,
        "title": report.title,
        "created_by": current_employee.id,
        "created_at": datetime.now(),
        "expiry_date": report.expiry_date,
        "is_active": True
    }
    
    # 将记录保存到数据库
    # 注意：这里使用了简化的方式，实际应用中应该创建一个专门的模型
    db.execute(
        """
        INSERT INTO shared_reports (
            id, report_config, report_type, title, created_by, created_at, expiry_date, is_active
        ) VALUES (
            :id, :report_config, :report_type, :title, :created_by, :created_at, :expiry_date, :is_active
        )
        """,
        new_shared_report
    )
    db.commit()
    
    return {"share_id": share_id}


@router.get("/shared-reports/{share_id}", response_model=SharedReportDetail)
def get_shared_report(
    share_id: str,
    db: Session = Depends(get_db)
):
    """
    获取共享报表
    
    根据分享ID获取报表配置和元数据
    """
    # 查询共享报表
    shared_report = db.execute(
        """
        SELECT * FROM shared_reports
        WHERE id = :share_id AND is_active = TRUE
        """,
        {"share_id": share_id}
    ).fetchone()
    
    if not shared_report:
        raise HTTPException(status_code=404, detail="分享报表不存在或已过期")
    
    # 检查是否过期
    if shared_report.expiry_date and datetime.now() > shared_report.expiry_date:
        # 更新为非活动状态
        db.execute(
            """
            UPDATE shared_reports
            SET is_active = FALSE
            WHERE id = :share_id
            """,
            {"share_id": share_id}
        )
        db.commit()
        
        raise HTTPException(status_code=404, detail="分享链接已过期")
    
    # 获取创建者信息
    creator = db.query(Employee).filter(Employee.id == shared_report.created_by).first()
    creator_name = creator.name if creator else "未知用户"
    
    return {
        "share_id": shared_report.id,
        "report_config": shared_report.report_config,
        "report_type": shared_report.report_type,
        "title": shared_report.title,
        "created_by": creator_name,
        "created_at": shared_report.created_at,
        "expiry_date": shared_report.expiry_date
    }


@router.get("/shared-reports", response_model=List[SharedReportDetail])
def list_shared_reports(
    db: Session = Depends(get_db),
    current_employee: EmployeeSchema = Depends(get_current_employee)
):
    """
    列出当前用户创建的所有共享报表
    """
    # 检查权限
    if current_employee.role not in ["admin", "manager", "owner"]:
        raise HTTPException(status_code=403, detail="没有权限访问此功能")
    
    # 查询用户创建的所有共享报表
    shared_reports = db.execute(
        """
        SELECT * FROM shared_reports
        WHERE created_by = :employee_id
        ORDER BY created_at DESC
        """,
        {"employee_id": current_employee.id}
    ).fetchall()
    
    result = []
    for report in shared_reports:
        # 检查是否过期
        is_expired = report.expiry_date and datetime.now() > report.expiry_date
        
        result.append({
            "share_id": report.id,
            "report_config": report.report_config,
            "report_type": report.report_type,
            "title": report.title,
            "created_by": current_employee.name,
            "created_at": report.created_at,
            "expiry_date": report.expiry_date,
            "is_active": report.is_active and not is_expired
        })
    
    return result


@router.delete("/shared-reports/{share_id}", status_code=204)
def delete_shared_report(
    share_id: str,
    db: Session = Depends(get_db),
    current_employee: EmployeeSchema = Depends(get_current_employee)
):
    """
    删除共享报表
    
    将共享报表标记为非活动状态
    """
    # 检查权限
    if current_employee.role not in ["admin", "manager", "owner"]:
        raise HTTPException(status_code=403, detail="没有权限访问此功能")
    
    # 查询共享报表
    shared_report = db.execute(
        """
        SELECT * FROM shared_reports
        WHERE id = :share_id
        """,
        {"share_id": share_id}
    ).fetchone()
    
    if not shared_report:
        raise HTTPException(status_code=404, detail="分享报表不存在")
    
    # 检查是否是创建者或管理员
    if shared_report.created_by != current_employee.id and current_employee.role != "admin":
        raise HTTPException(status_code=403, detail="没有权限删除此分享报表")
    
    # 将报表标记为非活动状态
    db.execute(
        """
        UPDATE shared_reports
        SET is_active = FALSE
        WHERE id = :share_id
        """,
        {"share_id": share_id}
    )
    db.commit()
    
    return None


@router.get("/advanced-analytics")
def get_advanced_analytics(
    store_id: Optional[int] = Query(None, description="门店ID"),
    start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD"),
    db: Session = Depends(get_db),
    current_employee: EmployeeSchema = Depends(get_current_employee)
):
    """
    获取高级分析数据
    """
    try:
        # 解析日期
        if start_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        else:
            start_date = datetime.now().date() - timedelta(days=30)

        if end_date:
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        else:
            end_date = datetime.now().date()

        # 构建缓存键
        cache_key = f"advanced_analytics:{store_id}:{start_date}:{end_date}"

        # 尝试从缓存获取
        cached_result = redis_cache.get(cache_key)
        if cached_result:
            return cached_result

        # 获取订单数据
        orders_query = db.query(Order).filter(
            and_(
                func.date(Order.created_at) >= start_date,
                func.date(Order.created_at) <= end_date,
                Order.status == 'completed'
            )
        )

        if store_id:
            orders_query = orders_query.filter(Order.store_id == store_id)

        orders = orders_query.all()

        # 客户生命周期价值分析
        clv_analysis = _calculate_customer_lifetime_value(db, orders)

        # 产品关联分析
        product_association = _analyze_product_association(db, orders)

        # 时间序列预测
        forecast_data = _generate_forecast(orders)

        # 客户细分分析
        customer_segmentation = _analyze_customer_segmentation(db, orders)

        # 技师绩效分析
        technician_performance = _analyze_technician_performance(db, orders)

        # 营销效果分析
        marketing_effectiveness = _analyze_marketing_effectiveness(db, orders)

        result = {
            "success": True,
            "data": {
                "period": f"{start_date} to {end_date}",
                "customer_lifetime_value": clv_analysis,
                "product_association": product_association,
                "forecast": forecast_data,
                "customer_segmentation": customer_segmentation,
                "technician_performance": technician_performance,
                "marketing_effectiveness": marketing_effectiveness,
                "generated_at": datetime.now().isoformat()
            }
        }

        # 缓存结果
        redis_cache.set(cache_key, result, expire=CACHE_EXPIRE_TIME["trend_prediction"])

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取高级分析失败: {str(e)}")


@router.get("/real-time-dashboard")
def get_real_time_dashboard(
    store_id: Optional[int] = Query(None, description="门店ID"),
    db: Session = Depends(get_db),
    current_employee: EmployeeSchema = Depends(get_current_employee)
):
    """
    获取实时仪表盘数据
    """
    try:
        # 今日数据
        today = datetime.now().date()
        today_orders = db.query(Order).filter(
            and_(
                func.date(Order.created_at) == today,
                Order.store_id == store_id if store_id else True
            )
        ).all()

        # 实时指标
        real_time_metrics = {
            "today_revenue": sum(order.total_amount for order in today_orders if order.status == 'completed'),
            "today_orders": len([order for order in today_orders if order.status == 'completed']),
            "pending_orders": len([order for order in today_orders if order.status == 'pending']),
            "active_customers": len(set(order.customer_id for order in today_orders)),
            "avg_order_value": 0,
            "hourly_revenue": {},
            "top_services": [],
            "technician_performance": []
        }

        if real_time_metrics["today_orders"] > 0:
            real_time_metrics["avg_order_value"] = real_time_metrics["today_revenue"] / real_time_metrics["today_orders"]

        # 按小时统计
        for order in today_orders:
            if order.status == 'completed':
                hour = order.created_at.hour
                if hour not in real_time_metrics["hourly_revenue"]:
                    real_time_metrics["hourly_revenue"][hour] = 0
                real_time_metrics["hourly_revenue"][hour] += order.total_amount

        # 与昨日对比
        yesterday = today - timedelta(days=1)
        yesterday_orders = db.query(Order).filter(
            and_(
                func.date(Order.created_at) == yesterday,
                Order.status == 'completed',
                Order.store_id == store_id if store_id else True
            )
        ).all()

        yesterday_revenue = sum(order.total_amount for order in yesterday_orders)
        revenue_change = ((real_time_metrics["today_revenue"] - yesterday_revenue) / yesterday_revenue * 100) if yesterday_revenue > 0 else 0

        comparison = {
            "yesterday_revenue": yesterday_revenue,
            "revenue_change_percent": revenue_change,
            "yesterday_orders": len(yesterday_orders),
            "orders_change_percent": ((real_time_metrics["today_orders"] - len(yesterday_orders)) / len(yesterday_orders) * 100) if yesterday_orders else 0
        }

        return {
            "success": True,
            "data": {
                "real_time_metrics": real_time_metrics,
                "comparison": comparison,
                "last_updated": datetime.now().isoformat()
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取实时仪表盘失败: {str(e)}")


# 辅助函数
def _calculate_customer_lifetime_value(db: Session, orders: List[Order]) -> Dict[str, Any]:
    """计算客户生命周期价值"""
    try:
        customer_data = {}

        for order in orders:
            customer_id = order.customer_id
            if customer_id not in customer_data:
                customer_data[customer_id] = {
                    'total_spent': 0,
                    'order_count': 0,
                    'first_order': order.created_at,
                    'last_order': order.created_at
                }

            customer_data[customer_id]['total_spent'] += order.total_amount
            customer_data[customer_id]['order_count'] += 1

            if order.created_at < customer_data[customer_id]['first_order']:
                customer_data[customer_id]['first_order'] = order.created_at
            if order.created_at > customer_data[customer_id]['last_order']:
                customer_data[customer_id]['last_order'] = order.created_at

        # 计算CLV指标
        clv_metrics = []
        total_clv = 0

        for customer_id, data in customer_data.items():
            # 计算客户生命周期（天）
            lifecycle_days = (data['last_order'] - data['first_order']).days + 1

            # 平均订单价值
            avg_order_value = data['total_spent'] / data['order_count']

            # 购买频率（每月）
            purchase_frequency = data['order_count'] / max(lifecycle_days / 30, 1)

            # 简化的CLV计算
            clv = avg_order_value * purchase_frequency * 12  # 假设客户生命周期1年

            clv_metrics.append({
                'customer_id': customer_id,
                'clv': clv,
                'total_spent': data['total_spent'],
                'order_count': data['order_count'],
                'avg_order_value': avg_order_value,
                'purchase_frequency': purchase_frequency,
                'lifecycle_days': lifecycle_days
            })

            total_clv += clv

        # 排序并获取高价值客户
        clv_metrics.sort(key=lambda x: x['clv'], reverse=True)
        high_value_customers = clv_metrics[:10]

        # 计算分布
        clv_ranges = {
            'high': len([c for c in clv_metrics if c['clv'] >= 5000]),
            'medium': len([c for c in clv_metrics if 2000 <= c['clv'] < 5000]),
            'low': len([c for c in clv_metrics if c['clv'] < 2000])
        }

        return {
            'total_customers': len(customer_data),
            'average_clv': total_clv / len(customer_data) if customer_data else 0,
            'total_clv': total_clv,
            'high_value_customers': high_value_customers,
            'clv_distribution': clv_ranges
        }

    except Exception as e:
        return {}


def _analyze_product_association(db: Session, orders: List[Order]) -> Dict[str, Any]:
    """分析产品关联性"""
    try:
        # 简化的关联分析
        product_combinations = {}

        # 这里假设订单中有项目信息
        for order in orders:
            # 模拟订单包含的项目
            items = ['按摩服务', '足疗', '精油', '会员卡']  # 模拟数据

            # 计算两两组合
            for i in range(len(items)):
                for j in range(i + 1, len(items)):
                    combination = tuple(sorted([items[i], items[j]]))
                    if combination not in product_combinations:
                        product_combinations[combination] = 0
                    product_combinations[combination] += 1

        # 排序获取最常见的组合
        sorted_combinations = sorted(
            product_combinations.items(),
            key=lambda x: x[1],
            reverse=True
        )

        # 计算支持度和置信度
        total_orders = len(orders)
        association_rules = []

        for (item1, item2), count in sorted_combinations[:10]:
            support = count / total_orders
            confidence = support * 1.2  # 简化的置信度计算

            association_rules.append({
                'item1': item1,
                'item2': item2,
                'support': support,
                'confidence': min(confidence, 1.0),
                'frequency': count
            })

        return {
            'association_rules': association_rules,
            'total_combinations': len(product_combinations)
        }

    except Exception as e:
        return {}


def _generate_forecast(orders: List[Order]) -> Dict[str, Any]:
    """生成预测数据"""
    try:
        # 按日期聚合订单数据
        daily_data = {}
        for order in orders:
            order_date = order.created_at.date()
            if order_date not in daily_data:
                daily_data[order_date] = {
                    'revenue': 0,
                    'order_count': 0
                }
            daily_data[order_date]['revenue'] += order.total_amount
            daily_data[order_date]['order_count'] += 1

        # 简化的线性趋势预测
        dates = sorted(daily_data.keys())
        revenues = [daily_data[d]['revenue'] for d in dates]

        if len(revenues) >= 7:
            # 计算7天移动平均
            moving_avg = []
            for i in range(6, len(revenues)):
                avg = sum(revenues[i-6:i+1]) / 7
                moving_avg.append(avg)

            # 简单线性预测未来7天
            if len(moving_avg) >= 2:
                trend = (moving_avg[-1] - moving_avg[-2])
                forecast_data = []

                for i in range(1, 8):
                    forecast_date = dates[-1] + timedelta(days=i)
                    forecast_revenue = moving_avg[-1] + trend * i
                    forecast_data.append({
                        'date': forecast_date.isoformat(),
                        'predicted_revenue': max(0, forecast_revenue),
                        'confidence': max(0.5, 1 - i * 0.1)  # 置信度递减
                    })

                return {
                    'historical_data': [
                        {
                            'date': d.isoformat(),
                            'revenue': daily_data[d]['revenue'],
                            'order_count': daily_data[d]['order_count']
                        }
                        for d in dates[-14:]  # 最近14天
                    ],
                    'forecast_data': forecast_data,
                    'trend_direction': 'up' if trend > 0 else 'down' if trend < 0 else 'stable',
                    'trend_strength': abs(trend)
                }

        return {
            'message': '数据不足，无法生成预测',
            'historical_data': [],
            'forecast_data': []
        }

    except Exception as e:
        return {}


def _analyze_customer_segmentation(db: Session, orders: List[Order]) -> Dict[str, Any]:
    """分析客户细分"""
    try:
        # RFM分析（Recency, Frequency, Monetary）
        customer_rfm = {}
        current_date = datetime.now().date()

        for order in orders:
            customer_id = order.customer_id
            if customer_id not in customer_rfm:
                customer_rfm[customer_id] = {
                    'last_order_date': order.created_at.date(),
                    'total_orders': 0,
                    'total_spent': 0
                }

            customer_rfm[customer_id]['total_orders'] += 1
            customer_rfm[customer_id]['total_spent'] += order.total_amount

            if order.created_at.date() > customer_rfm[customer_id]['last_order_date']:
                customer_rfm[customer_id]['last_order_date'] = order.created_at.date()

        # 计算RFM分数
        rfm_data = []
        for customer_id, data in customer_rfm.items():
            recency = (current_date - data['last_order_date']).days
            frequency = data['total_orders']
            monetary = data['total_spent']

            rfm_data.append({
                'customer_id': customer_id,
                'recency': recency,
                'frequency': frequency,
                'monetary': monetary
            })

        # 简化的客户分段
        segments = {
            'champions': [],      # 高频高额近期客户
            'loyal_customers': [], # 忠诚客户
            'potential_loyalists': [], # 潜在忠诚客户
            'at_risk': [],        # 流失风险客户
            'lost_customers': []  # 已流失客户
        }

        for customer in rfm_data:
            if customer['recency'] <= 30 and customer['frequency'] >= 5 and customer['monetary'] >= 1000:
                segments['champions'].append(customer)
            elif customer['frequency'] >= 3 and customer['monetary'] >= 500:
                segments['loyal_customers'].append(customer)
            elif customer['recency'] <= 60 and customer['frequency'] >= 2:
                segments['potential_loyalists'].append(customer)
            elif customer['recency'] > 60 and customer['recency'] <= 120:
                segments['at_risk'].append(customer)
            else:
                segments['lost_customers'].append(customer)

        # 计算各段占比
        total_customers = len(rfm_data)
        segment_stats = {}
        for segment, customers in segments.items():
            segment_stats[segment] = {
                'count': len(customers),
                'percentage': len(customers) / total_customers * 100 if total_customers > 0 else 0,
                'avg_monetary': sum(c['monetary'] for c in customers) / len(customers) if customers else 0
            }

        return {
            'total_customers': total_customers,
            'segments': segment_stats,
            'rfm_analysis': rfm_data[:20]  # 返回前20个客户的详细数据
        }

    except Exception as e:
        return {}


def _analyze_technician_performance(db: Session, orders: List[Order]) -> Dict[str, Any]:
    """分析技师绩效"""
    try:
        technician_stats = {}

        for order in orders:
            tech_id = order.technician_id
            if tech_id and tech_id not in technician_stats:
                technician_stats[tech_id] = {
                    'order_count': 0,
                    'total_revenue': 0,
                    'customer_ids': set()
                }

            if tech_id:
                technician_stats[tech_id]['order_count'] += 1
                technician_stats[tech_id]['total_revenue'] += order.total_amount
                technician_stats[tech_id]['customer_ids'].add(order.customer_id)

        # 计算绩效指标
        performance_data = []
        for tech_id, stats in technician_stats.items():
            avg_order_value = stats['total_revenue'] / stats['order_count'] if stats['order_count'] > 0 else 0
            customer_count = len(stats['customer_ids'])

            performance_data.append({
                'technician_id': tech_id,
                'order_count': stats['order_count'],
                'total_revenue': stats['total_revenue'],
                'avg_order_value': avg_order_value,
                'customer_count': customer_count,
                'revenue_per_customer': stats['total_revenue'] / customer_count if customer_count > 0 else 0
            })

        # 排序
        performance_data.sort(key=lambda x: x['total_revenue'], reverse=True)

        return {
            'technician_performance': performance_data,
            'top_performers': performance_data[:5],
            'total_technicians': len(performance_data)
        }

    except Exception as e:
        return {}


def _analyze_marketing_effectiveness(db: Session, orders: List[Order]) -> Dict[str, Any]:
    """分析营销效果"""
    try:
        # 这里应该结合营销活动数据
        # 现在返回模拟分析

        marketing_channels = {
            'online_ads': {'orders': 45, 'revenue': 12500, 'cost': 2000},
            'social_media': {'orders': 32, 'revenue': 8900, 'cost': 800},
            'referral': {'orders': 28, 'revenue': 9200, 'cost': 500},
            'direct': {'orders': 95, 'revenue': 28000, 'cost': 0}
        }

        channel_analysis = []
        for channel, data in marketing_channels.items():
            roi = (data['revenue'] - data['cost']) / data['cost'] * 100 if data['cost'] > 0 else float('inf')
            cac = data['cost'] / data['orders'] if data['orders'] > 0 else 0  # Customer Acquisition Cost

            channel_analysis.append({
                'channel': channel,
                'orders': data['orders'],
                'revenue': data['revenue'],
                'cost': data['cost'],
                'roi': roi,
                'cac': cac,
                'avg_order_value': data['revenue'] / data['orders'] if data['orders'] > 0 else 0
            })

        # 排序
        channel_analysis.sort(key=lambda x: x['roi'], reverse=True)

        return {
            'channel_analysis': channel_analysis,
            'best_performing_channel': channel_analysis[0] if channel_analysis else None,
            'total_marketing_spend': sum(data['cost'] for data in marketing_channels.values()),
            'total_marketing_revenue': sum(data['revenue'] for data in marketing_channels.values())
        }

    except Exception as e:
        return {}


@router.get("/advanced-analytics/customer-segmentation")
def get_customer_segmentation_analysis(
    store_id: Optional[int] = Query(None, description="门店ID"),
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_active_employee)
):
    """
    客户细分分析
    """
    try:
        from app.services.advanced_analytics import AdvancedAnalyticsService

        service = AdvancedAnalyticsService(db)
        result = service.customer_segmentation_analysis(store_id)

        return {
            "success": True,
            "data": result
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"客户细分分析失败: {str(e)}")


@router.get("/advanced-analytics/sales-forecasting")
def get_sales_forecasting_analysis(
    store_id: Optional[int] = Query(None, description="门店ID"),
    forecast_days: int = Query(30, description="预测天数"),
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_active_employee)
):
    """
    销售预测分析
    """
    try:
        from app.services.advanced_analytics import AdvancedAnalyticsService

        service = AdvancedAnalyticsService(db)
        result = service.sales_forecasting_analysis(store_id, forecast_days)

        return {
            "success": True,
            "data": result
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"销售预测分析失败: {str(e)}")


@router.get("/advanced-analytics/cohort-analysis")
def get_cohort_analysis(
    store_id: Optional[int] = Query(None, description="门店ID"),
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_active_employee)
):
    """
    队列分析
    """
    try:
        from app.services.advanced_analytics import AdvancedAnalyticsService

        service = AdvancedAnalyticsService(db)
        result = service.cohort_analysis(store_id)

        return {
            "success": True,
            "data": result
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"队列分析失败: {str(e)}")


@router.get("/advanced-analytics/analysis-types")
def get_analysis_types(
    current_employee: Employee = Depends(get_current_active_employee)
):
    """
    获取分析类型
    """
    try:
        analysis_types = [
            {
                "value": "customer_segmentation",
                "label": "客户细分分析",
                "description": "基于RFM模型的客户细分，识别不同价值的客户群体",
                "complexity": "中等",
                "data_requirement": "客户交易数据",
                "output": "客户群体分类、特征分析、营销建议"
            },
            {
                "value": "sales_forecasting",
                "label": "销售预测分析",
                "description": "基于历史数据预测未来销售趋势",
                "complexity": "高",
                "data_requirement": "历史销售数据",
                "output": "未来销售预测、趋势分析、置信区间"
            },
            {
                "value": "cohort_analysis",
                "label": "队列分析",
                "description": "分析不同时期客户的留存率和行为变化",
                "complexity": "中等",
                "data_requirement": "客户订单历史",
                "output": "留存率分析、客户生命周期洞察"
            }
        ]

        return {
            "success": True,
            "data": {
                "analysis_types": analysis_types,
                "total_count": len(analysis_types)
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分析类型失败: {str(e)}")


@router.get("/advanced-analytics/insights-summary")
def get_insights_summary(
    store_id: Optional[int] = Query(None, description="门店ID"),
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_active_employee)
):
    """
    获取洞察摘要
    """
    try:
        # 这里应该运行多个分析并汇总洞察
        # 现在返回模拟数据

        insights_summary = {
            "generated_at": "2024-01-20T15:30:00",
            "store_id": store_id,
            "key_insights": [
                {
                    "category": "客户分析",
                    "insight": "VIP客户群体占比15%，但贡献了45%的收入",
                    "importance": "high",
                    "action_required": True
                },
                {
                    "category": "销售趋势",
                    "insight": "预测下月收入将增长12%，主要由周末业务驱动",
                    "importance": "medium",
                    "action_required": False
                },
                {
                    "category": "客户留存",
                    "insight": "新客户3个月留存率为65%，高于行业平均水平",
                    "importance": "medium",
                    "action_required": False
                }
            ],
            "recommendations": [
                {
                    "priority": "high",
                    "category": "客户关系",
                    "recommendation": "为VIP客户建立专属服务通道",
                    "expected_impact": "提升客户满意度和忠诚度"
                },
                {
                    "priority": "medium",
                    "category": "运营优化",
                    "recommendation": "增加周末时段的服务供给",
                    "expected_impact": "满足需求增长，提升收入"
                }
            ],
            "performance_metrics": {
                "customer_satisfaction_trend": "上升",
                "revenue_growth_rate": 8.5,
                "customer_retention_rate": 72.3,
                "average_order_value": 285.6
            }
        }

        return {
            "success": True,
            "data": insights_summary
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取洞察摘要失败: {str(e)}")