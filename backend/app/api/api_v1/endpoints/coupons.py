from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import random
import string

from app import schemas, models
from app.api import deps
from app.schemas.marketing import CouponTemplateCreate, CouponTemplateUpdate, CouponTemplateInDB
from app.schemas.marketing import CouponCreate, CouponUpdate, CouponInDB, CouponWithTemplate
from app.schemas.marketing import Bat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, VerifyCoupon, CouponStatus, CouponIssueType
from app.models.marketing import CouponTemplate, Coupon

router = APIRouter()


@router.post("/templates/", response_model=CouponTemplateInDB)
def create_coupon_template(
    *,
    db: Session = Depends(deps.get_db),
    template_in: CouponTemplateCreate,
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["manage_marketing"]))
) -> Any:
    """
    创建优惠券模板
    """
    coupon_template = CouponTemplate(
        name=template_in.name,
        description=template_in.description,
        type=template_in.type,
        value=template_in.value,
        min_amount=template_in.min_amount,
        valid_days=template_in.valid_days,
        valid_start=template_in.valid_start,
        valid_end=template_in.valid_end,
        quantity=template_in.quantity,
        is_active=template_in.is_active
    )
    db.add(coupon_template)
    db.commit()
    db.refresh(coupon_template)
    return coupon_template


@router.get("/templates/", response_model=List[CouponTemplateInDB])
def get_coupon_templates(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["view_marketing"]))
) -> Any:
    """
    获取优惠券模板列表
    """
    templates = db.query(CouponTemplate).offset(skip).limit(limit).all()
    return templates


@router.get("/templates/{template_id}", response_model=CouponTemplateInDB)
def get_coupon_template(
    *,
    db: Session = Depends(deps.get_db),
    template_id: int = Path(..., gt=0, description="template_id"),
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["view_marketing"]))
) -> Any:
    """
    获取优惠券模板详情
    """
    template = db.query(CouponTemplate).filter(CouponTemplate.id == template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="优惠券模板不存在")
    return template


@router.put("/templates/{template_id}", response_model=CouponTemplateInDB)
def update_coupon_template(
    *,
    db: Session = Depends(deps.get_db),
    template_id: int = Path(..., gt=0, description="template_id"),
    template_in: CouponTemplateUpdate,
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["manage_marketing"]))
) -> Any:
    """
    更新优惠券模板
    """
    template = db.query(CouponTemplate).filter(CouponTemplate.id == template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="优惠券模板不存在")
    
    update_data = template_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(template, field, value)
    
    db.add(template)
    db.commit()
    db.refresh(template)
    return template


@router.delete("/templates/{template_id}", response_model=CouponTemplateInDB)
def delete_coupon_template(
    *,
    db: Session = Depends(deps.get_db),
    template_id: int = Path(..., gt=0, description="template_id"),
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["manage_marketing"]))
) -> Any:
    """
    删除优惠券模板
    """
    template = db.query(CouponTemplate).filter(CouponTemplate.id == template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="优惠券模板不存在")
    
    # 检查是否有关联的优惠券
    coupon_count = db.query(Coupon).filter(Coupon.template_id == template_id).count()
    if coupon_count > 0:
        raise HTTPException(status_code=400, detail=f"该模板已发放{coupon_count}张优惠券，无法删除")
    
    db.delete(template)
    db.commit()
    return template


def generate_coupon_code(length: int = 12) -> str:
    """
    生成随机优惠券码
    """
    chars = string.ascii_uppercase + string.digits
    return ''.join(random.choice(chars) for _ in range(length))


@router.post("/", response_model=CouponInDB)
def create_coupon(
    *,
    db: Session = Depends(deps.get_db),
    coupon_in: CouponCreate,
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["manage_marketing"]))
) -> Any:
    """
    创建单个优惠券
    """
    # 检查模板是否存在
    template = db.query(CouponTemplate).filter(CouponTemplate.id == coupon_in.template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="优惠券模板不存在")
    
    # 检查客户是否存在
    customer = db.query(models.Customer).filter(models.Customer.id == coupon_in.customer_id).first()
    if not customer:
        raise HTTPException(status_code=404, detail="客户不存在")
    
    # 检查模板数量限制
    if template.quantity > 0 and template.issued_count >= template.quantity:
        raise HTTPException(status_code=400, detail="优惠券已发放完毕")
    
    # 生成优惠券码
    code = coupon_in.code if coupon_in.code else generate_coupon_code()
    
    # 计算有效期
    valid_start = coupon_in.valid_start or template.valid_start or datetime.now()
    valid_end = coupon_in.valid_end or template.valid_end
    if not valid_end and template.valid_days:
        valid_end = valid_start + timedelta(days=template.valid_days)
    
    # 创建优惠券
    coupon = Coupon(
        code=code,
        template_id=template.id,
        customer_id=customer.id,
        status=CouponStatus.UNUSED,
        issue_type=coupon_in.issue_type,
        valid_start=valid_start,
        valid_end=valid_end
    )
    
    db.add(coupon)
    
    # 更新模板发放数量
    template.issued_count += 1
    db.add(template)
    
    db.commit()
    db.refresh(coupon)
    return coupon


@router.post("/batch-issue/", response_model=List[CouponInDB])
def batch_issue_coupons(
    *,
    db: Session = Depends(deps.get_db),
    batch_in: BatchIssueCoupons,
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["manage_marketing"]))
) -> Any:
    """
    批量发放优惠券
    """
    # 检查模板是否存在
    template = db.query(CouponTemplate).filter(CouponTemplate.id == batch_in.template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="优惠券模板不存在")
    
    # 检查模板数量限制
    if template.quantity > 0 and template.issued_count + len(batch_in.customer_ids) > template.quantity:
        raise HTTPException(status_code=400, detail="优惠券数量不足")
    
    # 计算有效期
    valid_start = template.valid_start or datetime.now()
    valid_end = template.valid_end
    if not valid_end and template.valid_days:
        valid_end = valid_start + timedelta(days=template.valid_days)
    
    # 批量创建优惠券
    coupons = []
    for customer_id in batch_in.customer_ids:
        # 检查客户是否存在
        customer = db.query(models.Customer).filter(models.Customer.id == customer_id).first()
        if not customer:
            continue
        
        code = generate_coupon_code()
        coupon = Coupon(
            code=code,
            template_id=template.id,
            customer_id=customer_id,
            status=CouponStatus.UNUSED,
            issue_type=batch_in.issue_type,
            valid_start=valid_start,
            valid_end=valid_end
        )
        db.add(coupon)
        coupons.append(coupon)
    
    # 更新模板发放数量
    template.issued_count += len(coupons)
    db.add(template)
    
    db.commit()
    for coupon in coupons:
        db.refresh(coupon)
    
    return coupons


@router.get("/", response_model=List[CouponWithTemplate])
def get_coupons(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    customer_id: Optional[int] = None,
    status: Optional[CouponStatus] = None,
    template_id: Optional[int] = None,
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["view_marketing"]))
) -> Any:
    """
    获取优惠券列表，支持按客户、状态和模板筛选
    """
    query = db.query(Coupon)
    
    if customer_id:
        query = query.filter(Coupon.customer_id == customer_id)
    if status:
        query = query.filter(Coupon.status == status)
    if template_id:
        query = query.filter(Coupon.template_id == template_id)
    
    coupons = query.offset(skip).limit(limit).all()
    return coupons


@router.get("/customer/{customer_id}", response_model=List[CouponWithTemplate])
def get_customer_coupons(
    *,
    db: Session = Depends(deps.get_db),
    customer_id: int = Path(..., gt=0, description="客户ID"),
    status: Optional[CouponStatus] = None,
    current_employee: models.Employee = Depends(deps.get_current_employee)
) -> Any:
    """
    获取指定客户的优惠券列表
    """
    query = db.query(Coupon).filter(Coupon.customer_id == customer_id)
    
    if status:
        query = query.filter(Coupon.status == status)
    
    coupons = query.all()
    return coupons


@router.get("/{coupon_id}", response_model=CouponWithTemplate)
def get_coupon(
    *,
    db: Session = Depends(deps.get_db),
    coupon_id: int = Path(..., gt=0, description="coupon_id"),
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["view_marketing"]))
) -> Any:
    """
    获取优惠券详情
    """
    coupon = db.query(Coupon).filter(Coupon.id == coupon_id).first()
    if not coupon:
        raise HTTPException(status_code=404, detail="优惠券不存在")
    return coupon


@router.put("/{coupon_id}", response_model=CouponInDB)
def update_coupon(
    *,
    db: Session = Depends(deps.get_db),
    coupon_id: int = Path(..., gt=0, description="coupon_id"),
    coupon_in: CouponUpdate,
    current_employee: models.Employee = Depends(deps.get_current_employee_with_permissions(["manage_marketing"]))
) -> Any:
    """
    更新优惠券状态
    """
    coupon = db.query(Coupon).filter(Coupon.id == coupon_id).first()
    if not coupon:
        raise HTTPException(status_code=404, detail="优惠券不存在")
    
    update_data = coupon_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(coupon, field, value)
    
    # 如果状态变更为已使用，更新模板的使用数量
    if coupon_in.status == CouponStatus.USED and coupon.status != CouponStatus.USED:
        template = db.query(CouponTemplate).filter(CouponTemplate.id == coupon.template_id).first()
        if template:
            template.used_count += 1
            db.add(template)
    
    db.add(coupon)
    db.commit()
    db.refresh(coupon)
    return coupon


@router.post("/verify", response_model=dict)
def verify_coupon(
    *,
    db: Session = Depends(deps.get_db),
    verify_in: VerifyCoupon,
    current_employee: models.Employee = Depends(deps.get_current_employee)
) -> Any:
    """
    验证优惠券是否可用，并返回优惠信息
    """
    coupon = db.query(Coupon).filter(Coupon.code == verify_in.code).first()
    if not coupon:
        raise HTTPException(status_code=404, detail="优惠券不存在")
    
    # 检查优惠券状态
    if coupon.status != CouponStatus.UNUSED:
        raise HTTPException(status_code=400, detail=f"优惠券状态为{coupon.status}，无法使用")
    
    # 检查有效期
    now = datetime.now()
    if coupon.valid_start and coupon.valid_start > now:
        raise HTTPException(status_code=400, detail="优惠券尚未生效")
    if coupon.valid_end and coupon.valid_end < now:
        coupon.status = CouponStatus.EXPIRED
        db.add(coupon)
        db.commit()
        raise HTTPException(status_code=400, detail="优惠券已过期")
    
    # 获取优惠券模板
    template = db.query(CouponTemplate).filter(CouponTemplate.id == coupon.template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="优惠券模板不存在")
    
    # 检查最低消费金额
    if verify_in.order_amount < template.min_amount:
        raise HTTPException(status_code=400, detail=f"订单金额不满足最低消费要求{template.min_amount}元")
    
    # 计算优惠金额
    discount_amount = 0
    if template.type == "cash":
        discount_amount = template.value
    elif template.type == "discount":
        discount_amount = verify_in.order_amount * (1 - template.value / 10)
    elif template.type == "experience":
        discount_amount = verify_in.order_amount
    
    return {
        "coupon_id": coupon.id,
        "coupon_code": coupon.code,
        "coupon_type": template.type,
        "discount_amount": round(discount_amount, 2),
        "customer_id": coupon.customer_id,
        "is_valid": True
    } 