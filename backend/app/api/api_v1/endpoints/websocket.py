"""
WebSocket API端点
实现实时数据推送的WebSocket接口
"""

import json
import uuid
from typing import Dict, Any
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query
from app.services.websocket_manager import connection_manager, real_time_service
# from app.api.deps import get_current_employee_ws  # 暂时注释掉
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.websocket("/ws/{client_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    client_id: str,
    token: str = Query(None, description="认证token")
):
    """
    WebSocket连接端点
    
    客户端可以通过此端点建立WebSocket连接，接收实时数据推送
    """
    try:
        # 验证客户端ID
        if not client_id:
            await websocket.close(code=4000, reason="客户端ID不能为空")
            return
        
        # 简单的token验证（在实际应用中应该更严格）
        user_info = {}
        if token:
            try:
                # 这里应该验证token并获取用户信息
                # 为了演示，我们简化处理
                user_info = {"token": token, "authenticated": True}
            except Exception as e:
                logger.warning(f"Token验证失败: {str(e)}")
                user_info = {"authenticated": False}
        
        # 建立连接
        await connection_manager.connect(websocket, client_id, user_info)
        
        # 启动实时数据服务（如果还没启动）
        if not real_time_service.is_running:
            import asyncio
            asyncio.create_task(real_time_service.start_real_time_updates())
        
        # 监听客户端消息
        while True:
            try:
                # 接收客户端消息
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # 处理不同类型的消息
                await handle_client_message(client_id, message)
                
            except WebSocketDisconnect:
                logger.info(f"客户端主动断开连接: {client_id}")
                break
            except json.JSONDecodeError:
                await connection_manager.send_personal_message(client_id, {
                    'type': 'error',
                    'message': '消息格式错误，请发送有效的JSON'
                })
            except Exception as e:
                logger.error(f"处理客户端消息失败: {client_id}, 错误: {str(e)}")
                await connection_manager.send_personal_message(client_id, {
                    'type': 'error',
                    'message': f'处理消息失败: {str(e)}'
                })
    
    except Exception as e:
        logger.error(f"WebSocket连接失败: {client_id}, 错误: {str(e)}")
    
    finally:
        # 断开连接
        connection_manager.disconnect(client_id)


async def handle_client_message(client_id: str, message: Dict[str, Any]):
    """处理客户端消息"""
    try:
        message_type = message.get('type')
        
        if message_type == 'subscribe':
            # 订阅数据类型
            data_types = message.get('data_types', [])
            await connection_manager.subscribe(client_id, data_types)
            
        elif message_type == 'unsubscribe':
            # 取消订阅数据类型
            data_types = message.get('data_types', [])
            await connection_manager.unsubscribe(client_id, data_types)
            
        elif message_type == 'ping':
            # 心跳检测
            await connection_manager.send_personal_message(client_id, {
                'type': 'pong',
                'timestamp': message.get('timestamp')
            })
            
        elif message_type == 'get_stats':
            # 获取连接统计
            stats = connection_manager.get_connection_stats()
            await connection_manager.send_personal_message(client_id, {
                'type': 'stats',
                'data': stats
            })
            
        elif message_type == 'request_data':
            # 请求特定数据
            data_type = message.get('data_type')
            await handle_data_request(client_id, data_type, message.get('params', {}))
            
        else:
            await connection_manager.send_personal_message(client_id, {
                'type': 'error',
                'message': f'不支持的消息类型: {message_type}'
            })
            
    except Exception as e:
        logger.error(f"处理客户端消息失败: {client_id}, 错误: {str(e)}")
        await connection_manager.send_personal_message(client_id, {
            'type': 'error',
            'message': f'处理消息失败: {str(e)}'
        })


async def handle_data_request(client_id: str, data_type: str, params: Dict[str, Any]):
    """处理数据请求"""
    try:
        from app.db.session import SessionLocal
        from app.services.query_optimizer import QueryOptimizer
        from datetime import datetime, timedelta
        
        db = SessionLocal()
        try:
            if data_type == 'business_indicators':
                # 获取业务指标
                optimizer = QueryOptimizer(db)
                
                end_date = datetime.now()
                start_date = end_date - timedelta(days=7)  # 默认最近7天
                
                sales_data = optimizer.get_aggregated_sales_data(
                    start_date=start_date,
                    end_date=end_date,
                    group_by="day"
                )
                
                await connection_manager.send_personal_message(client_id, {
                    'type': 'data_response',
                    'data_type': data_type,
                    'data': {
                        'sales_data': sales_data,
                        'period': '最近7天',
                        'generated_at': datetime.now().isoformat()
                    }
                })
                
            elif data_type == 'real_time_orders':
                # 获取实时订单
                recent_orders = db.query(Order).filter(
                    Order.order_time >= datetime.now() - timedelta(hours=1),
                    Order.is_deleted == False
                ).order_by(Order.order_time.desc()).limit(20).all()
                
                orders_data = []
                for order in recent_orders:
                    orders_data.append({
                        'id': order.id,
                        'order_number': order.order_number,
                        'total_amount': float(order.total_amount),
                        'order_time': order.order_time.isoformat(),
                        'store_id': order.store_id
                    })
                
                await connection_manager.send_personal_message(client_id, {
                    'type': 'data_response',
                    'data_type': data_type,
                    'data': {
                        'orders': orders_data,
                        'count': len(orders_data),
                        'period': '最近1小时',
                        'generated_at': datetime.now().isoformat()
                    }
                })
                
            else:
                await connection_manager.send_personal_message(client_id, {
                    'type': 'error',
                    'message': f'不支持的数据类型: {data_type}'
                })
                
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"处理数据请求失败: {client_id}, 数据类型: {data_type}, 错误: {str(e)}")
        await connection_manager.send_personal_message(client_id, {
            'type': 'error',
            'message': f'获取数据失败: {str(e)}'
        })


@router.get("/ws/stats")
async def get_websocket_stats():
    """
    获取WebSocket连接统计信息
    """
    try:
        stats = connection_manager.get_connection_stats()
        return {
            "status": "success",
            "data": stats
        }
    except Exception as e:
        logger.error(f"获取WebSocket统计失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }


@router.post("/ws/broadcast")
async def broadcast_message(
    message: Dict[str, Any],
    data_type: str = Query(None, description="数据类型")
):
    """
    广播消息到所有连接的客户端
    
    仅用于测试和管理目的
    """
    try:
        await connection_manager.broadcast_message(message, data_type)
        return {
            "status": "success",
            "message": "消息已广播",
            "recipients": len(connection_manager.active_connections)
        }
    except Exception as e:
        logger.error(f"广播消息失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }


@router.post("/ws/send/{client_id}")
async def send_personal_message(
    client_id: str,
    message: Dict[str, Any]
):
    """
    发送个人消息到指定客户端
    
    仅用于测试和管理目的
    """
    try:
        await connection_manager.send_personal_message(client_id, message)
        return {
            "status": "success",
            "message": f"消息已发送到客户端 {client_id}"
        }
    except Exception as e:
        logger.error(f"发送个人消息失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }


@router.get("/ws/test")
async def test_websocket():
    """
    WebSocket测试页面
    
    返回一个简单的HTML页面用于测试WebSocket连接
    """
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>WebSocket测试</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .container { max-width: 800px; margin: 0 auto; }
            .message-box { 
                border: 1px solid #ccc; 
                height: 300px; 
                overflow-y: scroll; 
                padding: 10px; 
                margin: 10px 0; 
                background: #f9f9f9;
            }
            .controls { margin: 10px 0; }
            button { margin: 5px; padding: 8px 16px; }
            input, select { margin: 5px; padding: 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>WebSocket实时数据测试</h1>
            
            <div class="controls">
                <input type="text" id="clientId" placeholder="客户端ID" value="test-client-1">
                <button onclick="connect()">连接</button>
                <button onclick="disconnect()">断开</button>
                <span id="status">未连接</span>
            </div>
            
            <div class="controls">
                <select id="dataType">
                    <option value="business_indicators">业务指标</option>
                    <option value="real_time_orders">实时订单</option>
                    <option value="customer_activity">客户活动</option>
                    <option value="store_performance">门店表现</option>
                </select>
                <button onclick="subscribe()">订阅</button>
                <button onclick="unsubscribe()">取消订阅</button>
                <button onclick="requestData()">请求数据</button>
            </div>
            
            <div class="message-box" id="messages"></div>
            
            <div class="controls">
                <button onclick="clearMessages()">清空消息</button>
                <button onclick="ping()">心跳测试</button>
                <button onclick="getStats()">获取统计</button>
            </div>
        </div>

        <script>
            let ws = null;
            let clientId = 'test-client-1';
            
            function connect() {
                clientId = document.getElementById('clientId').value || 'test-client-1';
                const wsUrl = `ws://localhost:8000/api/v1/ws/${clientId}`;
                
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function(event) {
                    document.getElementById('status').textContent = '已连接';
                    addMessage('系统', '连接已建立');
                };
                
                ws.onmessage = function(event) {
                    const message = JSON.parse(event.data);
                    addMessage('服务器', JSON.stringify(message, null, 2));
                };
                
                ws.onclose = function(event) {
                    document.getElementById('status').textContent = '已断开';
                    addMessage('系统', '连接已断开');
                };
                
                ws.onerror = function(error) {
                    addMessage('错误', error.toString());
                };
            }
            
            function disconnect() {
                if (ws) {
                    ws.close();
                    ws = null;
                }
            }
            
            function subscribe() {
                if (!ws) return;
                const dataType = document.getElementById('dataType').value;
                ws.send(JSON.stringify({
                    type: 'subscribe',
                    data_types: [dataType]
                }));
            }
            
            function unsubscribe() {
                if (!ws) return;
                const dataType = document.getElementById('dataType').value;
                ws.send(JSON.stringify({
                    type: 'unsubscribe',
                    data_types: [dataType]
                }));
            }
            
            function requestData() {
                if (!ws) return;
                const dataType = document.getElementById('dataType').value;
                ws.send(JSON.stringify({
                    type: 'request_data',
                    data_type: dataType
                }));
            }
            
            function ping() {
                if (!ws) return;
                ws.send(JSON.stringify({
                    type: 'ping',
                    timestamp: new Date().toISOString()
                }));
            }
            
            function getStats() {
                if (!ws) return;
                ws.send(JSON.stringify({
                    type: 'get_stats'
                }));
            }
            
            function addMessage(sender, content) {
                const messagesDiv = document.getElementById('messages');
                const messageDiv = document.createElement('div');
                messageDiv.innerHTML = `<strong>${sender}:</strong> <pre>${content}</pre>`;
                messagesDiv.appendChild(messageDiv);
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            }
            
            function clearMessages() {
                document.getElementById('messages').innerHTML = '';
            }
        </script>
    </body>
    </html>
    """
    
    from fastapi.responses import HTMLResponse
    return HTMLResponse(content=html_content)
