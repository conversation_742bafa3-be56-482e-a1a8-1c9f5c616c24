from fastapi import APIRouter, Depends, HTTPException, status, Body
from sqlalchemy.orm import Session
from app.db.session import get_db
from app.models.employee import Employee as EmployeeModel
from app.models.shareholder import Shareholder as ShareholderModel
from app.core.security import verify_password, create_access_token
from app.schemas.token import Token
from app.schemas.employee import Employee  # 用Pydantic模型
from app.schemas.shareholder import ShareholderResponse
from app.api.deps import get_current_user, get_current_shareholder
from typing import Any

router = APIRouter()

@router.post('/login', response_model=Token)
def login(
    db: Session = Depends(get_db),
    body: dict = Body(...)
):
    # 只接受JSON格式
    username = body.get('phone')
    password = body.get('password')
    if not username or not password:
        raise HTTPException(status_code=400, detail='请提供手机号和密码')
    user = db.query(EmployeeModel).filter(EmployeeModel.phone == username).first()
    if not user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail='手机号或密码错误')
    if not verify_password(password, user.password):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail='手机号或密码错误')
    if user.status != 'active':
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail='账号已禁用')

    # 创建访问令牌，添加用户类型
    access_token = create_access_token(
        subject=str(user.id),
        user_type="employee"
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user_type": "employee",
        "user_id": user.id,
        "user_name": user.name
    }

@router.get('/me', response_model=Employee)
def get_me(current_user = Depends(get_current_user)):
    return Employee.model_validate(current_user, from_attributes=True)

@router.post('/logout')
def logout() -> Any:
    return {"msg": "已退出登录"}


@router.post('/shareholder/login', response_model=Token)
def shareholder_login(
    db: Session = Depends(get_db),
    body: dict = Body(...)
):
    """
    股东专用登录接口
    """
    # 只接受JSON格式
    phone = body.get('phone')
    password = body.get('password')

    if not phone or not password:
        raise HTTPException(status_code=400, detail='请提供手机号和密码')

    # 查找股东
    shareholder = db.query(ShareholderModel).filter(
        ShareholderModel.phone == phone,
        ShareholderModel.is_deleted == False
    ).first()

    if not shareholder:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail='手机号或密码错误'
        )

    # 验证密码
    if not verify_password(password, shareholder.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail='手机号或密码错误'
        )

    # 创建访问令牌，添加股东标识
    access_token = create_access_token(
        subject=str(shareholder.id),
        user_type="shareholder"  # 标识为股东用户
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user_type": "shareholder",
        "user_id": shareholder.id,
        "user_name": shareholder.name
    }


@router.get('/shareholder/me', response_model=ShareholderResponse)
def get_shareholder_me(current_shareholder = Depends(get_current_shareholder)):
    """
    获取当前登录股东信息
    """
    return ShareholderResponse.model_validate(current_shareholder, from_attributes=True)


@router.post('/shareholder/logout')
def shareholder_logout() -> Any:
    """
    股东退出登录
    """
    return {"msg": "股东已退出登录"}