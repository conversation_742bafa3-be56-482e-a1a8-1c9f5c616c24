from typing import Optional, Dict, Any
from datetime import date
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.db.session import get_db
from app.models.employee import Employee
from app.api.deps import get_current_active_employee
from app.services.voucher_generation import VoucherGenerationService

router = APIRouter()


class VoucherGenerationRequest(BaseModel):
    """凭证生成请求模型"""
    voucher_type: str  # revenue, salary, cost
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    year: Optional[int] = None
    month: Optional[int] = None
    store_id: Optional[int] = None


class VoucherValidationRequest(BaseModel):
    """凭证验证请求模型"""
    voucher_data: Dict[str, Any]


@router.post("/generate")
def generate_voucher(
    request: VoucherGenerationRequest,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_active_employee)
):
    """
    生成财务凭证
    """
    try:
        service = VoucherGenerationService(db)
        
        if request.voucher_type == 'revenue':
            if not request.start_date or not request.end_date:
                raise HTTPException(status_code=400, detail="收入凭证需要提供开始和结束日期")
            
            result = service.generate_revenue_voucher(
                request.start_date,
                request.end_date,
                request.store_id
            )
        
        elif request.voucher_type == 'salary':
            if not request.year or not request.month:
                raise HTTPException(status_code=400, detail="工资凭证需要提供年份和月份")
            
            result = service.generate_salary_voucher(
                request.year,
                request.month,
                request.store_id
            )
        
        elif request.voucher_type == 'cost':
            if not request.start_date or not request.end_date:
                raise HTTPException(status_code=400, detail="成本凭证需要提供开始和结束日期")
            
            result = service.generate_cost_voucher(
                request.start_date,
                request.end_date,
                request.store_id
            )
        
        else:
            raise HTTPException(status_code=400, detail="不支持的凭证类型")
        
        return {
            "success": True,
            "data": result
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成凭证失败: {str(e)}")


@router.post("/validate")
def validate_voucher(
    request: VoucherValidationRequest,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_active_employee)
):
    """
    验证凭证数据
    """
    try:
        service = VoucherGenerationService(db)
        result = service.validate_voucher(request.voucher_data)
        
        return {
            "success": True,
            "data": result
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"验证凭证失败: {str(e)}")


@router.get("/templates")
def get_voucher_templates(
    current_employee: Employee = Depends(get_current_active_employee)
):
    """
    获取凭证模板
    """
    try:
        templates = [
            {
                "type": "revenue",
                "name": "收入凭证",
                "description": "根据订单数据自动生成收入凭证",
                "required_fields": ["start_date", "end_date"],
                "optional_fields": ["store_id"]
            },
            {
                "type": "salary",
                "name": "工资凭证",
                "description": "根据员工数据自动生成工资凭证",
                "required_fields": ["year", "month"],
                "optional_fields": ["store_id"]
            },
            {
                "type": "cost",
                "name": "成本凭证",
                "description": "根据成本数据自动生成成本凭证",
                "required_fields": ["start_date", "end_date"],
                "optional_fields": ["store_id"]
            }
        ]
        
        return {
            "success": True,
            "data": templates
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取凭证模板失败: {str(e)}")


@router.get("/account-codes")
def get_account_codes(
    current_employee: Employee = Depends(get_current_active_employee)
):
    """
    获取会计科目代码
    """
    try:
        service = VoucherGenerationService(None)
        account_codes = service.account_codes
        
        # 转换为前端需要的格式
        account_list = []
        for key, code in account_codes.items():
            account_list.append({
                "code": code,
                "key": key,
                "name": _get_account_name(key)
            })
        
        return {
            "success": True,
            "data": account_list
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取科目代码失败: {str(e)}")


@router.post("/batch-generate")
def batch_generate_vouchers(
    voucher_types: list[str],
    start_date: date,
    end_date: date,
    store_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_active_employee)
):
    """
    批量生成凭证
    """
    try:
        service = VoucherGenerationService(db)
        results = []
        
        for voucher_type in voucher_types:
            if voucher_type == 'revenue':
                result = service.generate_revenue_voucher(start_date, end_date, store_id)
            elif voucher_type == 'cost':
                result = service.generate_cost_voucher(start_date, end_date, store_id)
            elif voucher_type == 'salary':
                # 工资凭证按月生成
                result = service.generate_salary_voucher(start_date.year, start_date.month, store_id)
            else:
                continue
            
            results.append({
                "voucher_type": voucher_type,
                "result": result
            })
        
        return {
            "success": True,
            "data": {
                "results": results,
                "total_count": len(results)
            }
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量生成凭证失败: {str(e)}")


@router.get("/preview")
def preview_voucher(
    voucher_type: str,
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    year: Optional[int] = Query(None, description="year"),
    month: Optional[int] = Query(None, description="month"),
    store_id: Optional[int] = Query(None, description="门店ID"),
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_active_employee)
):
    """
    预览凭证数据
    """
    try:
        service = VoucherGenerationService(db)
        
        if voucher_type == 'revenue':
            if not start_date or not end_date:
                raise HTTPException(status_code=400, detail="收入凭证需要提供开始和结束日期")
            result = service.generate_revenue_voucher(start_date, end_date, store_id)
        
        elif voucher_type == 'salary':
            if not year or not month:
                raise HTTPException(status_code=400, detail="工资凭证需要提供年份和月份")
            result = service.generate_salary_voucher(year, month, store_id)
        
        elif voucher_type == 'cost':
            if not start_date or not end_date:
                raise HTTPException(status_code=400, detail="成本凭证需要提供开始和结束日期")
            result = service.generate_cost_voucher(start_date, end_date, store_id)
        
        else:
            raise HTTPException(status_code=400, detail="不支持的凭证类型")
        
        # 添加验证信息
        if result['success'] and result['data']:
            validation = service.validate_voucher(result['data'])
            result['data']['validation'] = validation
        
        return {
            "success": True,
            "data": result
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"预览凭证失败: {str(e)}")


def _get_account_name(key: str) -> str:
    """获取科目名称"""
    account_names = {
        'cash': '库存现金',
        'bank_deposit': '银行存款',
        'accounts_receivable': '应收账款',
        'prepaid_expenses': '待摊费用',
        'fixed_assets': '固定资产',
        'accounts_payable': '应付账款',
        'accrued_expenses': '应付职工薪酬',
        'taxes_payable': '应交税费',
        'paid_in_capital': '实收资本',
        'retained_earnings': '未分配利润',
        'main_business_income': '主营业务收入',
        'other_business_income': '其他业务收入',
        'main_business_cost': '主营业务成本',
        'administrative_expenses': '管理费用',
        'selling_expenses': '销售费用',
        'financial_expenses': '财务费用'
    }
    return account_names.get(key, key)
