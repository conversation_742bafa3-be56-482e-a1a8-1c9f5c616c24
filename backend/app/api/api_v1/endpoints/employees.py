from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, status
from sqlalchemy.orm import Session
from datetime import datetime

from app.db.session import get_db
from app.models.employee import Employee
from app.models.store import Store
from app.core import security
from app.schemas.employee import Employee as EmployeeSchema
from app.schemas.employee import EmployeeCreate, EmployeeUpdate

router = APIRouter()


@router.get("/", response_model=List[EmployeeSchema], summary="获取员工列表")
def read_employees(
    db: Session = Depends(get_db),
    skip: int = Query(0, description="跳过的记录数"),
    limit: int = Query(100, description="返回的记录数"),
    store_id: Optional[int] = Query(None, description="门店ID筛选"),
    role: Optional[str] = Query(None, description="角色筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
) -> Any:
    """
    获取员工列表

    支持分页查询和多条件筛选，返回员工的基本信息和工作状态
    """
    # 构建查询过滤条件
    query = db.query(Employee).filter(Employee.is_deleted == False)
    
    if store_id:
        query = query.filter(Employee.store_id == store_id)
    
    if role:
        query = query.filter(Employee.role == role)
        
    if status:
        query = query.filter(Employee.status == status)
    
    return query.offset(skip).limit(limit).all()


@router.post("/", response_model=EmployeeSchema, status_code=status.HTTP_201_CREATED)
def create_employee(
    *,
    db: Session = Depends(get_db),
    employee_in: EmployeeCreate,
) -> Any:
    """
    创建新员工
    """
    # 检查门店是否存在
    store = db.query(Store).filter(Store.id == employee_in.store_id).first()
    if not store:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Store with ID {employee_in.store_id} not found"
        )
    
    # 检查手机号是否已被使用
    existing_employee = db.query(Employee).filter(Employee.phone == employee_in.phone).first()
    if existing_employee:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Phone number already registered"
        )
    
    # 创建员工对象
    employee = Employee(
        store_id=employee_in.store_id,
        name=employee_in.name,
        phone=employee_in.phone,
        password=security.get_password_hash(employee_in.password),
        role=employee_in.role,
        avatar=employee_in.photo_url,
        skills=employee_in.skills,
        attachments=employee_in.attachments,
        commission_scheme_id=employee_in.default_commission_scheme_id,
        entry_date=employee_in.hire_date,
        status=employee_in.status,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
    )
    
    db.add(employee)
    db.commit()
    db.refresh(employee)
    
    return employee


@router.get("/{employee_id}", response_model=EmployeeSchema)
def read_employee(
    *,
    db: Session = Depends(get_db),
    employee_id: int = Path(..., gt=0, description="员工ID"),
) -> Any:
    """
    获取特定员工的信息
    """
    employee = db.query(Employee).filter(Employee.id == employee_id, Employee.is_deleted == False).first()
    if not employee:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Employee with ID {employee_id} not found"
        )
    
    return employee


@router.put("/{employee_id}", response_model=EmployeeSchema)
def update_employee(
    *,
    db: Session = Depends(get_db),
    employee_id: int = Path(..., gt=0, description="员工ID"),
    employee_in: EmployeeUpdate,
) -> Any:
    """
    更新员工信息
    """
    employee = db.query(Employee).filter(Employee.id == employee_id, Employee.is_deleted == False).first()
    if not employee:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Employee with ID {employee_id} not found"
        )
    
    # 如果更新手机号，检查是否已被使用
    if employee_in.phone and employee_in.phone != employee.phone:
        existing_employee = db.query(Employee).filter(
            Employee.phone == employee_in.phone,
            Employee.id != employee_id
        ).first()
        if existing_employee:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Phone number already registered"
            )
    
    # 更新员工信息
    update_data = employee_in.model_dump(exclude_unset=True)
    
    # 特殊处理密码字段
    if "password" in update_data:
        password = update_data.pop("password")
        employee.password = security.get_password_hash(password)
    
    for field, value in update_data.items():
        setattr(employee, field, value)
    
    employee.updated_at = datetime.utcnow()
    
    db.add(employee)
    db.commit()
    db.refresh(employee)
    
    return employee


@router.delete("/{employee_id}", response_model=EmployeeSchema)
def delete_employee(
    *,
    db: Session = Depends(get_db),
    employee_id: int = Path(..., gt=0, description="员工ID"),
) -> Any:
    """
    软删除员工
    """
    employee = db.query(Employee).filter(Employee.id == employee_id, Employee.is_deleted == False).first()
    if not employee:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Employee with ID {employee_id} not found"
        )
    
    employee.is_deleted = True
    employee.status = "left"
    employee.updated_at = datetime.utcnow()
    
    db.add(employee)
    db.commit()
    db.refresh(employee)
    
    return employee 