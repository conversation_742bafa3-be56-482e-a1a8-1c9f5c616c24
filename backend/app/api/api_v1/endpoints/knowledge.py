from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session
from datetime import datetime

from app.db.session import get_db
from app.models.knowledge import KnowledgeTag, KnowledgeCategory, KnowledgeArticle, KnowledgeComment
from app.models.employee import Employee
from app.schemas.knowledge import (
    KnowledgeTag as KnowledgeTagSchema,
    KnowledgeTagCreate, KnowledgeTagUpdate,
    KnowledgeCategory as KnowledgeCategorySchema,
    KnowledgeCategoryCreate, KnowledgeCategoryUpdate,
    KnowledgeArticle as KnowledgeArticleSchema,
    KnowledgeArticleCreate, KnowledgeArticleUpdate, KnowledgeArticleDetail,
    KnowledgeComment as KnowledgeCommentSchema,
    KnowledgeCommentCreate, KnowledgeCommentUpdate
)

router = APIRouter()

# 知识库标签相关接口
@router.get("/tags", response_model=List[KnowledgeTagSchema])
def read_knowledge_tags(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    获取知识库标签列表
    """
    tags = db.query(KnowledgeTag).filter(
        KnowledgeTag.is_deleted == False
    ).offset(skip).limit(limit).all()
    return tags


@router.post("/tags", response_model=KnowledgeTagSchema, status_code=status.HTTP_201_CREATED)
def create_knowledge_tag(
    *,
    db: Session = Depends(get_db),
    tag_in: KnowledgeTagCreate,
) -> Any:
    """
    创建知识库标签
    """
    # 检查标签名是否已存在
    existing_tag = db.query(KnowledgeTag).filter(
        KnowledgeTag.name == tag_in.name,
        KnowledgeTag.is_deleted == False
    ).first()
    
    if existing_tag:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Tag with name '{tag_in.name}' already exists"
        )
    
    tag = KnowledgeTag(
        name=tag_in.name,
        color=tag_in.color,
        created_at=datetime.utcnow(),
    )
    
    db.add(tag)
    db.commit()
    db.refresh(tag)
    
    return tag


@router.put("/tags/{tag_id}", response_model=KnowledgeTagSchema)
def update_knowledge_tag(
    *,
    db: Session = Depends(get_db),
    tag_id: int = Path(..., gt=0, description="tag_id"),
    tag_in: KnowledgeTagUpdate,
) -> Any:
    """
    更新知识库标签
    """
    tag = db.query(KnowledgeTag).filter(
        KnowledgeTag.id == tag_id,
        KnowledgeTag.is_deleted == False
    ).first()
    
    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Tag with ID {tag_id} not found"
        )
    
    # 检查标签名是否已存在
    if tag_in.name and tag_in.name != tag.name:
        existing_tag = db.query(KnowledgeTag).filter(
            KnowledgeTag.name == tag_in.name,
            KnowledgeTag.id != tag_id,
            KnowledgeTag.is_deleted == False
        ).first()
        
        if existing_tag:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Tag with name '{tag_in.name}' already exists"
            )
    
    update_data = tag_in.model_dump(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(tag, field, value)
    
    db.add(tag)
    db.commit()
    db.refresh(tag)
    
    return tag


@router.delete("/tags/{tag_id}", response_model=KnowledgeTagSchema)
def delete_knowledge_tag(
    *,
    db: Session = Depends(get_db),
    tag_id: int = Path(..., gt=0, description="tag_id"),
) -> Any:
    """
    删除知识库标签
    """
    tag = db.query(KnowledgeTag).filter(
        KnowledgeTag.id == tag_id,
        KnowledgeTag.is_deleted == False
    ).first()
    
    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Tag with ID {tag_id} not found"
        )
    
    tag.is_deleted = True
    
    db.add(tag)
    db.commit()
    db.refresh(tag)
    
    return tag


# 知识库分类相关接口
@router.get("/categories", response_model=List[KnowledgeCategorySchema])
def read_knowledge_categories(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    parent_id: Optional[int] = None,
) -> Any:
    """
    获取知识库分类列表
    """
    query = db.query(KnowledgeCategory).filter(KnowledgeCategory.is_deleted == False)
    
    if parent_id is not None:
        query = query.filter(KnowledgeCategory.parent_id == parent_id)
    
    categories = query.offset(skip).limit(limit).all()
    return categories


@router.post("/categories", response_model=KnowledgeCategorySchema, status_code=status.HTTP_201_CREATED)
def create_knowledge_category(
    *,
    db: Session = Depends(get_db),
    category_in: KnowledgeCategoryCreate,
) -> Any:
    """
    创建知识库分类
    """
    # 检查父分类是否存在
    if category_in.parent_id:
        parent_category = db.query(KnowledgeCategory).filter(
            KnowledgeCategory.id == category_in.parent_id,
            KnowledgeCategory.is_deleted == False
        ).first()
        
        if not parent_category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Parent category with ID {category_in.parent_id} not found"
            )
    
    category = KnowledgeCategory(
        name=category_in.name,
        description=category_in.description,
        parent_id=category_in.parent_id,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
    )
    
    db.add(category)
    db.commit()
    db.refresh(category)
    
    return category


@router.put("/categories/{category_id}", response_model=KnowledgeCategorySchema)
def update_knowledge_category(
    *,
    db: Session = Depends(get_db),
    category_id: int = Path(..., gt=0, description="category_id"),
    category_in: KnowledgeCategoryUpdate,
) -> Any:
    """
    更新知识库分类
    """
    category = db.query(KnowledgeCategory).filter(
        KnowledgeCategory.id == category_id,
        KnowledgeCategory.is_deleted == False
    ).first()
    
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Category with ID {category_id} not found"
        )
    
    # 检查父分类是否存在
    if category_in.parent_id:
        # 不能将自己设为父分类
        if category_in.parent_id == category_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot set category as its own parent"
            )
        
        parent_category = db.query(KnowledgeCategory).filter(
            KnowledgeCategory.id == category_in.parent_id,
            KnowledgeCategory.is_deleted == False
        ).first()
        
        if not parent_category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Parent category with ID {category_in.parent_id} not found"
            )
    
    update_data = category_in.model_dump(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(category, field, value)
    
    category.updated_at = datetime.utcnow()
    
    db.add(category)
    db.commit()
    db.refresh(category)
    
    return category


@router.delete("/categories/{category_id}", response_model=KnowledgeCategorySchema)
def delete_knowledge_category(
    *,
    db: Session = Depends(get_db),
    category_id: int = Path(..., gt=0, description="category_id"),
) -> Any:
    """
    删除知识库分类
    """
    category = db.query(KnowledgeCategory).filter(
        KnowledgeCategory.id == category_id,
        KnowledgeCategory.is_deleted == False
    ).first()
    
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Category with ID {category_id} not found"
        )
    
    # 检查是否有子分类
    child_categories = db.query(KnowledgeCategory).filter(
        KnowledgeCategory.parent_id == category_id,
        KnowledgeCategory.is_deleted == False
    ).count()
    
    if child_categories > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot delete category with {child_categories} child categories"
        )
    
    # 检查是否有关联的文章
    articles = db.query(KnowledgeArticle).filter(
        KnowledgeArticle.category_id == category_id,
        KnowledgeArticle.is_deleted == False
    ).count()
    
    if articles > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot delete category with {articles} associated articles"
        )
    
    category.is_deleted = True
    category.updated_at = datetime.utcnow()
    
    db.add(category)
    db.commit()
    db.refresh(category)
    
    return category


# 知识库文章相关接口
@router.get("/articles", response_model=List[KnowledgeArticleSchema])
def read_knowledge_articles(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    category_id: Optional[int] = None,
    tag_id: Optional[int] = None,
    author_id: Optional[int] = None,
    is_published: Optional[bool] = True,
) -> Any:
    """
    获取知识库文章列表
    """
    query = db.query(KnowledgeArticle).filter(KnowledgeArticle.is_deleted == False)
    
    if category_id:
        query = query.filter(KnowledgeArticle.category_id == category_id)
    
    if author_id:
        query = query.filter(KnowledgeArticle.author_id == author_id)
    
    if is_published is not None:
        query = query.filter(KnowledgeArticle.is_published == is_published)
    
    if tag_id:
        query = query.filter(KnowledgeArticle.tags.any(KnowledgeTag.id == tag_id))
    
    articles = query.order_by(KnowledgeArticle.created_at.desc()).offset(skip).limit(limit).all()
    
    # 添加作者名称和分类名称
    result = []
    for article in articles:
        article_dict = article.__dict__.copy()
        
        # 添加作者名称
        if article.author_id:
            author = db.query(Employee).filter(Employee.id == article.author_id).first()
            if author:
                article_dict["author_name"] = author.name
        
        # 添加分类名称
        if article.category_id:
            category = db.query(KnowledgeCategory).filter(KnowledgeCategory.id == article.category_id).first()
            if category:
                article_dict["category_name"] = category.name
        
        result.append(article_dict)
    
    return result


@router.post("/articles", response_model=KnowledgeArticleSchema, status_code=status.HTTP_201_CREATED)
def create_knowledge_article(
    *,
    db: Session = Depends(get_db),
    article_in: KnowledgeArticleCreate,
) -> Any:
    """
    创建知识库文章
    """
    # 检查分类是否存在
    if article_in.category_id:
        category = db.query(KnowledgeCategory).filter(
            KnowledgeCategory.id == article_in.category_id,
            KnowledgeCategory.is_deleted == False
        ).first()
        
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Category with ID {article_in.category_id} not found"
            )
    
    # 检查作者是否存在
    if article_in.author_id:
        author = db.query(Employee).filter(
            Employee.id == article_in.author_id,
            Employee.is_deleted == False
        ).first()
        
        if not author:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Employee with ID {article_in.author_id} not found"
            )
    
    article = KnowledgeArticle(
        title=article_in.title,
        content=article_in.content,
        summary=article_in.summary,
        category_id=article_in.category_id,
        author_id=article_in.author_id,
        cover_image=article_in.cover_image,
        is_published=article_in.is_published,
        attachments=article_in.attachments,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
    )
    
    db.add(article)
    db.commit()
    db.refresh(article)
    
    # 添加标签
    if article_in.tag_ids:
        tags = db.query(KnowledgeTag).filter(
            KnowledgeTag.id.in_(article_in.tag_ids),
            KnowledgeTag.is_deleted == False
        ).all()
        
        article.tags = tags
        db.commit()
    
    return article


@router.get("/articles/{article_id}", response_model=KnowledgeArticleDetail)
def read_knowledge_article(
    *,
    db: Session = Depends(get_db),
    article_id: int = Path(..., gt=0, description="article_id"),
    increment_view: bool = False,
) -> Any:
    """
    获取知识库文章详情
    """
    article = db.query(KnowledgeArticle).filter(
        KnowledgeArticle.id == article_id,
        KnowledgeArticle.is_deleted == False
    ).first()
    
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Article with ID {article_id} not found"
        )
    
    # 增加浏览次数
    if increment_view:
        article.view_count += 1
        db.commit()
    
    # 构建返回结果
    result = article.__dict__.copy()
    
    # 添加作者名称
    if article.author_id:
        author = db.query(Employee).filter(Employee.id == article.author_id).first()
        if author:
            result["author_name"] = author.name
    
    # 添加分类名称
    if article.category_id:
        category = db.query(KnowledgeCategory).filter(KnowledgeCategory.id == article.category_id).first()
        if category:
            result["category_name"] = category.name
    
    # 添加标签
    result["tags"] = article.tags
    
    # 添加评论
    comments = db.query(KnowledgeComment).filter(
        KnowledgeComment.article_id == article_id,
        KnowledgeComment.parent_id.is_(None),  # 只获取顶级评论
        KnowledgeComment.is_deleted == False
    ).all()
    
    # 处理评论
    result["comments"] = process_comments(db, comments)
    
    return result


def process_comments(db: Session, comments):
    """处理评论，添加员工名称和回复"""
    result = []
    for comment in comments:
        comment_dict = comment.__dict__.copy()
        
        # 添加员工名称
        employee = db.query(Employee).filter(Employee.id == comment.employee_id).first()
        if employee:
            comment_dict["employee_name"] = employee.name
        
        # 添加回复
        replies = db.query(KnowledgeComment).filter(
            KnowledgeComment.parent_id == comment.id,
            KnowledgeComment.is_deleted == False
        ).all()
        
        if replies:
            comment_dict["replies"] = process_comments(db, replies)
        else:
            comment_dict["replies"] = []
        
        result.append(comment_dict)
    
    return result


@router.put("/articles/{article_id}", response_model=KnowledgeArticleSchema)
def update_knowledge_article(
    *,
    db: Session = Depends(get_db),
    article_id: int = Path(..., gt=0, description="article_id"),
    article_in: KnowledgeArticleUpdate,
) -> Any:
    """
    更新知识库文章
    """
    article = db.query(KnowledgeArticle).filter(
        KnowledgeArticle.id == article_id,
        KnowledgeArticle.is_deleted == False
    ).first()
    
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Article with ID {article_id} not found"
        )
    
    # 检查分类是否存在
    if article_in.category_id:
        category = db.query(KnowledgeCategory).filter(
            KnowledgeCategory.id == article_in.category_id,
            KnowledgeCategory.is_deleted == False
        ).first()
        
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Category with ID {article_in.category_id} not found"
            )
    
    update_data = article_in.model_dump(exclude={"tag_ids"}, exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(article, field, value)
    
    article.updated_at = datetime.utcnow()
    
    # 更新标签
    if article_in.tag_ids is not None:
        tags = db.query(KnowledgeTag).filter(
            KnowledgeTag.id.in_(article_in.tag_ids),
            KnowledgeTag.is_deleted == False
        ).all()
        
        article.tags = tags
    
    db.add(article)
    db.commit()
    db.refresh(article)
    
    return article


@router.delete("/articles/{article_id}", response_model=KnowledgeArticleSchema)
def delete_knowledge_article(
    *,
    db: Session = Depends(get_db),
    article_id: int = Path(..., gt=0, description="article_id"),
) -> Any:
    """
    删除知识库文章
    """
    article = db.query(KnowledgeArticle).filter(
        KnowledgeArticle.id == article_id,
        KnowledgeArticle.is_deleted == False
    ).first()
    
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Article with ID {article_id} not found"
        )
    
    article.is_deleted = True
    article.updated_at = datetime.utcnow()
    
    db.add(article)
    db.commit()
    db.refresh(article)
    
    return article


@router.post("/articles/{article_id}/like", response_model=KnowledgeArticleSchema)
def like_knowledge_article(
    *,
    db: Session = Depends(get_db),
    article_id: int = Path(..., gt=0, description="article_id"),
) -> Any:
    """
    点赞知识库文章
    """
    article = db.query(KnowledgeArticle).filter(
        KnowledgeArticle.id == article_id,
        KnowledgeArticle.is_deleted == False
    ).first()
    
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Article with ID {article_id} not found"
        )
    
    article.like_count += 1
    article.updated_at = datetime.utcnow()
    
    db.add(article)
    db.commit()
    db.refresh(article)
    
    return article


# 知识库评论相关接口
@router.post("/comments", response_model=KnowledgeCommentSchema, status_code=status.HTTP_201_CREATED)
def create_knowledge_comment(
    *,
    db: Session = Depends(get_db),
    comment_in: KnowledgeCommentCreate,
) -> Any:
    """
    创建知识库评论
    """
    # 检查文章是否存在
    article = db.query(KnowledgeArticle).filter(
        KnowledgeArticle.id == comment_in.article_id,
        KnowledgeArticle.is_deleted == False
    ).first()
    
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Article with ID {comment_in.article_id} not found"
        )
    
    # 检查员工是否存在
    employee = db.query(Employee).filter(
        Employee.id == comment_in.employee_id,
        Employee.is_deleted == False
    ).first()
    
    if not employee:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Employee with ID {comment_in.employee_id} not found"
        )
    
    # 检查父评论是否存在
    if comment_in.parent_id:
        parent_comment = db.query(KnowledgeComment).filter(
            KnowledgeComment.id == comment_in.parent_id,
            KnowledgeComment.is_deleted == False
        ).first()
        
        if not parent_comment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Parent comment with ID {comment_in.parent_id} not found"
            )
        
        # 确保父评论属于同一篇文章
        if parent_comment.article_id != comment_in.article_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Parent comment does not belong to the specified article"
            )
    
    comment = KnowledgeComment(
        article_id=comment_in.article_id,
        employee_id=comment_in.employee_id,
        content=comment_in.content,
        parent_id=comment_in.parent_id,
        created_at=datetime.utcnow(),
    )
    
    db.add(comment)
    db.commit()
    db.refresh(comment)
    
    # 添加员工名称
    result = comment.__dict__.copy()
    result["employee_name"] = employee.name
    result["replies"] = []
    
    return result


@router.put("/comments/{comment_id}", response_model=KnowledgeCommentSchema)
def update_knowledge_comment(
    *,
    db: Session = Depends(get_db),
    comment_id: int = Path(..., gt=0, description="comment_id"),
    comment_in: KnowledgeCommentUpdate,
) -> Any:
    """
    更新知识库评论
    """
    comment = db.query(KnowledgeComment).filter(
        KnowledgeComment.id == comment_id,
        KnowledgeComment.is_deleted == False
    ).first()
    
    if not comment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Comment with ID {comment_id} not found"
        )
    
    update_data = comment_in.model_dump(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(comment, field, value)
    
    db.add(comment)
    db.commit()
    db.refresh(comment)
    
    # 添加员工名称
    employee = db.query(Employee).filter(Employee.id == comment.employee_id).first()
    result = comment.__dict__.copy()
    if employee:
        result["employee_name"] = employee.name
    
    # 添加回复
    replies = db.query(KnowledgeComment).filter(
        KnowledgeComment.parent_id == comment_id,
        KnowledgeComment.is_deleted == False
    ).all()
    
    result["replies"] = process_comments(db, replies)
    
    return result


@router.delete("/comments/{comment_id}", response_model=KnowledgeCommentSchema)
def delete_knowledge_comment(
    *,
    db: Session = Depends(get_db),
    comment_id: int = Path(..., gt=0, description="comment_id"),
) -> Any:
    """
    删除知识库评论
    """
    comment = db.query(KnowledgeComment).filter(
        KnowledgeComment.id == comment_id,
        KnowledgeComment.is_deleted == False
    ).first()
    
    if not comment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Comment with ID {comment_id} not found"
        )
    
    comment.is_deleted = True
    
    db.add(comment)
    db.commit()
    db.refresh(comment)
    
    return comment 