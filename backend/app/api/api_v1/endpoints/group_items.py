from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session
from datetime import datetime

from app.db.session import get_db
from app.models.group_item import GroupItem
from app.schemas.group_item import GroupItem as GroupItemSchema
from app.schemas.group_item import GroupItemCreate, GroupItemUpdate

router = APIRouter()


@router.get("/", response_model=List[GroupItemSchema])
def read_group_items(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    type: Optional[str] = Query(None, description="项目类型筛选"),
    category: Optional[str] = Query(None, description="项目分类筛选")
) -> Any:
    """
    获取集团项目列表
    """
    # 构建查询过滤条件
    query = db.query(GroupItem).filter(GroupItem.is_deleted == False)

    if type:
        query = query.filter(GroupItem.type == type)

    if category:
        query = query.filter(GroupItem.category == category)

    return query.offset(skip).limit(limit).all()


@router.post("/", response_model=GroupItemSchema, status_code=status.HTTP_201_CREATED)
def create_group_item(
    *,
    db: Session = Depends(get_db),
    item_in: GroupItemCreate,
) -> Any:
    """
    创建集团项目
    """
    item = GroupItem(
        name=item_in.name,
        type=item_in.type,
        category=item_in.category,
        subcategory=item_in.subcategory,
        group_standard_price=item_in.group_standard_price,
        duration=item_in.duration,
        image_url=item_in.image_url,
        description=item_in.description,
        specifications=item_in.specifications,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
    )
    
    db.add(item)
    db.commit()
    db.refresh(item)
    
    return item


@router.get("/{item_id}", response_model=GroupItemSchema)
def read_group_item(
    *,
    db: Session = Depends(get_db),
    item_id: int = Path(..., gt=0, description="项目ID"),
) -> Any:
    """
    获取集团项目详情
    """
    item = db.query(GroupItem).filter(
        GroupItem.id == item_id,
        GroupItem.is_deleted == False
    ).first()
    
    if not item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Group item with ID {item_id} not found"
        )
    
    return item


@router.put("/{item_id}", response_model=GroupItemSchema)
def update_group_item(
    *,
    db: Session = Depends(get_db),
    item_id: int = Path(..., gt=0, description="项目ID"),
    item_in: GroupItemUpdate,
) -> Any:
    """
    更新集团项目
    """
    item = db.query(GroupItem).filter(
        GroupItem.id == item_id,
        GroupItem.is_deleted == False
    ).first()
    
    if not item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Group item with ID {item_id} not found"
        )
    
    update_data = item_in.model_dump(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(item, field, value)
    
    item.updated_at = datetime.utcnow()
    
    db.add(item)
    db.commit()
    db.refresh(item)
    
    return item


@router.delete("/{item_id}", response_model=GroupItemSchema)
def delete_group_item(
    *,
    db: Session = Depends(get_db),
    item_id: int = Path(..., gt=0, description="项目ID"),
) -> Any:
    """
    删除集团项目 (软删除)
    """
    item = db.query(GroupItem).filter(
        GroupItem.id == item_id,
        GroupItem.is_deleted == False
    ).first()
    
    if not item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Group item with ID {item_id} not found"
        )
    
    item.is_deleted = True
    item.updated_at = datetime.utcnow()
    
    db.add(item)
    db.commit()
    db.refresh(item)
    
    return item 