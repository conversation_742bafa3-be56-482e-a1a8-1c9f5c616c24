from fastapi import APIRouter

from app.api.api_v1.endpoints import (
    stores, employees, commission_schemes, group_items, store_items,
    operations, performances, shareholders, shareholder_structures,
    dividend_schemes, dividend_records,
    knowledge, customer_tags, customer_segments, customer_profile,
    customer_behaviors, appointments, reviews, store_costs, dividend_reports,
    business_intelligence, ai_analysis, auth, uploads,
    marketing_activities, shareholder_investment_records, shareholder_dashboard_simple,
    marketing_automation, customer_retention, birthday_care, consumption_incentive,
    technician_management, financial_integration,
    coupons, member_cards, member_levels, points, referrals, referral_rewards,
    security, voucher_generation, auto_reconciliation, data_mapping, scheduling_rules,
    skill_assessment, custom_reports, audit_logs, api_management, points_mall,
    real_time_sync, mobile_api, customer_referral, admin, business_roles,
    customers, dashboard, product_categories, employees_enhanced, projects_enhanced,
    payment_methods, group_buying_platforms, store_member_pricing, store_item_pricing,
    websocket
)

api_router = APIRouter()
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(stores.router, prefix="/stores", tags=["stores"])
api_router.include_router(employees.router, prefix="/employees", tags=["employees"])
api_router.include_router(customers.router, prefix="/customers", tags=["customers"])
api_router.include_router(commission_schemes.router, prefix="/commission-schemes", tags=["commission_schemes"])
api_router.include_router(technician_management.router, prefix="/technicians", tags=["technician_management"])
api_router.include_router(group_items.router, prefix="/group-items", tags=["group_items"])
api_router.include_router(store_items.router, prefix="/store-items", tags=["store_items"])
api_router.include_router(operations.router, prefix="/operations", tags=["operations"])
api_router.include_router(performances.router, prefix="/performances", tags=["performances"])
api_router.include_router(shareholders.router, prefix="/shareholders", tags=["shareholders"])
api_router.include_router(shareholder_structures.router, prefix="/shareholder-structures", tags=["shareholder_structures"])
api_router.include_router(shareholder_investment_records.router, prefix="/shareholder-investment-records", tags=["shareholder_investment_records"])
api_router.include_router(shareholder_dashboard_simple.router, prefix="/shareholder-dashboard", tags=["shareholder_dashboard"])
api_router.include_router(dividend_schemes.router, prefix="/dividend-schemes", tags=["dividend_schemes"])
api_router.include_router(dividend_records.router, prefix="/dividend-records", tags=["dividend_records"])
api_router.include_router(knowledge.router, prefix="/knowledge", tags=["knowledge"])
api_router.include_router(customer_tags.router, prefix="/customer-tags", tags=["customer_tags"])
api_router.include_router(customer_segments.router, prefix="/customer-segments", tags=["customer_segments"])
api_router.include_router(customer_profile.router, prefix="/customer-profile", tags=["customer_profile"])
api_router.include_router(customer_behaviors.router, prefix="/customer-behaviors", tags=["customer_behaviors"])
api_router.include_router(appointments.router, prefix="/appointment", tags=["appointments"])
api_router.include_router(reviews.router, prefix="/reviews", tags=["reviews"])
api_router.include_router(store_costs.router, prefix="/store-costs", tags=["store_costs"])
api_router.include_router(dividend_reports.router, prefix="/dividend-reports", tags=["dividend_reports"])
api_router.include_router(financial_integration.router, prefix="/financial", tags=["financial_integration"])
api_router.include_router(business_intelligence.router, prefix="/bi", tags=["business_intelligence"])
api_router.include_router(ai_analysis.router, prefix="/ai-analysis", tags=["ai_analysis"])
api_router.include_router(uploads.router, prefix="/uploads", tags=["uploads"])

# 营销相关路由
api_router.include_router(marketing_activities.router, prefix="/marketing/activities", tags=["marketing_activities"])
api_router.include_router(marketing_automation.router, prefix="/marketing/automation", tags=["marketing_automation"])
api_router.include_router(customer_retention.router, prefix="/marketing/retention", tags=["customer_retention"])
api_router.include_router(birthday_care.router, prefix="/marketing/birthday", tags=["birthday_care"])
api_router.include_router(consumption_incentive.router, prefix="/marketing/incentive", tags=["consumption_incentive"])
api_router.include_router(coupons.router, prefix="/marketing/coupons", tags=["coupons"])

# 会员相关路由
api_router.include_router(member_cards.router, prefix="/member-cards", tags=["member_cards"])
api_router.include_router(member_levels.router, prefix="/member-levels", tags=["member_levels"])
api_router.include_router(points.router, prefix="/points", tags=["points"])
api_router.include_router(referrals.router, prefix="/referrals", tags=["referrals"])
api_router.include_router(referral_rewards.router, prefix="/referral-rewards", tags=["referral_rewards"])

# 安全相关路由
api_router.include_router(security.router, prefix="/security", tags=["security"])

# 凭证生成路由
api_router.include_router(voucher_generation.router, prefix="/voucher-generation", tags=["voucher_generation"])

# 自动对账路由
api_router.include_router(auto_reconciliation.router, prefix="/auto-reconciliation", tags=["auto_reconciliation"])

# 数据映射路由
api_router.include_router(data_mapping.router, prefix="/data-mapping", tags=["data_mapping"])

# 排班规则路由
api_router.include_router(scheduling_rules.router, prefix="/scheduling-rules", tags=["scheduling_rules"])

# 技能评估路由
api_router.include_router(skill_assessment.router, prefix="/skill-assessment", tags=["skill_assessment"])

# 自定义报表路由
api_router.include_router(custom_reports.router, prefix="/custom-reports", tags=["custom_reports"])

# 审计日志路由
api_router.include_router(audit_logs.router, prefix="/audit-logs", tags=["audit_logs"])

# API管理路由
api_router.include_router(api_management.router, prefix="/api-management", tags=["api_management"])

# 积分商城路由
api_router.include_router(points_mall.router, prefix="/points-mall", tags=["points_mall"])

# 实时数据同步路由
api_router.include_router(real_time_sync.router, prefix="/real-time-sync", tags=["real_time_sync"])

# 移动端API路由
api_router.include_router(mobile_api.router, prefix="/mobile", tags=["mobile_api"])

# 客户转介绍路由
api_router.include_router(customer_referral.router, prefix="/customer-referral", tags=["customer_referral"])

# 超级管理员路由
api_router.include_router(admin.router, prefix="/admin", tags=["admin"])

# 业务角色管理路由
api_router.include_router(business_roles.router, prefix="/admin/business-roles", tags=["business_roles"])

# 工作台路由
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["dashboard"])

# 新增功能路由
api_router.include_router(product_categories.router, prefix="/product-categories", tags=["product_categories"])
api_router.include_router(employees_enhanced.router, prefix="/employees-enhanced", tags=["employees_enhanced"])
api_router.include_router(projects_enhanced.router, prefix="/projects-enhanced", tags=["projects_enhanced"])
api_router.include_router(payment_methods.router, prefix="/payment-methods", tags=["payment_methods"])
api_router.include_router(group_buying_platforms.router, prefix="/group-buying-platforms", tags=["group_buying_platforms"])
api_router.include_router(store_member_pricing.router, prefix="/store-member-pricing", tags=["store_member_pricing"])
api_router.include_router(store_item_pricing.router, prefix="/store-item-pricing", tags=["store_item_pricing"])

# WebSocket路由
api_router.include_router(websocket.router, prefix="", tags=["websocket"])
