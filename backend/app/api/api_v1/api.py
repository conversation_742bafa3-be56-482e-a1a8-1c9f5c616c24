from fastapi import APIRouter

from app.api.api_v1.endpoints import (
    stores, employees, commission_schemes, group_items, store_items,
    operations, performances, shareholders, shareholder_structures,
    dividend_schemes, dividend_records,
    knowledge, customer_tags, customer_segments, customer_profile,
    customer_behaviors, appointments, reviews, store_costs, dividend_reports,
    business_intelligence, ai_analysis, auth, uploads,
    marketing_activities, shareholder_investment_records, shareholder_dashboard_simple,
    marketing_automation, customer_retention, birthday_care, consumption_incentive,
    technician_management, financial_integration,
    coupons, member_cards, member_levels, points, referrals, referral_rewards,
    security, voucher_generation, auto_reconciliation, data_mapping, scheduling_rules,
    skill_assessment, custom_reports, audit_logs, api_management, points_mall,
    real_time_sync, mobile_api, customer_referral, admin, business_roles,
    customers, dashboard, product_categories, employees_enhanced, projects_enhanced,
    payment_methods, group_buying_platforms, store_member_pricing, store_item_pricing,
    websocket
)

api_router = APIRouter()
api_router.include_router(auth.router, prefix="/auth", tags=["🔐 认证管理"])
api_router.include_router(stores.router, prefix="/stores", tags=["🏪 门店管理"])
api_router.include_router(employees.router, prefix="/employees", tags=["👥 员工管理"])
api_router.include_router(customers.router, prefix="/customers", tags=["👤 客户管理"])
api_router.include_router(commission_schemes.router, prefix="/commission-schemes", tags=["💰 提成方案"])
api_router.include_router(technician_management.router, prefix="/technicians", tags=["🧑‍⚕️ 技师管理"])
api_router.include_router(group_items.router, prefix="/group-items", tags=["📦 集团项目"])
api_router.include_router(store_items.router, prefix="/store-items", tags=["🛍️ 门店项目"])
api_router.include_router(operations.router, prefix="/operations", tags=["📊 营业数据"])
api_router.include_router(performances.router, prefix="/performances", tags=["📈 绩效统计"])
api_router.include_router(shareholders.router, prefix="/shareholders", tags=["🏛️ 股东管理"])
api_router.include_router(shareholder_structures.router, prefix="/shareholder-structures", tags=["🏗️ 股权结构"])
api_router.include_router(shareholder_investment_records.router, prefix="/shareholder-investment-records", tags=["💼 投资记录"])
api_router.include_router(shareholder_dashboard_simple.router, prefix="/shareholder-dashboard", tags=["📋 股东仪表盘"])
api_router.include_router(dividend_schemes.router, prefix="/dividend-schemes", tags=["💵 分红方案"])
api_router.include_router(dividend_records.router, prefix="/dividend-records", tags=["📝 分红记录"])
api_router.include_router(knowledge.router, prefix="/knowledge", tags=["📚 知识库"])
api_router.include_router(customer_tags.router, prefix="/customer-tags", tags=["🏷️ 客户标签"])
api_router.include_router(customer_segments.router, prefix="/customer-segments", tags=["👥 客户分群"])
api_router.include_router(customer_profile.router, prefix="/customer-profile", tags=["👤 客户画像"])
api_router.include_router(customer_behaviors.router, prefix="/customer-behaviors", tags=["📊 客户行为"])
api_router.include_router(appointments.router, prefix="/appointment", tags=["📅 预约管理"])
api_router.include_router(reviews.router, prefix="/reviews", tags=["⭐ 评价管理"])
api_router.include_router(store_costs.router, prefix="/store-costs", tags=["💸 门店成本"])
api_router.include_router(dividend_reports.router, prefix="/dividend-reports", tags=["📊 分红报表"])
api_router.include_router(financial_integration.router, prefix="/financial", tags=["💳 财务集成"])
api_router.include_router(business_intelligence.router, prefix="/bi", tags=["📈 商业智能"])
api_router.include_router(ai_analysis.router, prefix="/ai-analysis", tags=["🤖 AI分析"])
api_router.include_router(uploads.router, prefix="/uploads", tags=["📁 文件上传"])

# 营销相关路由
api_router.include_router(marketing_activities.router, prefix="/marketing/activities", tags=["🎯 营销活动"])
api_router.include_router(marketing_automation.router, prefix="/marketing/automation", tags=["🤖 营销自动化"])
api_router.include_router(customer_retention.router, prefix="/marketing/retention", tags=["🔄 客户留存"])
api_router.include_router(birthday_care.router, prefix="/marketing/birthday", tags=["🎂 生日关怀"])
api_router.include_router(consumption_incentive.router, prefix="/marketing/incentive", tags=["🎁 消费激励"])
api_router.include_router(coupons.router, prefix="/marketing/coupons", tags=["🎫 优惠券"])

# 会员相关路由
api_router.include_router(member_cards.router, prefix="/member-cards", tags=["💳 会员卡"])
api_router.include_router(member_levels.router, prefix="/member-levels", tags=["⭐ 会员等级"])
api_router.include_router(points.router, prefix="/points", tags=["🎯 积分管理"])
api_router.include_router(referrals.router, prefix="/referrals", tags=["👥 推荐关系"])
api_router.include_router(referral_rewards.router, prefix="/referral-rewards", tags=["🎁 推荐奖励"])

# 安全相关路由
api_router.include_router(security.router, prefix="/security", tags=["🔒 安全管理"])

# 凭证生成路由
api_router.include_router(voucher_generation.router, prefix="/voucher-generation", tags=["🎫 券码生成"])

# 自动对账路由
api_router.include_router(auto_reconciliation.router, prefix="/auto-reconciliation", tags=["🔄 自动对账"])

# 数据映射路由
api_router.include_router(data_mapping.router, prefix="/data-mapping", tags=["🗺️ 数据映射"])

# 排班规则路由
api_router.include_router(scheduling_rules.router, prefix="/scheduling-rules", tags=["📅 排班规则"])

# 技能评估路由
api_router.include_router(skill_assessment.router, prefix="/skill-assessment", tags=["🎯 技能评估"])

# 自定义报表路由
api_router.include_router(custom_reports.router, prefix="/custom-reports", tags=["📊 自定义报表"])

# 审计日志路由
api_router.include_router(audit_logs.router, prefix="/audit-logs", tags=["📝 审计日志"])

# API管理路由
api_router.include_router(api_management.router, prefix="/api-management", tags=["⚙️ API管理"])

# 积分商城路由
api_router.include_router(points_mall.router, prefix="/points-mall", tags=["🛒 积分商城"])

# 实时数据同步路由
api_router.include_router(real_time_sync.router, prefix="/real-time-sync", tags=["🔄 实时同步"])

# 移动端API路由
api_router.include_router(mobile_api.router, prefix="/mobile", tags=["📱 移动端API"])

# 客户转介绍路由
api_router.include_router(customer_referral.router, prefix="/customer-referral", tags=["🤝 客户转介绍"])

# 超级管理员路由
api_router.include_router(admin.router, prefix="/admin", tags=["👑 超级管理员"])

# 业务角色管理路由
api_router.include_router(business_roles.router, prefix="/admin/business-roles", tags=["👔 业务角色"])

# 工作台路由
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["🏠 工作台"])

# 新增功能路由
api_router.include_router(product_categories.router, prefix="/product-categories", tags=["📂 产品分类"])
api_router.include_router(employees_enhanced.router, prefix="/employees-enhanced", tags=["👥 增强员工管理"])
api_router.include_router(projects_enhanced.router, prefix="/projects-enhanced", tags=["📦 增强项目管理"])
api_router.include_router(payment_methods.router, prefix="/payment-methods", tags=["💳 支付方式"])
api_router.include_router(group_buying_platforms.router, prefix="/group-buying-platforms", tags=["🛒 团购平台"])
api_router.include_router(store_member_pricing.router, prefix="/store-member-pricing", tags=["💰 门店会员定价"])
api_router.include_router(store_item_pricing.router, prefix="/store-item-pricing", tags=["💸 门店项目定价"])

# WebSocket路由
api_router.include_router(websocket.router, prefix="", tags=["🔌 WebSocket"])
