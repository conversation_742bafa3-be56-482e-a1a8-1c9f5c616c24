from typing import Generator, Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import jwt, JWTError
from pydantic import ValidationError
from sqlalchemy.orm import Session

from app.db.session import SessionLocal
from app.core.security import verify_password
from app.core.config import settings
from app.models.employee import Employee
from app.models.shareholder import Shareholder
from app.models.admin import AdminUser
from app.schemas.token import TokenPayload

oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/login/access-token")
admin_oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/admin/auth/login")


def get_db() -> Generator:
    """
    获取数据库会话
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_current_employee(
    db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)
) -> Employee:
    """
    获取当前登录的员工
    """
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_data = TokenPayload(**payload)
    except (JWTError, ValidationError) as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Could not validate credentials",
        )
    employee = db.query(Employee).filter(Employee.id == token_data.sub).first()
    if not employee:
        raise HTTPException(status_code=404, detail="Employee not found")
    if employee.is_deleted:
        raise HTTPException(status_code=400, detail="Inactive employee")
    return employee


# 添加别名，兼容现有代码
get_current_user = get_current_employee


def get_current_active_employee(
    current_employee: Employee = Depends(get_current_employee),
) -> Employee:
    """
    获取当前活跃的员工
    """
    if current_employee.status != "active":
        raise HTTPException(status_code=400, detail="Inactive employee")
    return current_employee


def get_current_admin(
    current_employee: Employee = Depends(get_current_employee),
) -> Employee:
    """
    获取当前管理员
    """
    if current_employee.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Not enough permissions"
        )
    return current_employee


def get_current_shareholder(
    db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)
) -> Shareholder:
    """
    获取当前登录的股东
    """
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_data = TokenPayload(**payload)

        # 检查用户类型
        user_type = payload.get("user_type", "employee")
        if user_type != "shareholder":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Invalid user type for shareholder access"
            )

    except (JWTError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Could not validate credentials",
        )

    shareholder = db.query(Shareholder).filter(
        Shareholder.id == token_data.sub,
        Shareholder.is_deleted == False
    ).first()

    if not shareholder:
        raise HTTPException(status_code=404, detail="Shareholder not found")

    return shareholder


def get_current_user_flexible(
    db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)
):
    """
    灵活的用户认证，支持员工和股东
    """
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_data = TokenPayload(**payload)
        user_type = payload.get("user_type", "employee")

    except (JWTError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Could not validate credentials",
        )

    if user_type == "shareholder":
        user = db.query(Shareholder).filter(
            Shareholder.id == token_data.sub,
            Shareholder.is_deleted == False
        ).first()
        if not user:
            raise HTTPException(status_code=404, detail="Shareholder not found")
    else:
        user = db.query(Employee).filter(
            Employee.id == token_data.sub,
            Employee.is_deleted == False
        ).first()
        if not user:
            raise HTTPException(status_code=404, detail="Employee not found")
        if user.status != "active":
            raise HTTPException(status_code=400, detail="Inactive employee")

    # 添加用户类型属性
    user.user_type = user_type
    return user


def require_shareholder_access(
    current_user = Depends(get_current_user_flexible)
):
    """
    要求股东访问权限
    """
    if not hasattr(current_user, 'user_type') or current_user.user_type != "shareholder":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Shareholder access required"
        )
    return current_user


def get_current_employee_with_permissions(permissions: list):
    """
    获取具有特定权限的当前员工
    """
    def _get_current_employee_with_permissions(
        current_employee: Employee = Depends(get_current_active_employee)
    ) -> Employee:
        # 简化权限检查，实际应该检查员工的具体权限
        if current_employee.role in ["admin", "manager"]:
            return current_employee

        # 这里应该检查具体的权限，现在简化处理
        return current_employee

    return _get_current_employee_with_permissions


def get_current_admin_user(
    db: Session = Depends(get_db), token: str = Depends(admin_oauth2_scheme)
) -> AdminUser:
    """
    获取当前登录的管理员用户
    """
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_data = TokenPayload(**payload)

        # 检查用户类型
        user_type = payload.get("user_type", "employee")
        if user_type != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Invalid user type for admin access"
            )

    except (JWTError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Could not validate credentials",
        )

    admin_user = db.query(AdminUser).filter(
        AdminUser.id == token_data.sub,
        AdminUser.is_deleted == False
    ).first()

    if not admin_user:
        raise HTTPException(status_code=404, detail="Admin user not found")

    if not admin_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive admin user")

    return admin_user


def get_current_active_admin_user(
    current_admin: AdminUser = Depends(get_current_admin_user),
) -> AdminUser:
    """
    获取当前活跃的管理员用户
    """
    return current_admin