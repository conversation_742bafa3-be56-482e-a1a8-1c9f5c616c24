"""
访问模式记录中间件
自动记录API访问模式以优化数据预加载策略
"""

import time
import json
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response as StarletteResponse
import logging

logger = logging.getLogger(__name__)


class AccessPatternMiddleware(BaseHTTPMiddleware):
    """访问模式记录中间件"""
    
    def __init__(self, app, preloader=None):
        super().__init__(app)
        self.preloader = preloader
        
        # 需要记录的API端点
        self.tracked_endpoints = [
            "/api/v1/bi/business-indicators",
            "/api/v1/bi/multidimensional-analysis", 
            "/api/v1/bi/trend-prediction"
        ]
    
    async def dispatch(self, request: Request, call_next: Callable) -> StarletteResponse:
        """处理请求并记录访问模式"""
        start_time = time.time()
        
        # 检查是否需要记录此端点
        should_track = any(
            endpoint in str(request.url.path) 
            for endpoint in self.tracked_endpoints
        )
        
        # 执行请求
        response = await call_next(request)
        
        # 记录访问模式（仅对成功的请求）
        if should_track and response.status_code == 200 and self.preloader:
            try:
                await self._record_access_pattern(request, response, start_time)
            except Exception as e:
                logger.error(f"记录访问模式失败: {str(e)}")
        
        return response
    
    async def _record_access_pattern(self, request: Request, response: Response, start_time: float):
        """记录访问模式"""
        try:
            # 提取请求信息
            endpoint = str(request.url.path)
            method = request.method
            
            # 提取请求参数
            params = {}
            
            # 查询参数
            if request.query_params:
                params.update(dict(request.query_params))
            
            # POST请求体参数（如果是JSON）
            if method == "POST":
                try:
                    # 注意：这里需要小心处理，因为request body可能已经被消费
                    # 在实际应用中，可能需要在请求处理前缓存body
                    content_type = request.headers.get("content-type", "")
                    if "application/json" in content_type:
                        # 这里简化处理，实际应用中需要更复杂的逻辑
                        pass
                except Exception:
                    pass
            
            # 计算响应时间
            response_time = time.time() - start_time
            
            # 记录访问模式
            pattern_data = {
                "endpoint": endpoint,
                "method": method,
                "params": params,
                "response_time": response_time,
                "status_code": response.status_code
            }
            
            # 调用预加载器记录访问模式
            if hasattr(self.preloader, 'record_access_pattern'):
                self.preloader.record_access_pattern(endpoint, params)
            
            # 记录性能指标
            if response_time > 2.0:  # 响应时间超过2秒
                logger.warning(f"慢查询检测: {endpoint} 耗时 {response_time:.2f}秒")
                
        except Exception as e:
            logger.error(f"记录访问模式详细信息失败: {str(e)}")


def create_access_pattern_middleware(preloader=None):
    """创建访问模式中间件的工厂函数"""
    def middleware_factory(app):
        return AccessPatternMiddleware(app, preloader)
    return middleware_factory


class RequestBodyCacheMiddleware(BaseHTTPMiddleware):
    """请求体缓存中间件，用于在访问模式记录中获取POST参数"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> StarletteResponse:
        """缓存请求体以供后续使用"""
        
        # 只处理JSON请求
        content_type = request.headers.get("content-type", "")
        if "application/json" in content_type and request.method == "POST":
            try:
                # 读取请求体
                body = await request.body()
                
                # 解析JSON
                if body:
                    try:
                        json_data = json.loads(body.decode())
                        # 将解析的数据存储在request.state中
                        request.state.json_body = json_data
                    except json.JSONDecodeError:
                        pass
                
                # 重新创建request以包含body
                async def receive():
                    return {"type": "http.request", "body": body}
                
                request._receive = receive
                
            except Exception as e:
                logger.error(f"缓存请求体失败: {str(e)}")
        
        response = await call_next(request)
        return response


class EnhancedAccessPatternMiddleware(BaseHTTPMiddleware):
    """增强的访问模式记录中间件，支持POST参数记录"""
    
    def __init__(self, app, preloader=None):
        super().__init__(app)
        self.preloader = preloader
        
        # 需要记录的API端点
        self.tracked_endpoints = [
            "/api/v1/bi/business-indicators",
            "/api/v1/bi/multidimensional-analysis", 
            "/api/v1/bi/trend-prediction"
        ]
    
    async def dispatch(self, request: Request, call_next: Callable) -> StarletteResponse:
        """处理请求并记录访问模式"""
        start_time = time.time()
        
        # 检查是否需要记录此端点
        should_track = any(
            endpoint in str(request.url.path) 
            for endpoint in self.tracked_endpoints
        )
        
        # 执行请求
        response = await call_next(request)
        
        # 记录访问模式（仅对成功的请求）
        if should_track and response.status_code == 200 and self.preloader:
            try:
                await self._record_enhanced_access_pattern(request, response, start_time)
            except Exception as e:
                logger.error(f"记录增强访问模式失败: {str(e)}")
        
        return response
    
    async def _record_enhanced_access_pattern(self, request: Request, response: Response, start_time: float):
        """记录增强的访问模式"""
        try:
            # 提取请求信息
            endpoint = str(request.url.path)
            method = request.method
            
            # 提取请求参数
            params = {}
            
            # 查询参数
            if request.query_params:
                params.update(dict(request.query_params))
            
            # POST请求体参数
            if method == "POST" and hasattr(request.state, 'json_body'):
                json_body = request.state.json_body
                if json_body:
                    # 只记录关键参数，避免敏感信息
                    safe_params = {}
                    safe_keys = [
                        'store_ids', 'start_date', 'end_date', 'dimensions', 
                        'metrics', 'prediction_type', 'period_unit', 'indicators'
                    ]
                    
                    for key in safe_keys:
                        if key in json_body:
                            safe_params[key] = json_body[key]
                    
                    params.update(safe_params)
            
            # 计算响应时间
            response_time = time.time() - start_time
            
            # 记录访问模式
            if hasattr(self.preloader, 'record_access_pattern'):
                self.preloader.record_access_pattern(endpoint, params)
            
            # 记录性能指标
            if response_time > 2.0:  # 响应时间超过2秒
                logger.warning(f"慢查询检测: {endpoint} 耗时 {response_time:.2f}秒，参数: {params}")
            elif response_time > 5.0:  # 响应时间超过5秒
                logger.error(f"极慢查询检测: {endpoint} 耗时 {response_time:.2f}秒，参数: {params}")
                
        except Exception as e:
            logger.error(f"记录增强访问模式详细信息失败: {str(e)}")


def setup_access_pattern_tracking(app, preloader=None):
    """设置访问模式跟踪中间件"""
    
    # 添加请求体缓存中间件
    app.add_middleware(RequestBodyCacheMiddleware)
    
    # 添加增强的访问模式记录中间件
    app.add_middleware(EnhancedAccessPatternMiddleware, preloader=preloader)
    
    logger.info("访问模式跟踪中间件已设置")
