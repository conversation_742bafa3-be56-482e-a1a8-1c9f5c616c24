"""
API限流中间件
实现基于令牌桶算法的API限流，防止API滥用
"""

import time
import asyncio
from typing import Dict, Optional, Callable, List
from collections import defaultdict, deque
from fastapi import Request, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response, JSONResponse
from starlette.types import ASGIApp
import redis
import json
import logging

logger = logging.getLogger(__name__)


class TokenBucket:
    """令牌桶算法实现"""
    
    def __init__(self, capacity: int, refill_rate: float):
        self.capacity = capacity  # 桶容量
        self.tokens = capacity    # 当前令牌数
        self.refill_rate = refill_rate  # 每秒补充令牌数
        self.last_refill = time.time()
        self.lock = asyncio.Lock()
    
    async def consume(self, tokens: int = 1) -> bool:
        """消费令牌"""
        async with self.lock:
            now = time.time()
            # 补充令牌
            time_passed = now - self.last_refill
            self.tokens = min(
                self.capacity,
                self.tokens + time_passed * self.refill_rate
            )
            self.last_refill = now
            
            # 检查是否有足够令牌
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            return False
    
    def get_wait_time(self, tokens: int = 1) -> float:
        """获取需要等待的时间"""
        if self.tokens >= tokens:
            return 0
        return (tokens - self.tokens) / self.refill_rate


class SlidingWindowCounter:
    """滑动窗口计数器"""
    
    def __init__(self, window_size: int, max_requests: int):
        self.window_size = window_size  # 窗口大小（秒）
        self.max_requests = max_requests  # 最大请求数
        self.requests = deque()
        self.lock = asyncio.Lock()
    
    async def is_allowed(self) -> bool:
        """检查是否允许请求"""
        async with self.lock:
            now = time.time()
            
            # 移除过期的请求记录
            while self.requests and self.requests[0] <= now - self.window_size:
                self.requests.popleft()
            
            # 检查是否超过限制
            if len(self.requests) < self.max_requests:
                self.requests.append(now)
                return True
            return False
    
    def get_remaining_requests(self) -> int:
        """获取剩余请求数"""
        return max(0, self.max_requests - len(self.requests))
    
    def get_reset_time(self) -> float:
        """获取重置时间"""
        if not self.requests:
            return 0
        return self.requests[0] + self.window_size


class RedisRateLimiter:
    """基于Redis的分布式限流器"""
    
    def __init__(self, redis_client, key_prefix: str = "rate_limit"):
        self.redis = redis_client
        self.key_prefix = key_prefix
    
    async def is_allowed(
        self,
        identifier: str,
        limit: int,
        window: int,
        cost: int = 1
    ) -> tuple[bool, dict]:
        """
        检查是否允许请求
        
        Args:
            identifier: 唯一标识符（如IP地址、用户ID）
            limit: 限制数量
            window: 时间窗口（秒）
            cost: 请求成本（默认1）
        
        Returns:
            (是否允许, 限流信息)
        """
        try:
            key = f"{self.key_prefix}:{identifier}"
            now = time.time()
            
            # 使用Lua脚本确保原子性
            lua_script = """
            local key = KEYS[1]
            local window = tonumber(ARGV[1])
            local limit = tonumber(ARGV[2])
            local cost = tonumber(ARGV[3])
            local now = tonumber(ARGV[4])
            
            -- 清理过期记录
            redis.call('ZREMRANGEBYSCORE', key, 0, now - window)
            
            -- 获取当前计数
            local current = redis.call('ZCARD', key)
            
            if current + cost <= limit then
                -- 允许请求，添加记录
                for i = 1, cost do
                    redis.call('ZADD', key, now, now .. ':' .. i)
                end
                redis.call('EXPIRE', key, window)
                return {1, current + cost, limit - current - cost, 0}
            else
                -- 拒绝请求
                local oldest = redis.call('ZRANGE', key, 0, 0, 'WITHSCORES')
                local reset_time = 0
                if #oldest > 0 then
                    reset_time = oldest[2] + window
                end
                return {0, current, 0, reset_time}
            end
            """
            
            result = self.redis.eval(
                lua_script,
                1,
                key,
                window,
                limit,
                cost,
                now
            )
            
            allowed, current_count, remaining, reset_time = result
            
            return bool(allowed), {
                "allowed": bool(allowed),
                "current": current_count,
                "remaining": remaining,
                "reset_time": reset_time,
                "retry_after": max(0, reset_time - now) if reset_time > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Redis限流检查失败: {str(e)}")
            # Redis失败时允许请求（fail-open策略）
            return True, {
                "allowed": True,
                "current": 0,
                "remaining": limit,
                "reset_time": 0,
                "retry_after": 0,
                "error": str(e)
            }


class RateLimitMiddleware(BaseHTTPMiddleware):
    """API限流中间件"""
    
    def __init__(
        self,
        app: ASGIApp,
        default_limit: int = 100,
        default_window: int = 60,
        redis_client=None,
        rate_limit_rules: Optional[Dict] = None,
        exempt_paths: Optional[List[str]] = None,
        key_func: Optional[Callable] = None
    ):
        super().__init__(app)
        self.default_limit = default_limit
        self.default_window = default_window
        self.redis_client = redis_client
        self.rate_limit_rules = rate_limit_rules or {}
        self.exempt_paths = exempt_paths or ["/health", "/docs", "/openapi.json"]
        self.key_func = key_func or self._default_key_func
        
        # 内存限流器（Redis不可用时的备选方案）
        self.memory_limiters = defaultdict(lambda: defaultdict(SlidingWindowCounter))
        
        # Redis限流器
        if self.redis_client:
            self.redis_limiter = RedisRateLimiter(self.redis_client)
        else:
            self.redis_limiter = None
    
    def _default_key_func(self, request: Request) -> str:
        """默认的键生成函数"""
        # 优先使用用户ID，其次使用IP地址
        if hasattr(request.state, 'user_id'):
            return f"user:{request.state.user_id}"
        
        # 获取真实IP地址
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            ip = forwarded_for.split(",")[0].strip()
        else:
            ip = request.client.host if request.client else "unknown"
        
        return f"ip:{ip}"
    
    def _get_rate_limit_config(self, request: Request) -> tuple[int, int, int]:
        """获取限流配置"""
        path = request.url.path
        method = request.method
        
        # 检查特定路径的限流规则
        for pattern, config in self.rate_limit_rules.items():
            if pattern in path:
                return (
                    config.get("limit", self.default_limit),
                    config.get("window", self.default_window),
                    config.get("cost", 1)
                )
        
        # 默认配置
        return self.default_limit, self.default_window, 1
    
    def _is_exempt(self, request: Request) -> bool:
        """检查是否豁免限流"""
        path = request.url.path
        return any(exempt_path in path for exempt_path in self.exempt_paths)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求并应用限流"""
        
        # 检查是否豁免
        if self._is_exempt(request):
            return await call_next(request)
        
        # 获取限流配置
        limit, window, cost = self._get_rate_limit_config(request)
        
        # 生成限流键
        identifier = self.key_func(request)
        
        # 执行限流检查
        if self.redis_limiter:
            allowed, info = await self.redis_limiter.is_allowed(
                identifier, limit, window, cost
            )
        else:
            # 使用内存限流器
            limiter = self.memory_limiters[identifier][f"{limit}:{window}"]
            if not hasattr(limiter, 'window_size'):
                limiter.window_size = window
                limiter.max_requests = limit
                limiter.requests = deque()
                limiter.lock = asyncio.Lock()
            
            allowed = await limiter.is_allowed()
            info = {
                "allowed": allowed,
                "current": len(limiter.requests),
                "remaining": limiter.get_remaining_requests(),
                "reset_time": limiter.get_reset_time(),
                "retry_after": max(0, limiter.get_reset_time() - time.time())
            }
        
        # 添加限流头信息
        headers = {
            "X-RateLimit-Limit": str(limit),
            "X-RateLimit-Remaining": str(info.get("remaining", 0)),
            "X-RateLimit-Reset": str(int(info.get("reset_time", 0))),
        }
        
        if not allowed:
            # 请求被限流
            headers["Retry-After"] = str(int(info.get("retry_after", 60)))
            
            logger.warning(f"请求被限流: {identifier}, 路径: {request.url.path}")
            
            return JSONResponse(
                status_code=429,
                content={
                    "error": "Too Many Requests",
                    "message": "API请求频率超过限制",
                    "limit": limit,
                    "window": window,
                    "retry_after": int(info.get("retry_after", 60))
                },
                headers=headers
            )
        
        # 执行请求
        response = await call_next(request)
        
        # 添加限流头信息到响应
        for key, value in headers.items():
            response.headers[key] = value
        
        return response


class AdaptiveRateLimitMiddleware(RateLimitMiddleware):
    """自适应限流中间件，根据系统负载动态调整限流策略"""
    
    def __init__(self, app: ASGIApp, **kwargs):
        super().__init__(app, **kwargs)
        self.load_factor = 1.0  # 负载因子
        self.error_rate = 0.0   # 错误率
        self.response_times = deque(maxlen=100)  # 最近100个响应时间
    
    def _calculate_dynamic_limit(self, base_limit: int) -> int:
        """根据系统状态计算动态限制"""
        
        # 计算平均响应时间
        if self.response_times:
            avg_response_time = sum(self.response_times) / len(self.response_times)
        else:
            avg_response_time = 0
        
        # 根据响应时间调整限制
        if avg_response_time > 2.0:  # 响应时间超过2秒
            self.load_factor = 0.5  # 减少50%限制
        elif avg_response_time > 1.0:  # 响应时间超过1秒
            self.load_factor = 0.7  # 减少30%限制
        elif avg_response_time < 0.1:  # 响应时间小于100ms
            self.load_factor = 1.2  # 增加20%限制
        else:
            self.load_factor = 1.0  # 保持原限制
        
        # 根据错误率调整
        if self.error_rate > 0.1:  # 错误率超过10%
            self.load_factor *= 0.5
        elif self.error_rate > 0.05:  # 错误率超过5%
            self.load_factor *= 0.8
        
        dynamic_limit = int(base_limit * self.load_factor)
        return max(1, dynamic_limit)  # 至少保留1个请求
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求并应用自适应限流"""
        start_time = time.time()
        
        # 检查是否豁免
        if self._is_exempt(request):
            return await call_next(request)
        
        # 获取基础限流配置
        base_limit, window, cost = self._get_rate_limit_config(request)
        
        # 计算动态限制
        dynamic_limit = self._calculate_dynamic_limit(base_limit)
        
        # 生成限流键
        identifier = self.key_func(request)
        
        # 执行限流检查（使用动态限制）
        if self.redis_limiter:
            allowed, info = await self.redis_limiter.is_allowed(
                identifier, dynamic_limit, window, cost
            )
        else:
            # 使用内存限流器
            limiter = self.memory_limiters[identifier][f"{dynamic_limit}:{window}"]
            if not hasattr(limiter, 'window_size'):
                limiter.window_size = window
                limiter.max_requests = dynamic_limit
                limiter.requests = deque()
                limiter.lock = asyncio.Lock()
            
            allowed = await limiter.is_allowed()
            info = {
                "allowed": allowed,
                "current": len(limiter.requests),
                "remaining": limiter.get_remaining_requests(),
                "reset_time": limiter.get_reset_time(),
                "retry_after": max(0, limiter.get_reset_time() - time.time())
            }
        
        # 添加限流头信息
        headers = {
            "X-RateLimit-Limit": str(dynamic_limit),
            "X-RateLimit-Remaining": str(info.get("remaining", 0)),
            "X-RateLimit-Reset": str(int(info.get("reset_time", 0))),
            "X-RateLimit-Load-Factor": f"{self.load_factor:.2f}",
        }
        
        if not allowed:
            # 请求被限流
            headers["Retry-After"] = str(int(info.get("retry_after", 60)))
            
            logger.warning(f"自适应限流: {identifier}, 动态限制: {dynamic_limit}, 负载因子: {self.load_factor:.2f}")
            
            return JSONResponse(
                status_code=429,
                content={
                    "error": "Too Many Requests",
                    "message": "API请求频率超过动态限制",
                    "limit": dynamic_limit,
                    "base_limit": base_limit,
                    "load_factor": self.load_factor,
                    "window": window,
                    "retry_after": int(info.get("retry_after", 60))
                },
                headers=headers
            )
        
        # 执行请求
        response = await call_next(request)
        
        # 记录响应时间
        response_time = time.time() - start_time
        self.response_times.append(response_time)
        
        # 更新错误率
        if response.status_code >= 500:
            # 简化的错误率计算
            recent_errors = sum(1 for _ in range(min(10, len(self.response_times))))
            self.error_rate = recent_errors / min(10, len(self.response_times))
        
        # 添加限流头信息到响应
        for key, value in headers.items():
            response.headers[key] = value
        
        # 添加性能信息
        response.headers["X-Response-Time"] = f"{response_time:.3f}"
        
        return response


def setup_rate_limiting(
    app,
    redis_client=None,
    rate_limit_type: str = "adaptive",
    **kwargs
):
    """设置限流中间件"""
    
    # 默认限流规则
    default_rules = {
        "/api/v1/bi/": {
            "limit": 60,    # BI接口每分钟60次
            "window": 60,
            "cost": 1
        },
        "/api/v1/auth/login": {
            "limit": 5,     # 登录接口每分钟5次
            "window": 60,
            "cost": 1
        },
        "/api/v1/auth/register": {
            "limit": 3,     # 注册接口每分钟3次
            "window": 60,
            "cost": 1
        }
    }
    
    # 合并配置
    config = {
        "default_limit": 100,
        "default_window": 60,
        "redis_client": redis_client,
        "rate_limit_rules": default_rules,
        "exempt_paths": ["/health", "/docs", "/openapi.json", "/preloader/status"],
        **kwargs
    }
    
    if rate_limit_type == "adaptive":
        app.add_middleware(AdaptiveRateLimitMiddleware, **config)
        logger.info("自适应限流中间件已设置")
    else:
        app.add_middleware(RateLimitMiddleware, **config)
        logger.info("标准限流中间件已设置")
    
    logger.info(f"限流配置: 默认限制={config['default_limit']}/分钟, 窗口={config['default_window']}秒")
