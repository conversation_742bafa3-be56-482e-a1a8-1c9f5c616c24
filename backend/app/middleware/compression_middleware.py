"""
API响应压缩中间件
支持gzip、brotli等压缩算法，减少网络传输数据量
"""

import gzip
import brotli
import zlib
from typing import Callable, List, Optional
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response as StarletteResponse, StreamingResponse
from starlette.types import ASGIApp
import logging

logger = logging.getLogger(__name__)


class CompressionMiddleware(BaseHTTPMiddleware):
    """响应压缩中间件"""
    
    def __init__(
        self,
        app: ASGIApp,
        minimum_size: int = 500,
        compression_level: int = 6,
        exclude_paths: Optional[List[str]] = None,
        exclude_media_types: Optional[List[str]] = None
    ):
        super().__init__(app)
        self.minimum_size = minimum_size
        self.compression_level = compression_level
        self.exclude_paths = exclude_paths or []
        self.exclude_media_types = exclude_media_types or [
            "image/",
            "video/",
            "audio/",
            "application/zip",
            "application/gzip",
            "application/x-rar-compressed"
        ]
        
        # 支持的压缩算法（按优先级排序）
        self.compression_methods = {
            "br": self._compress_brotli,
            "gzip": self._compress_gzip,
            "deflate": self._compress_deflate
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> StarletteResponse:
        """处理请求并压缩响应"""
        
        # 检查是否应该跳过压缩
        if self._should_skip_compression(request):
            return await call_next(request)
        
        # 获取客户端支持的压缩算法
        accept_encoding = request.headers.get("accept-encoding", "")
        compression_method = self._get_best_compression_method(accept_encoding)
        
        if not compression_method:
            return await call_next(request)
        
        # 执行请求
        response = await call_next(request)
        
        # 检查响应是否适合压缩
        if not self._should_compress_response(response):
            return response
        
        # 压缩响应
        return await self._compress_response(response, compression_method)
    
    def _should_skip_compression(self, request: Request) -> bool:
        """检查是否应该跳过压缩"""
        path = str(request.url.path)
        
        # 检查排除路径
        for exclude_path in self.exclude_paths:
            if exclude_path in path:
                return True
        
        return False
    
    def _get_best_compression_method(self, accept_encoding: str) -> Optional[str]:
        """获取最佳压缩方法"""
        accept_encoding = accept_encoding.lower()
        
        for method in self.compression_methods.keys():
            if method in accept_encoding:
                return method
        
        return None
    
    def _should_compress_response(self, response: StarletteResponse) -> bool:
        """检查响应是否应该压缩"""
        
        # 检查状态码
        if response.status_code < 200 or response.status_code >= 300:
            return False
        
        # 检查是否已经压缩
        if "content-encoding" in response.headers:
            return False
        
        # 检查媒体类型
        content_type = response.headers.get("content-type", "")
        for exclude_type in self.exclude_media_types:
            if content_type.startswith(exclude_type):
                return False
        
        return True
    
    async def _compress_response(self, response: StarletteResponse, method: str) -> StarletteResponse:
        """压缩响应"""
        try:
            # 获取响应内容
            if isinstance(response, StreamingResponse):
                # 处理流式响应
                return await self._compress_streaming_response(response, method)
            else:
                # 处理普通响应
                return await self._compress_regular_response(response, method)
                
        except Exception as e:
            logger.error(f"压缩响应失败: {str(e)}")
            return response
    
    async def _compress_regular_response(self, response: StarletteResponse, method: str) -> StarletteResponse:
        """压缩普通响应"""
        
        # 获取响应体
        body = b""
        async for chunk in response.body_iterator:
            body += chunk
        
        # 检查大小
        if len(body) < self.minimum_size:
            # 重新创建响应
            return StarletteResponse(
                content=body,
                status_code=response.status_code,
                headers=response.headers,
                media_type=response.media_type
            )
        
        # 压缩内容
        compress_func = self.compression_methods[method]
        compressed_body = compress_func(body)
        
        # 计算压缩率
        compression_ratio = len(compressed_body) / len(body) if len(body) > 0 else 1
        
        # 如果压缩效果不好，返回原始响应
        if compression_ratio > 0.9:
            return StarletteResponse(
                content=body,
                status_code=response.status_code,
                headers=response.headers,
                media_type=response.media_type
            )
        
        # 更新响应头
        headers = dict(response.headers)
        headers["content-encoding"] = method
        headers["content-length"] = str(len(compressed_body))
        headers["vary"] = "Accept-Encoding"
        
        # 添加压缩统计信息（仅在开发模式下）
        headers["x-compression-ratio"] = f"{compression_ratio:.3f}"
        headers["x-original-size"] = str(len(body))
        headers["x-compressed-size"] = str(len(compressed_body))
        
        logger.debug(f"响应压缩: {method}, 原始大小: {len(body)}, 压缩后: {len(compressed_body)}, 压缩率: {compression_ratio:.3f}")
        
        return StarletteResponse(
            content=compressed_body,
            status_code=response.status_code,
            headers=headers,
            media_type=response.media_type
        )
    
    async def _compress_streaming_response(self, response: StreamingResponse, method: str) -> StreamingResponse:
        """压缩流式响应"""
        
        # 收集所有数据块
        chunks = []
        total_size = 0
        
        async for chunk in response.body_iterator:
            chunks.append(chunk)
            total_size += len(chunk)
        
        # 检查大小
        if total_size < self.minimum_size:
            # 返回原始流式响应
            async def generate():
                for chunk in chunks:
                    yield chunk
            
            return StreamingResponse(
                generate(),
                status_code=response.status_code,
                headers=response.headers,
                media_type=response.media_type
            )
        
        # 合并所有数据块
        body = b"".join(chunks)
        
        # 压缩内容
        compress_func = self.compression_methods[method]
        compressed_body = compress_func(body)
        
        # 计算压缩率
        compression_ratio = len(compressed_body) / len(body) if len(body) > 0 else 1
        
        # 如果压缩效果不好，返回原始响应
        if compression_ratio > 0.9:
            async def generate():
                for chunk in chunks:
                    yield chunk
            
            return StreamingResponse(
                generate(),
                status_code=response.status_code,
                headers=response.headers,
                media_type=response.media_type
            )
        
        # 更新响应头
        headers = dict(response.headers)
        headers["content-encoding"] = method
        headers["content-length"] = str(len(compressed_body))
        headers["vary"] = "Accept-Encoding"
        
        # 添加压缩统计信息
        headers["x-compression-ratio"] = f"{compression_ratio:.3f}"
        headers["x-original-size"] = str(len(body))
        headers["x-compressed-size"] = str(len(compressed_body))
        
        logger.debug(f"流式响应压缩: {method}, 原始大小: {len(body)}, 压缩后: {len(compressed_body)}, 压缩率: {compression_ratio:.3f}")
        
        # 返回压缩后的流式响应
        async def generate_compressed():
            yield compressed_body
        
        return StreamingResponse(
            generate_compressed(),
            status_code=response.status_code,
            headers=headers,
            media_type=response.media_type
        )
    
    def _compress_gzip(self, data: bytes) -> bytes:
        """使用gzip压缩"""
        return gzip.compress(data, compresslevel=self.compression_level)
    
    def _compress_deflate(self, data: bytes) -> bytes:
        """使用deflate压缩"""
        return zlib.compress(data, level=self.compression_level)
    
    def _compress_brotli(self, data: bytes) -> bytes:
        """使用brotli压缩"""
        try:
            # brotli压缩级别范围是0-11
            brotli_level = min(11, max(0, self.compression_level))
            return brotli.compress(data, quality=brotli_level)
        except Exception as e:
            logger.warning(f"Brotli压缩失败，回退到gzip: {str(e)}")
            return self._compress_gzip(data)


class SmartCompressionMiddleware(CompressionMiddleware):
    """智能压缩中间件，根据内容类型和大小自动调整压缩策略"""
    
    def __init__(self, app: ASGIApp, **kwargs):
        super().__init__(app, **kwargs)
        
        # 不同内容类型的压缩配置
        self.content_type_configs = {
            "application/json": {
                "minimum_size": 100,
                "compression_level": 6,
                "preferred_method": "br"
            },
            "text/html": {
                "minimum_size": 200,
                "compression_level": 6,
                "preferred_method": "br"
            },
            "text/css": {
                "minimum_size": 200,
                "compression_level": 9,
                "preferred_method": "br"
            },
            "application/javascript": {
                "minimum_size": 200,
                "compression_level": 9,
                "preferred_method": "br"
            },
            "text/plain": {
                "minimum_size": 500,
                "compression_level": 6,
                "preferred_method": "gzip"
            }
        }
    
    def _get_content_type_config(self, content_type: str) -> dict:
        """获取内容类型的压缩配置"""
        for ct, config in self.content_type_configs.items():
            if content_type.startswith(ct):
                return config
        
        # 默认配置
        return {
            "minimum_size": self.minimum_size,
            "compression_level": self.compression_level,
            "preferred_method": None
        }
    
    async def _compress_response(self, response: StarletteResponse, method: str) -> StarletteResponse:
        """智能压缩响应"""
        
        # 获取内容类型配置
        content_type = response.headers.get("content-type", "")
        config = self._get_content_type_config(content_type)
        
        # 临时调整配置
        original_minimum_size = self.minimum_size
        original_compression_level = self.compression_level
        
        self.minimum_size = config["minimum_size"]
        self.compression_level = config["compression_level"]
        
        # 如果有首选压缩方法，优先使用
        if config["preferred_method"] and config["preferred_method"] in self.compression_methods:
            method = config["preferred_method"]
        
        try:
            result = await super()._compress_response(response, method)
        finally:
            # 恢复原始配置
            self.minimum_size = original_minimum_size
            self.compression_level = original_compression_level
        
        return result


def setup_compression_middleware(app, compression_type: str = "smart", **kwargs):
    """设置压缩中间件"""
    
    # 默认配置
    default_config = {
        "minimum_size": 500,
        "compression_level": 6,
        "exclude_paths": ["/uploads/", "/static/", "/health"],
        "exclude_media_types": [
            "image/", "video/", "audio/",
            "application/zip", "application/gzip",
            "application/x-rar-compressed", "application/pdf"
        ]
    }
    
    # 合并配置
    config = {**default_config, **kwargs}
    
    if compression_type == "smart":
        app.add_middleware(SmartCompressionMiddleware, **config)
        logger.info("智能压缩中间件已设置")
    else:
        app.add_middleware(CompressionMiddleware, **config)
        logger.info("标准压缩中间件已设置")
    
    logger.info(f"压缩配置: 最小大小={config['minimum_size']}字节, 压缩级别={config['compression_level']}")
