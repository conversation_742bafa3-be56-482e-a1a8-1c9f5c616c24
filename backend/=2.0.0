Collecting email-validator
  Using cached email_validator-2.2.0-py3-none-any.whl.metadata (25 kB)
Collecting dnspython>=2.0.0 (from email-validator)
  Using cached dnspython-2.7.0-py3-none-any.whl.metadata (5.8 kB)
Requirement already satisfied: idna>=2.0.0 in ./venv/lib/python3.12/site-packages (from email-validator) (3.10)
Using cached email_validator-2.2.0-py3-none-any.whl (33 kB)
Using cached dnspython-2.7.0-py3-none-any.whl (313 kB)
Installing collected packages: dnspython, email-validator
Successfully installed dnspython-2.7.0 email-validator-2.2.0
