#!/bin/bash

# 测试端口配置脚本

echo "🔍 检查端口配置..."
echo ""

# 检查manage.sh中的端口配置
echo "📋 manage.sh 端口配置:"
grep -n "FRONTEND_PORT\|BACKEND_PORT" ./manage.sh

echo ""

# 检查vite.config.js中的端口配置
echo "📋 vite.config.js 端口配置:"
grep -n "port:" frontend/vite.config.js

echo ""

# 检查run.py中的端口配置
echo "📋 run.py 端口配置:"
grep -n "port=" backend/run.py

echo ""

# 检查menu.sh中的URL配置
echo "📋 menu.sh URL配置:"
grep -n "http://10.10.10.207" ./menu.sh

echo ""
echo "✅ 端口配置检查完成！"
echo ""
echo "📊 预期配置:"
echo "  - 前端端口: 8080"
echo "  - 后端端口: 8000"
echo "  - 数据库管理端口: 8081"
