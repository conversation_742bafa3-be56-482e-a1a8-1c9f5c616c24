
> massage-chain-management-system@1.0.0 dev
> vite


  VITE v4.5.14  ready in 1705 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: http://************:5173/
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

  ╷
1 │ @import './main.scss';
  │         ^^^^^^^^^^^^^
  ╵
    src/styles/index.scss 1:9  root stylesheet

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

11:53:05 PM [vite] hmr update /src/views/appointment/AppointmentCalendar.vue
11:53:39 PM [vite] hmr update /src/views/appointment/AppointmentCalendar.vue
11:55:10 PM [vite] hmr update /src/views/order/OrderList.vue
11:55:47 PM [vite] hmr update /src/views/order/OrderList.vue
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

11:57:55 PM [vite] hmr update /src/views/order/OrderList.vue
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

11:58:43 PM [vite] hmr update /src/views/order/OrderList.vue
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

12:26:30 AM [vite] hmr update /src/components/business_intelligence/ShareReportModal.vue
12:29:03 AM [vite] hmr update /src/components/business_intelligence/ShareReportModal.vue
12:52:44 AM [vite] hmr update /src/views/business_intelligence/MultiDimensionalAnalysis.vue
12:54:40 AM [vite] hmr update /src/views/business_intelligence/MultiDimensionalAnalysis.vue
12:57:46 AM [vite] hmr update /src/views/business_intelligence/ChartGallery.vue
12:58:14 AM [vite] hmr update /src/views/business_intelligence/ChartGallery.vue
12:58:39 AM [vite] hmr update /src/views/business_intelligence/ChartGallery.vue
12:59:02 AM [vite] hmr update /src/views/business_intelligence/ChartGallery.vue
12:59:26 AM [vite] hmr update /src/views/business_intelligence/ChartGallery.vue?vue&type=style&index=0&scoped=62259b7d&lang.css
Invalid end tag.
1:00:46 AM [vite] Internal server error: Invalid end tag.
  Plugin: vite:vue
  File: /root/vip/frontend/src/components/business_intelligence/ShareReportModal.vue:47:11
  45 |                </div>
  46 |              </div>
  47 |            </a-tab-pane>
     |             ^
  48 |            
  49 |            <a-tab-pane key="qrcode" tab="二维码分享">
      at createCompilerError (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:1364:17)
      at emitError (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2949:5)
      at Object.onclosetag (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2318:9)
      at Tokenizer.stateInClosingTagName (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:785:16)
      at Tokenizer.parse (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:1143:16)
      at Object.baseParse (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2988:13)
      at Object.parse (/root/vip/frontend/node_modules/@vue/compiler-dom/dist/compiler-dom.cjs.js:907:23)
      at Object.parse$1 [as parse] (/root/vip/frontend/node_modules/@vue/compiler-sfc/dist/compiler-sfc.cjs.js:1794:24)
      at createDescriptor (/root/vip/frontend/node_modules/@vitejs/plugin-vue/dist/index.cjs:86:43)
      at handleHotUpdate (/root/vip/frontend/node_modules/@vitejs/plugin-vue/dist/index.cjs:2167:26)
1:01:44 AM [vite] hmr update /src/components/business_intelligence/ShareReportModal.vue
Invalid end tag.
1:01:44 AM [vite] Internal server error: Invalid end tag.
  Plugin: vite:vue
  File: /root/vip/frontend/src/components/business_intelligence/ShareReportModal.vue:61:13
  59 |                  </a-button>
  60 |                </div>
  61 |              </div>
     |               ^
  62 |            </a-tab-pane>
  63 |            
      at createCompilerError (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:1364:17)
      at emitError (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2949:5)
      at Object.onclosetag (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2318:9)
      at Tokenizer.stateInClosingTagName (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:785:16)
      at Tokenizer.parse (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:1143:16)
      at Object.baseParse (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2988:13)
      at Object.parse (/root/vip/frontend/node_modules/@vue/compiler-dom/dist/compiler-dom.cjs.js:907:23)
      at Object.parse$1 [as parse] (/root/vip/frontend/node_modules/@vue/compiler-sfc/dist/compiler-sfc.cjs.js:1794:24)
      at createDescriptor (/root/vip/frontend/node_modules/@vitejs/plugin-vue/dist/index.cjs:86:43)
      at handleHotUpdate (/root/vip/frontend/node_modules/@vitejs/plugin-vue/dist/index.cjs:2167:26)
1:02:16 AM [vite] hmr update /src/components/business_intelligence/ShareReportModal.vue
Invalid end tag.
1:02:16 AM [vite] Internal server error: Invalid end tag.
  Plugin: vite:vue
  File: /root/vip/frontend/src/components/business_intelligence/ShareReportModal.vue:66:15
  64 |                    />
  65 |                  </a-form-item>
  66 |                </a-form>
     |                 ^
  67 |              </div>
  68 |            </a-tab-pane>
      at createCompilerError (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:1364:17)
      at emitError (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2949:5)
      at Object.onclosetag (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2318:9)
      at Tokenizer.stateInClosingTagName (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:785:16)
      at Tokenizer.parse (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:1143:16)
      at Object.baseParse (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2988:13)
      at Object.parse (/root/vip/frontend/node_modules/@vue/compiler-dom/dist/compiler-dom.cjs.js:907:23)
      at Object.parse$1 [as parse] (/root/vip/frontend/node_modules/@vue/compiler-sfc/dist/compiler-sfc.cjs.js:1794:24)
      at createDescriptor (/root/vip/frontend/node_modules/@vitejs/plugin-vue/dist/index.cjs:86:43)
      at handleHotUpdate (/root/vip/frontend/node_modules/@vitejs/plugin-vue/dist/index.cjs:2167:26)
1:02:53 AM [vite] hmr update /src/components/business_intelligence/ShareReportModal.vue
Invalid end tag.
1:02:53 AM [vite] Internal server error: Invalid end tag.
  Plugin: vite:vue
  File: /root/vip/frontend/src/components/business_intelligence/ShareReportModal.vue:52:1
  50 |  
  51 |  
  52 |  </template>
     |   ^
  53 |  
  54 |  <script>
      at createCompilerError (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:1364:17)
      at emitError (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2949:5)
      at Object.onclosetag (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2318:9)
      at Tokenizer.stateInClosingTagName (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:785:16)
      at Tokenizer.parse (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:1143:16)
      at Object.baseParse (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2988:13)
      at Object.parse (/root/vip/frontend/node_modules/@vue/compiler-dom/dist/compiler-dom.cjs.js:907:23)
      at Object.parse$1 [as parse] (/root/vip/frontend/node_modules/@vue/compiler-sfc/dist/compiler-sfc.cjs.js:1794:24)
      at createDescriptor (/root/vip/frontend/node_modules/@vitejs/plugin-vue/dist/index.cjs:86:43)
      at handleHotUpdate (/root/vip/frontend/node_modules/@vitejs/plugin-vue/dist/index.cjs:2167:26)
1:11:07 AM [vite] hmr update /src/components/business_intelligence/ShareReportModal.vue
1:11:40 AM [vite] hmr update /src/components/business_intelligence/ShareReportModal.vue
1:18:54 AM [vite] hmr update /src/components/business_intelligence/ShareReportModal.vue, /src/components/business_intelligence/ShareReportModal.vue?vue&type=style&index=0&scoped=f139f9f9&lang.css
1:26:43 AM [vite] hmr update /src/components/business_intelligence/ShareReportModal.vue
1:30:37 AM [vite] Internal server error: Attribute name cannot contain U+0022 ("), U+0027 ('), and U+003C (<).
  Plugin: vite:vue
  File: /root/vip/frontend/src/views/business_intelligence/AIAnalytics.vue:37:59
  35 |          </div>
  36 |          <div v-else-if="!hasComprehensiveInsights" class="empty-container">
  37 |            <el-empty description="暂无分析数据，请选择门店和日期范围，点击"开始分析"按钮" />
     |                                                             ^
  38 |          </div>
  39 |          <div v-else>
      at createCompilerError (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:1364:17)
      at Object.emitError [as onerr] (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2949:5)
      at Tokenizer.stateInAttrName (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:854:16)
      at Tokenizer.parse (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:1103:16)
      at Object.baseParse (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2988:13)
      at Object.parse (/root/vip/frontend/node_modules/@vue/compiler-dom/dist/compiler-dom.cjs.js:907:23)
      at Object.parse$1 [as parse] (/root/vip/frontend/node_modules/@vue/compiler-sfc/dist/compiler-sfc.cjs.js:1794:24)
      at createDescriptor (/root/vip/frontend/node_modules/@vitejs/plugin-vue/dist/index.cjs:86:43)
      at transformMain (/root/vip/frontend/node_modules/@vitejs/plugin-vue/dist/index.cjs:2331:34)
      at TransformContext.transform (/root/vip/frontend/node_modules/@vitejs/plugin-vue/dist/index.cjs:2881:16)
1:31:28 AM [vite] Internal server error: Attribute name cannot contain U+0022 ("), U+0027 ('), and U+003C (<).
  Plugin: vite:vue
  File: /root/vip/frontend/src/views/business_intelligence/AIAnalytics.vue:66:63
  64 |          </div>
  65 |          <div v-else-if="!hasSalesInsights" class="empty-container">
  66 |            <el-empty description="暂无销售模式分析数据，请选择门店和日期范围，点击"开始分析"按钮" />
     |                                                                 ^
  67 |          </div>
  68 |          <div v-else class="insights-container">
      at createCompilerError (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:1364:17)
      at Object.emitError [as onerr] (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2949:5)
      at Tokenizer.stateInAttrName (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:854:16)
      at Tokenizer.parse (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:1103:16)
      at Object.baseParse (/root/vip/frontend/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2988:13)
      at Object.parse (/root/vip/frontend/node_modules/@vue/compiler-dom/dist/compiler-dom.cjs.js:907:23)
      at Object.parse$1 [as parse] (/root/vip/frontend/node_modules/@vue/compiler-sfc/dist/compiler-sfc.cjs.js:1794:24)
      at createDescriptor (/root/vip/frontend/node_modules/@vitejs/plugin-vue/dist/index.cjs:86:43)
      at handleHotUpdate (/root/vip/frontend/node_modules/@vitejs/plugin-vue/dist/index.cjs:2167:26)
      at async handleHMRUpdate (file:///root/vip/frontend/node_modules/vite/dist/node/chunks/dep-827b23df.js:41308:33)
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

2:23:58 AM [vite] hmr update /src/views/customer/CustomerList.vue
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

2:29:56 AM [vite] hmr update /src/views/shareholder/ShareholderDetail.vue
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

2:36:45 AM [vite] hmr update /src/views/store/StoreDetail.vue
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

2:03:01 AM [vite] page reload public/test_bi_api.html
2:53:17 AM [vite] hmr update /src/views/business_intelligence/DataDashboard.vue
2:54:16 AM [vite] hmr update /src/views/business_intelligence/DataDashboard.vue
4:24:08 AM [vite] hmr update /src/views/business_intelligence/DataDashboard.vue
10:30:19 AM [vite] hmr update /src/views/business_intelligence/DataDashboard.vue
10:32:40 AM [vite] hmr update /src/views/business_intelligence/MultiDimensionalAnalysis.vue, /src/views/business_intelligence/UserDashboards.vue, /src/views/business_intelligence/DataDashboard.vue
10:32:59 AM [vite] hmr update /src/views/business_intelligence/MultiDimensionalAnalysis.vue, /src/views/business_intelligence/UserDashboards.vue, /src/views/business_intelligence/DataDashboard.vue
10:33:21 AM [vite] hmr update /src/views/business_intelligence/MultiDimensionalAnalysis.vue, /src/views/business_intelligence/UserDashboards.vue, /src/views/business_intelligence/DataDashboard.vue
10:33:58 AM [vite] hmr update /src/views/business_intelligence/MultiDimensionalAnalysis.vue, /src/views/business_intelligence/UserDashboards.vue, /src/views/business_intelligence/DataDashboard.vue
10:34:36 AM [vite] hmr update /src/views/business_intelligence/MultiDimensionalAnalysis.vue, /src/views/business_intelligence/UserDashboards.vue, /src/views/business_intelligence/DataDashboard.vue
10:35:11 AM [vite] hmr update /src/views/business_intelligence/MultiDimensionalAnalysis.vue, /src/views/business_intelligence/UserDashboards.vue, /src/views/business_intelligence/DataDashboard.vue
10:40:24 AM [vite] hmr update /src/views/business_intelligence/DataDashboard.vue
