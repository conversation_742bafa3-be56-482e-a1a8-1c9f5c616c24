
> massage-chain-management-system@1.0.0 dev
> vite


  VITE v4.5.14  ready in 483 ms

  ➜  Local:   http://localhost:8080/
  ➜  Network: http://************:8080/
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

  ╷
1 │ @import './main.scss';
  │         ^^^^^^^^^^^^^
  ╵
    src/styles/index.scss 1:9  root stylesheet

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

8:32:59 PM [vite] hmr update /src/views/customer/CustomerList.vue
8:32:59 PM [vite] Internal server error: [vue/compiler-sfc] Identifier 'deleteCustomer' has already been declared. (218:6)

/root/vip/frontend/src/views/customer/CustomerList.vue
497|  }
498|  
499|  const deleteCustomer = async (customer) => {
   |        ^
500|    try {
501|      await ElMessageBox.confirm(
  Plugin: vite:vue
  File: /root/vip/frontend/src/views/customer/CustomerList.vue:218:6
  141|            <el-button 
  142|              type="danger" 
  143|              size="small" 
     |               ^
  144|              @click="deleteCustomer(scope.row)"
  145|              :disabled="scope.row.status === 'inactive'"
      at constructor (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:367:19)
      at Parser.raise (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:6627:19)
      at ScopeHandler.checkRedeclarationInScope (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:1644:19)
      at ScopeHandler.declareName (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:1610:12)
      at Parser.declareNameFromIdentifier (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:7591:16)
      at Parser.checkIdentifier (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:7587:12)
      at Parser.checkLVal (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:7526:12)
      at Parser.parseVarId (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:13412:10)
      at Parser.parseVar (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:13383:12)
      at Parser.parseVarStatement (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:13230:10)
8:33:49 PM [vite] hmr update /src/views/customer/CustomerList.vue
8:33:49 PM [vite] Internal server error: [vue/compiler-sfc] Identifier 'deleteCustomer' has already been declared. (187:6)

/root/vip/frontend/src/views/customer/CustomerList.vue
466|  }
467|  
468|  const deleteCustomer = async (customer) => {
   |        ^
469|    try {
470|      await ElMessageBox.confirm(
  Plugin: vite:vue
  File: /root/vip/frontend/src/views/customer/CustomerList.vue:187:6
  120|          <template #default="scope">
  121|            <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'" size="small">
  122|              {{ scope.row.status === 'active' ? '正常' : '禁用' }}
     |                                                          ^
  123|            </el-tag>
  124|          </template>
      at constructor (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:367:19)
      at Parser.raise (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:6627:19)
      at ScopeHandler.checkRedeclarationInScope (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:1644:19)
      at ScopeHandler.declareName (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:1610:12)
      at Parser.declareNameFromIdentifier (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:7591:16)
      at Parser.checkIdentifier (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:7587:12)
      at Parser.checkLVal (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:7526:12)
      at Parser.parseVarId (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:13412:10)
      at Parser.parseVar (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:13383:12)
      at Parser.parseVarStatement (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:13230:10)
8:34:36 PM [vite] hmr update /src/views/customer/CustomerList.vue
8:34:36 PM [vite] Internal server error: [vue/compiler-sfc] Identifier 'deleteCustomer' has already been declared. (187:6)

/root/vip/frontend/src/views/customer/CustomerList.vue
466|  }
467|  
468|  const deleteCustomer = async (customer) => {
   |        ^
469|    try {
470|      await ElMessageBox.confirm(
  Plugin: vite:vue
  File: /root/vip/frontend/src/views/customer/CustomerList.vue:187:6
  120|          <template #default="scope">
  121|            <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'" size="small">
  122|              {{ scope.row.status === 'active' ? '正常' : '禁用' }}
     |                                                          ^
  123|            </el-tag>
  124|          </template>
      at constructor (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:367:19)
      at Parser.raise (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:6627:19)
      at ScopeHandler.checkRedeclarationInScope (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:1644:19)
      at ScopeHandler.declareName (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:1610:12)
      at Parser.declareNameFromIdentifier (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:7591:16)
      at Parser.checkIdentifier (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:7587:12)
      at Parser.checkLVal (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:7526:12)
      at Parser.parseVarId (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:13412:10)
      at Parser.parseVar (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:13383:12)
      at Parser.parseVarStatement (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:13230:10)
8:34:58 PM [vite] Internal server error: [vue/compiler-sfc] Identifier 'deleteCustomer' has already been declared. (187:6)

/root/vip/frontend/src/views/customer/CustomerList.vue
466|  }
467|  
468|  const deleteCustomer = async (customer) => {
   |        ^
469|    try {
470|      await ElMessageBox.confirm(
  Plugin: vite:vue
  File: /root/vip/frontend/src/views/customer/CustomerList.vue:187:6
  120|          <template #default="scope">
  121|            <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'" size="small">
  122|              {{ scope.row.status === 'active' ? '正常' : '禁用' }}
     |                                                          ^
  123|            </el-tag>
  124|          </template>
      at constructor (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:367:19)
      at Parser.raise (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:6627:19)
      at ScopeHandler.checkRedeclarationInScope (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:1644:19)
      at ScopeHandler.declareName (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:1610:12)
      at Parser.declareNameFromIdentifier (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:7591:16)
      at Parser.checkIdentifier (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:7587:12)
      at Parser.checkLVal (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:7526:12)
      at Parser.parseVarId (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:13412:10)
      at Parser.parseVar (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:13383:12)
      at Parser.parseVarStatement (/root/vip/frontend/node_modules/@babel/parser/lib/index.js:13230:10)
8:35:20 PM [vite] hmr update /src/views/customer/CustomerList.vue
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

8:36:22 PM [vite] hmr update /src/views/customer/CustomerList.vue
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

8:38:44 PM [vite] hmr update /src/views/customer/CustomerList.vue
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

8:39:48 PM [vite] hmr update /src/views/customer/CustomerList.vue
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

8:43:22 PM [vite] hmr update /src/views/customer/CustomerList.vue
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

8:44:23 PM [vite] hmr update /src/views/customer/CustomerList.vue
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

8:49:39 PM [vite] hmr update /src/views/customer/CustomerList.vue
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

