INFO:     Will watch for changes in these directories: ['/root/vip/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [6073] using StatReload
/root/vip/backend/venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
2025-07-28 16:12:34,296 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:12:35,051 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:12:35,052 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:12:36,604 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:12:36,606 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 16:12:36,606 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 16:12:36,606 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 16:12:36,607 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 16:12:36,607 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 16:12:36,607 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
INFO:     Started server process [6089]
INFO:     Waiting for application startup.
2025-07-28 16:12:37,319 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 16:12:37,320 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 16:12:37,320 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 16:12:37,320 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 16:12:37,325 - app.services.monitoring_service - INFO - 监控系统已初始化
INFO:     Application startup complete.
2025-07-28 16:12:38,172 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 0.85秒
INFO:     *********:59281 - "GET /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     *********:59282 - "GET /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     *********:59280 - "GET /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     *********:59279 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *********:59283 - "GET /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
2025-07-28 16:28:07,738 - app.middleware.rate_limit_middleware - WARNING - 自适应限流: ip:*********, 动态限制: 5, 负载因子: 1.00
INFO:     *********:59314 - "OPTIONS /api/v1/auth/login HTTP/1.1" 429 Too Many Requests
2025-07-28 16:28:12,821 - app.middleware.rate_limit_middleware - WARNING - 自适应限流: ip:*********, 动态限制: 5, 负载因子: 1.00
INFO:     *********:59330 - "OPTIONS /api/v1/auth/login HTTP/1.1" 429 Too Many Requests
