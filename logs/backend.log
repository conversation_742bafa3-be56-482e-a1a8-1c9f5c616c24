INFO:     Will watch for changes in these directories: ['/root/vip/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [1944] using StatReload
数据库索引创建完成
/root/vip/backend/venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [1971]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     ************:50393 - "GET /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ************:50395 - "GET /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ************:50391 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ************:50392 - "GET /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ************:50394 - "GET /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ************:50401 - "OPTIONS /api/v1/auth/login HTTP/1.1" 200 OK
(trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/root/vip/backend/venv/lib/python3.12/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
INFO:     ************:50402 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
INFO:     ************:50402 - "OPTIONS /api/v1/auth/me HTTP/1.1" 200 OK
INFO:     ************:50401 - "GET /api/v1/auth/me HTTP/1.1" 200 OK
INFO:     ************:50410 - "OPTIONS /api/v1/member-levels/ HTTP/1.1" 200 OK
INFO:     ************:50411 - "GET /api/v1/member-levels/ HTTP/1.1" 200 OK
INFO:     ************:50412 - "OPTIONS /api/v1/member-levels/2/benefits HTTP/1.1" 200 OK
INFO:     ************:50413 - "OPTIONS /api/v1/member-levels/2/benefits HTTP/1.1" 200 OK
INFO:     ************:50414 - "GET /api/v1/member-levels/2/benefits HTTP/1.1" 200 OK
INFO:     ************:50415 - "GET /api/v1/member-levels/2/benefits HTTP/1.1" 200 OK
INFO:     ************:50417 - "GET /api/v1/member-levels/ HTTP/1.1" 200 OK
INFO:     ************:50483 - "OPTIONS /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ************:50484 - "GET /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ************:50560 - "OPTIONS /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ************:50561 - "OPTIONS /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ************:50563 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ************:50562 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ************:50563 - "OPTIONS /api/v1/employees-enhanced/enhanced?store_id=&skill_tag= HTTP/1.1" 200 OK
INFO:     ************:50562 - "OPTIONS /api/v1/employees-enhanced/skills HTTP/1.1" 200 OK
INFO:     ************:50560 - "GET /api/v1/employees-enhanced/enhanced?store_id=&skill_tag= HTTP/1.1" 422 Unprocessable Entity
INFO:     ************:50561 - "GET /api/v1/employees-enhanced/skills HTTP/1.1" 200 OK
INFO:     ************:50561 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ************:50560 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ************:50560 - "GET /api/v1/employees-enhanced/enhanced?store_id=&skill_tag= HTTP/1.1" 422 Unprocessable Entity
INFO:     ************:50561 - "GET /api/v1/employees-enhanced/skills HTTP/1.1" 200 OK
INFO:     ************:50560 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ************:50561 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ************:50560 - "GET /api/v1/employees-enhanced/enhanced?store_id=&skill_tag= HTTP/1.1" 422 Unprocessable Entity
INFO:     ************:50561 - "GET /api/v1/employees-enhanced/skills HTTP/1.1" 200 OK
INFO:     ************:50561 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ************:50560 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ************:50561 - "GET /api/v1/employees-enhanced/enhanced?store_id=&skill_tag= HTTP/1.1" 422 Unprocessable Entity
INFO:     ************:50560 - "GET /api/v1/employees-enhanced/skills HTTP/1.1" 200 OK
INFO:     ************:50561 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ************:50560 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ************:50561 - "GET /api/v1/employees-enhanced/enhanced?store_id=&skill_tag= HTTP/1.1" 422 Unprocessable Entity
INFO:     ************:50560 - "GET /api/v1/employees-enhanced/skills HTTP/1.1" 200 OK
INFO:     ************:50560 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ************:50561 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ************:50561 - "GET /api/v1/employees-enhanced/enhanced?store_id=&skill_tag= HTTP/1.1" 422 Unprocessable Entity
INFO:     ************:50560 - "GET /api/v1/employees-enhanced/skills HTTP/1.1" 200 OK
INFO:     ************:50762 - "OPTIONS /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ************:50763 - "OPTIONS /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ************:50764 - "OPTIONS /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ************:50765 - "OPTIONS /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ************:50766 - "OPTIONS /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ************:50762 - "GET /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ************:50766 - "GET /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ************:50767 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ************:50763 - "GET /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ************:50765 - "GET /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     *********:63856 - "OPTIONS /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     *********:63855 - "OPTIONS /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *********:63858 - "OPTIONS /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     *********:63859 - "OPTIONS /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     *********:63857 - "OPTIONS /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     *********:63863 - "GET /api/v1/dashboard/stats HTTP/1.1" 500 Internal Server Error
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
psycopg2.OperationalError: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/root/vip/backend/venv/lib/python3.12/site-packages/uvicorn/protocols/http/h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 75, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/fastapi/routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/fastapi/routing.py", line 215, in run_endpoint_function
    return await run_in_threadpool(dependant.call, **values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/concurrency.py", line 38, in run_in_threadpool
    return await anyio.to_thread.run_sync(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/anyio/to_thread.py", line 56, in run_sync
    return await get_async_backend().run_sync_in_worker_thread(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/anyio/_backends/_asyncio.py", line 2470, in run_sync_in_worker_thread
    return await future
           ^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/anyio/_backends/_asyncio.py", line 967, in run
    result = context.run(func, *args)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/app/api/api_v1/endpoints/dashboard.py", line 28, in get_dashboard_stats
    ).scalar() or 0
      ^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/orm/query.py", line 2835, in scalar
    ret = self.one()
          ^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/orm/query.py", line 2808, in one
    return self._iter().one()  # type: ignore
           ^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/orm/query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/orm/context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/sql/elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (psycopg2.OperationalError) server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

[SQL: SELECT sum(orders.actual_amount) AS sum_1 
FROM orders 
WHERE date(orders.order_time) = %(date_1)s AND orders.status IN (%(status_1_1)s, %(status_1_2)s)]
[parameters: {'date_1': datetime.date(2025, 7, 26), 'status_1_1': 'paid', 'status_1_2': 'completed'}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
INFO:     *********:63865 - "GET /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     *********:63862 - "GET /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     *********:63864 - "GET /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     *********:63866 - "GET /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     *********:63876 - "OPTIONS /api/v1/member-levels/ HTTP/1.1" 200 OK
INFO:     *********:63877 - "GET /api/v1/member-levels/ HTTP/1.1" 200 OK
INFO:     *********:63882 - "GET /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     *********:63883 - "GET /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     *********:63881 - "GET /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     *********:63880 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *********:63884 - "GET /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     *********:63908 - "GET /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     *********:63909 - "GET /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     *********:63907 - "GET /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     *********:63906 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     *********:63910 - "GET /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ********:35450 - "OPTIONS /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:35456 - "OPTIONS /api/v1/employees HTTP/1.1" 200 OK
INFO:     ********:35468 - "OPTIONS /api/v1/store-items/ HTTP/1.1" 200 OK
INFO:     ********:35462 - "OPTIONS /api/v1/employees HTTP/1.1" 200 OK
INFO:     ********:35472 - "OPTIONS /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:35484 - "OPTIONS /api/v1/store-items/ HTTP/1.1" 200 OK
INFO:     ********:35456 - "GET /api/v1/employees HTTP/1.1" 307 Temporary Redirect
INFO:     ********:35456 - "GET /api/v1/employees HTTP/1.1" 307 Temporary Redirect
INFO:     ********:35484 - "OPTIONS /api/v1/employees/ HTTP/1.1" 200 OK
INFO:     ********:35456 - "OPTIONS /api/v1/employees/ HTTP/1.1" 200 OK
INFO:     ********:35450 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:35472 - "GET /api/v1/employees/ HTTP/1.1" 200 OK
INFO:     ********:35468 - "GET /api/v1/store-items/ HTTP/1.1" 200 OK
INFO:     ********:35462 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:35468 - "GET /api/v1/employees/ HTTP/1.1" 200 OK
INFO:     ********:35484 - "GET /api/v1/store-items/ HTTP/1.1" 200 OK
INFO:     ********:51524 - "OPTIONS /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ********:51528 - "OPTIONS /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ********:51530 - "OPTIONS /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ********:51536 - "OPTIONS /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ********:51532 - "OPTIONS /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ********:51548 - "OPTIONS /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ********:51524 - "OPTIONS /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ********:51528 - "OPTIONS /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ********:51530 - "OPTIONS /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ********:51536 - "OPTIONS /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ********:51548 - "GET /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ********:51532 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ********:51528 - "GET /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ********:51536 - "GET /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ********:51524 - "GET /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ********:51532 - "GET /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ********:51548 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ********:51528 - "GET /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ********:51530 - "GET /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ********:51524 - "GET /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ********:37116 - "OPTIONS /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:37106 - "OPTIONS /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:37116 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:37106 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:34066 - "OPTIONS /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:34070 - "OPTIONS /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:34066 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:34070 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:49842 - "OPTIONS /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:49842 - "OPTIONS /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:49854 - "OPTIONS /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:49854 - "OPTIONS /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:49842 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:49854 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:49842 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:49854 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:40402 - "OPTIONS /api/v1/employees/2 HTTP/1.1" 200 OK
INFO:     ********:40414 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:40402 - "GET /api/v1/employees/2 HTTP/1.1" 200 OK
INFO:     ********:40402 - "OPTIONS /api/v1/stores/1 HTTP/1.1" 200 OK
INFO:     ********:40414 - "GET /api/v1/stores/1 HTTP/1.1" 200 OK
INFO:     ********:40420 - "OPTIONS /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:45168 - "OPTIONS /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:40420 - "GET /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:45168 - "GET /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:45180 - "GET /api/v1/stores/1 HTTP/1.1" 200 OK
INFO:     ********:40754 - "OPTIONS /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:40766 - "OPTIONS /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:40754 - "GET /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:40766 - "GET /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:40780 - "OPTIONS /api/v1/shareholders/1 HTTP/1.1" 200 OK
INFO:     ********:48836 - "OPTIONS /api/v1/shareholder-investment-records/?shareholder_id=1 HTTP/1.1" 200 OK
INFO:     ********:48846 - "OPTIONS /api/v1/shareholder-investment-records/shareholder/1/summary HTTP/1.1" 200 OK
INFO:     ********:48828 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:48836 - "GET /api/v1/shareholder-investment-records/shareholder/1/summary HTTP/1.1" 500 Internal Server Error
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/root/vip/backend/venv/lib/python3.12/site-packages/uvicorn/protocols/http/h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 75, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/fastapi/routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/fastapi/routing.py", line 215, in run_endpoint_function
    return await run_in_threadpool(dependant.call, **values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/concurrency.py", line 38, in run_in_threadpool
    return await anyio.to_thread.run_sync(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/anyio/to_thread.py", line 56, in run_sync
    return await get_async_backend().run_sync_in_worker_thread(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/anyio/_backends/_asyncio.py", line 2470, in run_sync_in_worker_thread
    return await future
           ^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/anyio/_backends/_asyncio.py", line 967, in run
    result = context.run(func, *args)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/app/api/api_v1/endpoints/shareholder_investment_records.py", line 214, in get_shareholder_investment_summary
    func.case(
  File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/sql/functions.py", line 978, in __call__
    return Function(
           ^^^^^^^^^
TypeError: Function.__init__() got an unexpected keyword argument 'else_'
INFO:     ********:40780 - "GET /api/v1/shareholders/1 HTTP/1.1" 200 OK
INFO:     ********:48852 - "GET /api/v1/shareholder-investment-records/?shareholder_id=1 HTTP/1.1" 200 OK
INFO:     ********:39896 - "GET /docs HTTP/1.1" 200 OK
/root/vip/backend/venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     ********:39896 - "GET /api/v1/openapi.json HTTP/1.1" 200 OK
INFO:     ********:45310 - "OPTIONS /api/v1/shareholders/1 HTTP/1.1" 200 OK
INFO:     ********:45324 - "OPTIONS /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:45360 - "OPTIONS /api/v1/shareholder-investment-records/shareholder/1/summary HTTP/1.1" 200 OK
INFO:     ********:45340 - "OPTIONS /api/v1/shareholder-investment-records/?shareholder_id=1 HTTP/1.1" 200 OK
INFO:     ********:45346 - "OPTIONS /api/v1/shareholders/1 HTTP/1.1" 200 OK
INFO:     ********:45376 - "OPTIONS /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:45360 - "OPTIONS /api/v1/shareholder-investment-records/?shareholder_id=1 HTTP/1.1" 200 OK
INFO:     ********:45310 - "OPTIONS /api/v1/shareholder-investment-records/shareholder/1/summary HTTP/1.1" 200 OK
INFO:     ********:45346 - "GET /api/v1/shareholder-investment-records/shareholder/1/summary HTTP/1.1" 500 Internal Server Error
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/root/vip/backend/venv/lib/python3.12/site-packages/uvicorn/protocols/http/h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 75, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/fastapi/routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/fastapi/routing.py", line 215, in run_endpoint_function
    return await run_in_threadpool(dependant.call, **values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/concurrency.py", line 38, in run_in_threadpool
    return await anyio.to_thread.run_sync(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/anyio/to_thread.py", line 56, in run_sync
    return await get_async_backend().run_sync_in_worker_thread(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/anyio/_backends/_asyncio.py", line 2470, in run_sync_in_worker_thread
    return await future
           ^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/anyio/_backends/_asyncio.py", line 967, in run
    result = context.run(func, *args)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/app/api/api_v1/endpoints/shareholder_investment_records.py", line 214, in get_shareholder_investment_summary
    func.case(
  File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/sql/functions.py", line 978, in __call__
    return Function(
           ^^^^^^^^^
TypeError: Function.__init__() got an unexpected keyword argument 'else_'
INFO:     ********:45376 - "GET /api/v1/shareholder-investment-records/?shareholder_id=1 HTTP/1.1" 200 OK
INFO:     ********:45324 - "GET /api/v1/shareholders/1 HTTP/1.1" 200 OK
INFO:     ********:45386 - "GET /api/v1/shareholder-investment-records/shareholder/1/summary HTTP/1.1" 500 Internal Server Error
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/root/vip/backend/venv/lib/python3.12/site-packages/uvicorn/protocols/http/h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 75, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/fastapi/routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/fastapi/routing.py", line 215, in run_endpoint_function
    return await run_in_threadpool(dependant.call, **values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/concurrency.py", line 38, in run_in_threadpool
    return await anyio.to_thread.run_sync(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/anyio/to_thread.py", line 56, in run_sync
    return await get_async_backend().run_sync_in_worker_thread(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/anyio/_backends/_asyncio.py", line 2470, in run_sync_in_worker_thread
    return await future
           ^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/anyio/_backends/_asyncio.py", line 967, in run
    result = context.run(func, *args)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/app/api/api_v1/endpoints/shareholder_investment_records.py", line 214, in get_shareholder_investment_summary
    func.case(
  File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/sql/functions.py", line 978, in __call__
    return Function(
           ^^^^^^^^^
TypeError: Function.__init__() got an unexpected keyword argument 'else_'
INFO:     ********:45360 - "GET /api/v1/shareholder-investment-records/?shareholder_id=1 HTTP/1.1" 200 OK
INFO:     ********:45340 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:45310 - "GET /api/v1/shareholders/1 HTTP/1.1" 200 OK
INFO:     ********:45376 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:41486 - "OPTIONS /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:41500 - "OPTIONS /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:41514 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:41486 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:41500 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:41514 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:41530 - "OPTIONS /api/v1/employees/2 HTTP/1.1" 200 OK
INFO:     ********:51994 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:41530 - "GET /api/v1/employees/2 HTTP/1.1" 200 OK
INFO:     ********:41530 - "OPTIONS /api/v1/stores/1 HTTP/1.1" 200 OK
INFO:     ********:51994 - "GET /api/v1/stores/1 HTTP/1.1" 200 OK
INFO:     ********:52006 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:50484 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:40790 - "OPTIONS /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:40802 - "OPTIONS /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:40790 - "GET /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:40802 - "GET /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:40816 - "GET /api/v1/stores/1 HTTP/1.1" 200 OK
INFO:     ********:39872 - "GET /api/v1/stores/1 HTTP/1.1" 200 OK
INFO:     ********:39872 - "GET /api/v1/stores/1 HTTP/1.1" 200 OK
INFO:     ********:37778 - "OPTIONS /api/v1/stores/1 HTTP/1.1" 200 OK
INFO:     ********:37778 - "PUT /api/v1/stores/1 HTTP/1.1" 200 OK
INFO:     ********:37778 - "GET /api/v1/stores/1 HTTP/1.1" 200 OK
