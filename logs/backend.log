INFO:     Will watch for changes in these directories: ['/root/vip/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [62748] using StatReload
/root/vip/backend/venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
2025-07-28 19:47:55,061 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:47:55,829 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:47:55,830 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:47:57,497 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:47:57,499 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 19:47:57,500 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 19:47:57,500 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 19:47:57,500 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
INFO:     Started server process [62767]
INFO:     Waiting for application startup.
2025-07-28 19:47:58,224 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 19:47:58,224 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 19:47:58,224 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 19:47:58,225 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 19:47:58,235 - app.services.monitoring_service - INFO - 监控系统已初始化
INFO:     Application startup complete.
2025-07-28 19:47:59,288 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 1.06秒
INFO:     ********:53794 - "OPTIONS /api/v1/member-levels HTTP/1.1" 200 OK
INFO:     ********:53796 - "OPTIONS /api/v1/member-cards?page=1&size=10&card_number=&customer_name=&level_id=&status= HTTP/1.1" 200 OK
INFO:     ********:53810 - "OPTIONS /api/v1/member-levels HTTP/1.1" 200 OK
INFO:     ********:53818 - "OPTIONS /api/v1/member-cards?page=1&size=10&card_number=&customer_name=&level_id=&status= HTTP/1.1" 200 OK
API参数: page=1, size=10, skip=0, limit=10
搜索参数: card_number=None, customer_name=None, level_id=None, status=None, customer_id=None
INFO:     ********:53794 - "GET /api/v1/member-levels HTTP/1.1" 200 OK
查询结果: total=0, items_count=0
INFO:     ********:53796 - "GET /api/v1/member-cards?page=1&size=10&card_number=&customer_name=&level_id=&status= HTTP/1.1" 200 OK
API参数: page=1, size=10, skip=0, limit=10
搜索参数: card_number=None, customer_name=None, level_id=None, status=None, customer_id=None
查询结果: total=0, items_count=0
INFO:     ********:53794 - "GET /api/v1/member-cards?page=1&size=10&card_number=&customer_name=&level_id=&status= HTTP/1.1" 200 OK
INFO:     ********:53810 - "GET /api/v1/member-levels HTTP/1.1" 200 OK
2025-07-28 19:49:20,817 - app.middleware.rate_limit_middleware - WARNING - 自适应限流: ip:********, 动态限制: 6, 负载因子: 1.20
INFO:     ********:53822 - "OPTIONS /api/v1/auth/login HTTP/1.1" 429 Too Many Requests
INFO:     ********:53184 - "OPTIONS /api/v1/auth/login HTTP/1.1" 200 OK
2025-07-28 19:50:50,948 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/root/vip/backend/venv/lib/python3.12/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
INFO:     ********:53184 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
INFO:     ********:53184 - "OPTIONS /api/v1/auth/me HTTP/1.1" 200 OK
INFO:     ********:53186 - "GET /api/v1/auth/me HTTP/1.1" 200 OK
INFO:     ********:53186 - "OPTIONS /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ********:53184 - "OPTIONS /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ********:53192 - "OPTIONS /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ********:53200 - "OPTIONS /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ********:53212 - "OPTIONS /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ********:53218 - "OPTIONS /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ********:53186 - "OPTIONS /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ********:53184 - "OPTIONS /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ********:53192 - "OPTIONS /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ********:53200 - "OPTIONS /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ********:53212 - "GET /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ********:53186 - "GET /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ********:53192 - "GET /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ********:53218 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ********:53212 - "GET /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ********:53186 - "GET /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ********:53184 - "GET /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ********:53200 - "GET /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ********:53192 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ********:53218 - "GET /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
API参数: page=1, size=10, skip=0, limit=10
搜索参数: card_number=None, customer_name=None, level_id=None, status=None, customer_id=None
查询结果: total=0, items_count=0
INFO:     ********:37320 - "GET /api/v1/member-cards?page=1&size=10&card_number=&customer_name=&level_id=&status= HTTP/1.1" 200 OK
INFO:     ********:37326 - "GET /api/v1/member-levels HTTP/1.1" 200 OK
API参数: page=1, size=10, skip=0, limit=10
搜索参数: card_number=None, customer_name=None, level_id=None, status=None, customer_id=None
查询结果: total=0, items_count=0
INFO:     ********:52720 - "GET /api/v1/member-cards?page=1&size=10&card_number=&customer_name=&level_id=&status= HTTP/1.1" 200 OK
INFO:     ********:36382 - "OPTIONS /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:36398 - "OPTIONS /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:36382 - "GET /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:36398 - "GET /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:42450 - "OPTIONS /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:42450 - "POST /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:42450 - "GET /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:36150 - "OPTIONS /api/v1/stores/4 HTTP/1.1" 200 OK
INFO:     ********:36150 - "PUT /api/v1/stores/4 HTTP/1.1" 200 OK
INFO:     ********:36150 - "GET /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:36152 - "GET /api/v1/stores/4 HTTP/1.1" 200 OK
INFO:     ********:35028 - "GET /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:36820 - "OPTIONS /api/v1/stores/?skip=0&limit=10&search=%E6%B5%8B%E8%AF%95 HTTP/1.1" 200 OK
INFO:     ********:36820 - "GET /api/v1/stores/?skip=0&limit=10&search=%E6%B5%8B%E8%AF%95 HTTP/1.1" 200 OK
INFO:     ********:36836 - "GET /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:44050 - "OPTIONS /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:44060 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:44050 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:54084 - "OPTIONS /api/v1/employees/ HTTP/1.1" 200 OK
INFO:     ********:54084 - "POST /api/v1/employees/ HTTP/1.1" 201 Created
INFO:     ********:54084 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
