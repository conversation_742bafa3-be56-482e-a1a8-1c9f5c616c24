INFO:     Will watch for changes in these directories: ['/root/vip/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [85019] using StatReload
/root/vip/backend/venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
2025-07-28 21:00:47,715 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 21:00:48,537 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 21:00:48,538 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 21:00:50,209 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 21:00:50,211 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 21:00:50,211 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 21:00:50,211 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 21:00:50,212 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
INFO:     Started server process [85035]
INFO:     Waiting for application startup.
2025-07-28 21:00:50,942 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 21:00:50,942 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 21:00:50,942 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 21:00:50,942 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 21:00:50,953 - app.services.monitoring_service - INFO - 监控系统已初始化
INFO:     Application startup complete.
2025-07-28 21:00:51,857 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 0.91秒
INFO:     ********:60838 - "POST /api/v1/customers HTTP/1.1" 307 Temporary Redirect
INFO:     ********:60838 - "POST /api/v1/customers/ HTTP/1.1" 200 OK
INFO:     ********:60838 - "OPTIONS /api/v1/customers?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:60852 - "GET /api/v1/customers?params[skip]=0&params[limit]=20 HTTP/1.1" 307 Temporary Redirect
INFO:     ********:60852 - "OPTIONS /api/v1/customers/?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:60838 - "GET /api/v1/customers/?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:49066 - "OPTIONS /api/v1/customers/122 HTTP/1.1" 200 OK
INFO:     ********:49066 - "PUT /api/v1/customers/122 HTTP/1.1" 200 OK
INFO:     ********:49066 - "GET /api/v1/customers?params[skip]=0&params[limit]=20 HTTP/1.1" 307 Temporary Redirect
INFO:     ********:49066 - "GET /api/v1/customers/?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:39452 - "DELETE /api/v1/customers/122 HTTP/1.1" 200 OK
INFO:     ********:39452 - "GET /api/v1/customers?params[skip]=0&params[limit]=20 HTTP/1.1" 307 Temporary Redirect
INFO:     ********:39452 - "GET /api/v1/customers/?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:41494 - "OPTIONS /api/v1/customers?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:41496 - "OPTIONS /api/v1/customers?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:41494 - "GET /api/v1/customers?params[skip]=0&params[limit]=20 HTTP/1.1" 307 Temporary Redirect
INFO:     ********:41494 - "GET /api/v1/customers?params[skip]=0&params[limit]=20 HTTP/1.1" 307 Temporary Redirect
INFO:     ********:41494 - "OPTIONS /api/v1/customers/?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:41496 - "OPTIONS /api/v1/customers/?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:41510 - "GET /api/v1/customers/?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:41496 - "GET /api/v1/customers/?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:52168 - "OPTIONS /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:52148 - "OPTIONS /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:52160 - "OPTIONS /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:52178 - "OPTIONS /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:52168 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:52178 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:52148 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:52168 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:45606 - "OPTIONS /api/v1/employees/ HTTP/1.1" 200 OK
2025-07-28 21:29:10,181 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/root/vip/backend/venv/lib/python3.12/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
INFO:     ********:45606 - "POST /api/v1/employees/ HTTP/1.1" 201 Created
INFO:     ********:45606 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
2025-07-28 21:30:51,869 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 21:30:52,429 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 0.56秒
INFO:     ********:40210 - "OPTIONS /api/v1/employees/5 HTTP/1.1" 200 OK
INFO:     ********:40210 - "PUT /api/v1/employees/5 HTTP/1.1" 200 OK
INFO:     ********:40210 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:40216 - "DELETE /api/v1/employees/5 HTTP/1.1" 200 OK
INFO:     ********:40216 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:43430 - "OPTIONS /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:43436 - "OPTIONS /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:43430 - "GET /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:43436 - "GET /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:58470 - "GET /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:58470 - "GET /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:34676 - "OPTIONS /api/v1/shareholders/ HTTP/1.1" 200 OK
INFO:     ********:34676 - "POST /api/v1/shareholders/ HTTP/1.1" 200 OK
INFO:     ********:34676 - "GET /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:34678 - "OPTIONS /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ********:56214 - "OPTIONS /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ********:56236 - "OPTIONS /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ********:56230 - "OPTIONS /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ********:56234 - "OPTIONS /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ********:56242 - "OPTIONS /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ********:34678 - "OPTIONS /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ********:56214 - "OPTIONS /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ********:56236 - "OPTIONS /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ********:56230 - "OPTIONS /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ********:56242 - "GET /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ********:56234 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ********:56214 - "GET /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ********:56230 - "GET /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ********:56236 - "GET /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ********:34678 - "GET /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ********:56214 - "GET /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ********:56234 - "GET /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ********:56242 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ********:56230 - "GET /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
