INFO:     Will watch for changes in these directories: ['/root/vip/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [85019] using StatReload
/root/vip/backend/venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
2025-07-28 21:00:47,715 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 21:00:48,537 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 21:00:48,538 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 21:00:50,209 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 21:00:50,211 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 21:00:50,211 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 21:00:50,211 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 21:00:50,212 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
INFO:     Started server process [85035]
INFO:     Waiting for application startup.
2025-07-28 21:00:50,942 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 21:00:50,942 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 21:00:50,942 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 21:00:50,942 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 21:00:50,953 - app.services.monitoring_service - INFO - 监控系统已初始化
INFO:     Application startup complete.
2025-07-28 21:00:51,857 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 0.91秒
INFO:     ********:60838 - "POST /api/v1/customers HTTP/1.1" 307 Temporary Redirect
INFO:     ********:60838 - "POST /api/v1/customers/ HTTP/1.1" 200 OK
INFO:     ********:60838 - "OPTIONS /api/v1/customers?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:60852 - "GET /api/v1/customers?params[skip]=0&params[limit]=20 HTTP/1.1" 307 Temporary Redirect
INFO:     ********:60852 - "OPTIONS /api/v1/customers/?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:60838 - "GET /api/v1/customers/?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:49066 - "OPTIONS /api/v1/customers/122 HTTP/1.1" 200 OK
INFO:     ********:49066 - "PUT /api/v1/customers/122 HTTP/1.1" 200 OK
INFO:     ********:49066 - "GET /api/v1/customers?params[skip]=0&params[limit]=20 HTTP/1.1" 307 Temporary Redirect
INFO:     ********:49066 - "GET /api/v1/customers/?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:39452 - "DELETE /api/v1/customers/122 HTTP/1.1" 200 OK
INFO:     ********:39452 - "GET /api/v1/customers?params[skip]=0&params[limit]=20 HTTP/1.1" 307 Temporary Redirect
INFO:     ********:39452 - "GET /api/v1/customers/?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:41494 - "OPTIONS /api/v1/customers?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:41496 - "OPTIONS /api/v1/customers?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:41494 - "GET /api/v1/customers?params[skip]=0&params[limit]=20 HTTP/1.1" 307 Temporary Redirect
INFO:     ********:41494 - "GET /api/v1/customers?params[skip]=0&params[limit]=20 HTTP/1.1" 307 Temporary Redirect
INFO:     ********:41494 - "OPTIONS /api/v1/customers/?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:41496 - "OPTIONS /api/v1/customers/?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:41510 - "GET /api/v1/customers/?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:41496 - "GET /api/v1/customers/?params[skip]=0&params[limit]=20 HTTP/1.1" 200 OK
INFO:     ********:52168 - "OPTIONS /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:52148 - "OPTIONS /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:52160 - "OPTIONS /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:52178 - "OPTIONS /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:52168 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:52178 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:52148 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:52168 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:45606 - "OPTIONS /api/v1/employees/ HTTP/1.1" 200 OK
2025-07-28 21:29:10,181 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/root/vip/backend/venv/lib/python3.12/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
INFO:     ********:45606 - "POST /api/v1/employees/ HTTP/1.1" 201 Created
INFO:     ********:45606 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
2025-07-28 21:30:51,869 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 21:30:52,429 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 0.56秒
INFO:     ********:40210 - "OPTIONS /api/v1/employees/5 HTTP/1.1" 200 OK
INFO:     ********:40210 - "PUT /api/v1/employees/5 HTTP/1.1" 200 OK
INFO:     ********:40210 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:40216 - "DELETE /api/v1/employees/5 HTTP/1.1" 200 OK
INFO:     ********:40216 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:43430 - "OPTIONS /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:43436 - "OPTIONS /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:43430 - "GET /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:43436 - "GET /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:58470 - "GET /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:58470 - "GET /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:34676 - "OPTIONS /api/v1/shareholders/ HTTP/1.1" 200 OK
INFO:     ********:34676 - "POST /api/v1/shareholders/ HTTP/1.1" 200 OK
INFO:     ********:34676 - "GET /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:34678 - "OPTIONS /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ********:56214 - "OPTIONS /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ********:56236 - "OPTIONS /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ********:56230 - "OPTIONS /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ********:56234 - "OPTIONS /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ********:56242 - "OPTIONS /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ********:34678 - "OPTIONS /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ********:56214 - "OPTIONS /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ********:56236 - "OPTIONS /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ********:56230 - "OPTIONS /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ********:56242 - "GET /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ********:56234 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ********:56214 - "GET /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ********:56230 - "GET /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ********:56236 - "GET /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ********:34678 - "GET /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ********:56214 - "GET /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ********:56234 - "GET /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ********:56242 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ********:56230 - "GET /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ********:46920 - "OPTIONS /api/v1/group-items/?page=1&size=10 HTTP/1.1" 200 OK
INFO:     ********:46920 - "GET /api/v1/group-items/?page=1&size=10 HTTP/1.1" 200 OK
INFO:     ********:46924 - "GET /api/v1/group-items/?page=1&size=10 HTTP/1.1" 200 OK
INFO:     ********:59686 - "OPTIONS /api/v1/group-items/ HTTP/1.1" 200 OK
INFO:     ********:59686 - "POST /api/v1/group-items/ HTTP/1.1" 201 Created
INFO:     ********:59686 - "GET /api/v1/group-items/?page=1&size=10 HTTP/1.1" 200 OK
INFO:     ********:59702 - "GET /api/v1/group-items/?page=1&size=10 HTTP/1.1" 200 OK
INFO:     ********:47578 - "GET /api/v1/group-items/?page=1&size=10 HTTP/1.1" 200 OK
INFO:     ********:47578 - "GET /api/v1/group-items/?page=1&size=10 HTTP/1.1" 200 OK
2025-07-28 22:00:52,441 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 22:00:52,997 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 0.56秒
INFO:     ********:52742 - "OPTIONS /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:52756 - "OPTIONS /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:52742 - "GET /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:52756 - "GET /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:52772 - "OPTIONS /api/v1/shareholders/1 HTTP/1.1" 200 OK
INFO:     ********:53940 - "OPTIONS /api/v1/shareholder-structures/?shareholder_id=1&page=1&size=10 HTTP/1.1" 200 OK
INFO:     ********:52772 - "GET /api/v1/shareholders/1 HTTP/1.1" 200 OK
INFO:     ********:53946 - "GET /api/v1/shareholder-structures/?shareholder_id=1&page=1&size=10 HTTP/1.1" 200 OK
INFO:     ********:50464 - "OPTIONS /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:50480 - "GET /api/v1/shareholder-structures/?shareholder_id=1&page=1&size=10 HTTP/1.1" 200 OK
INFO:     ********:50466 - "GET /api/v1/shareholders/1 HTTP/1.1" 200 OK
INFO:     ********:50464 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:60512 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:50484 - "GET /api/v1/shareholders/1 HTTP/1.1" 200 OK
INFO:     ********:60514 - "GET /api/v1/shareholder-structures/?shareholder_id=1&page=1&size=10 HTTP/1.1" 200 OK
INFO:     ********:60512 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:50484 - "GET /api/v1/shareholder-structures/?shareholder_id=1&page=1&size=10 HTTP/1.1" 200 OK
INFO:     ********:60516 - "GET /api/v1/shareholders/1 HTTP/1.1" 200 OK
INFO:     ********:34316 - "GET /api/v1/shareholder-structures/?shareholder_id=1&page=1&size=10 HTTP/1.1" 200 OK
INFO:     ********:34322 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:34302 - "GET /api/v1/shareholders/1 HTTP/1.1" 200 OK
INFO:     ********:34316 - "GET /api/v1/shareholder-structures/?shareholder_id=1&page=1&size=10 HTTP/1.1" 200 OK
INFO:     ********:34324 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:34322 - "GET /api/v1/shareholders/1 HTTP/1.1" 200 OK
INFO:     ********:49484 - "OPTIONS /api/v1/group-items/?page=1&size=10 HTTP/1.1" 200 OK
INFO:     ********:49488 - "OPTIONS /api/v1/group-items/?page=1&size=10 HTTP/1.1" 200 OK
INFO:     ********:49484 - "GET /api/v1/group-items/?page=1&size=10 HTTP/1.1" 200 OK
INFO:     ********:49488 - "GET /api/v1/group-items/?page=1&size=10 HTTP/1.1" 200 OK
INFO:     ********:42788 - "OPTIONS /api/v1/group-items/ HTTP/1.1" 200 OK
INFO:     ********:42788 - "POST /api/v1/group-items/ HTTP/1.1" 201 Created
INFO:     ********:42788 - "GET /api/v1/group-items/?page=1&size=10 HTTP/1.1" 200 OK
INFO:     ********:36378 - "OPTIONS /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:36364 - "OPTIONS /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:36394 - "OPTIONS /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:36370 - "OPTIONS /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:36378 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:36364 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:36370 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:36394 - "GET /api/v1/employees/?skip=0&limit=10 HTTP/1.1" 200 OK
2025-07-28 22:30:53,011 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 22:30:53,591 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 0.58秒
INFO:     ********:46580 - "OPTIONS /api/v1/employees/2 HTTP/1.1" 200 OK
INFO:     ********:46594 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:46580 - "GET /api/v1/employees/2 HTTP/1.1" 200 OK
INFO:     ********:46580 - "OPTIONS /api/v1/stores/1 HTTP/1.1" 200 OK
INFO:     ********:46594 - "GET /api/v1/stores/1 HTTP/1.1" 200 OK
INFO:     ********:52620 - "OPTIONS /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:52626 - "OPTIONS /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:52620 - "GET /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:52626 - "GET /api/v1/stores/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:37082 - "GET /api/v1/stores/1 HTTP/1.1" 200 OK
INFO:     ********:57204 - "OPTIONS /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ********:57208 - "OPTIONS /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ********:57220 - "OPTIONS /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ********:57218 - "OPTIONS /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ********:57228 - "OPTIONS /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ********:57240 - "OPTIONS /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ********:57204 - "OPTIONS /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ********:57220 - "OPTIONS /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ********:57208 - "OPTIONS /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ********:57218 - "OPTIONS /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ********:57240 - "GET /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ********:57228 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ********:57204 - "GET /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ********:57218 - "GET /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ********:57208 - "GET /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     ********:57228 - "GET /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     ********:57240 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     ********:57220 - "GET /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ********:57204 - "GET /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     ********:57218 - "GET /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     ********:42226 - "OPTIONS /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:42232 - "OPTIONS /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:42226 - "GET /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:42232 - "GET /api/v1/shareholders/?skip=0&limit=10 HTTP/1.1" 200 OK
INFO:     ********:55220 - "OPTIONS /api/v1/shareholders/1 HTTP/1.1" 200 OK
INFO:     ********:55236 - "OPTIONS /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:55254 - "OPTIONS /api/v1/shareholder-investment-records/?shareholder_id=1 HTTP/1.1" 200 OK
INFO:     ********:55240 - "OPTIONS /api/v1/shareholder-investment-records/shareholder/1/summary HTTP/1.1" 200 OK
INFO:     ********:55236 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
INFO:     ********:55220 - "GET /api/v1/shareholders/1 HTTP/1.1" 200 OK
INFO:     ********:55254 - "GET /api/v1/shareholder-investment-records/?shareholder_id=1 HTTP/1.1" 200 OK
INFO:     ********:55256 - "GET /api/v1/shareholder-investment-records/shareholder/1/summary HTTP/1.1" 500 Internal Server Error
ERROR:    Exception in ASGI application
  + Exception Group Traceback (most recent call last):
  |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |   File "/root/vip/backend/venv/lib/python3.12/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/uvicorn/protocols/http/h11_impl.py", line 403, in run_asgi
    |     result = await app(  # type: ignore[func-returns-value]
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    |     return await self.app(scope, receive, send)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |   File "/usr/lib/python3.12/contextlib.py", line 158, in __exit__
    |     self.gen.throw(value)
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/root/vip/backend/app/middleware/rate_limit_middleware.py", line 430, in dispatch
    |     response = await call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |   File "/usr/lib/python3.12/contextlib.py", line 158, in __exit__
    |     self.gen.throw(value)
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/root/vip/backend/app/middleware/access_pattern_middleware.py", line 170, in dispatch
    |     response = await call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |   File "/usr/lib/python3.12/contextlib.py", line 158, in __exit__
    |     self.gen.throw(value)
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/root/vip/backend/app/middleware/access_pattern_middleware.py", line 141, in dispatch
    |     response = await call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 159, in call_next
    |     raise app_exc
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 93, in __call__
    |     await self.simple_response(scope, receive, send, request_headers=headers)
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    |     await self.app(scope, receive, send)
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    |     await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 716, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 736, in app
    |     await route.handle(scope, receive, send)
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 290, in handle
    |     await self.app(scope, receive, send)
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 78, in app
    |     await wrap_app_handling_exceptions(app, request)(scope, receive, send)
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 75, in app
    |     response = await f(request)
    |                ^^^^^^^^^^^^^^^^
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/fastapi/routing.py", line 302, in app
    |     raw_response = await run_endpoint_function(
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/fastapi/routing.py", line 215, in run_endpoint_function
    |     return await run_in_threadpool(dependant.call, **values)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/concurrency.py", line 38, in run_in_threadpool
    |     return await anyio.to_thread.run_sync(func)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/anyio/to_thread.py", line 56, in run_sync
    |     return await get_async_backend().run_sync_in_worker_thread(
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/anyio/_backends/_asyncio.py", line 2470, in run_sync_in_worker_thread
    |     return await future
    |            ^^^^^^^^^^^^
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/anyio/_backends/_asyncio.py", line 967, in run
    |     result = context.run(func, *args)
    |              ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/root/vip/backend/app/api/api_v1/endpoints/shareholder_investment_records.py", line 214, in get_shareholder_investment_summary
    |     func.case(
    |   File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/sql/functions.py", line 978, in __call__
    |     return Function(
    |            ^^^^^^^^^
    | TypeError: Function.__init__() got an unexpected keyword argument 'else_'
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/root/vip/backend/venv/lib/python3.12/site-packages/uvicorn/protocols/http/h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "/usr/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/app/middleware/rate_limit_middleware.py", line 430, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "/usr/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/app/middleware/access_pattern_middleware.py", line 170, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "/usr/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/app/middleware/access_pattern_middleware.py", line 141, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 159, in call_next
    raise app_exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/routing.py", line 75, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/fastapi/routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/fastapi/routing.py", line 215, in run_endpoint_function
    return await run_in_threadpool(dependant.call, **values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/starlette/concurrency.py", line 38, in run_in_threadpool
    return await anyio.to_thread.run_sync(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/anyio/to_thread.py", line 56, in run_sync
    return await get_async_backend().run_sync_in_worker_thread(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/anyio/_backends/_asyncio.py", line 2470, in run_sync_in_worker_thread
    return await future
           ^^^^^^^^^^^^
  File "/root/vip/backend/venv/lib/python3.12/site-packages/anyio/_backends/_asyncio.py", line 967, in run
    result = context.run(func, *args)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/vip/backend/app/api/api_v1/endpoints/shareholder_investment_records.py", line 214, in get_shareholder_investment_summary
    func.case(
  File "/root/vip/backend/venv/lib/python3.12/site-packages/sqlalchemy/sql/functions.py", line 978, in __call__
    return Function(
           ^^^^^^^^^
TypeError: Function.__init__() got an unexpected keyword argument 'else_'
2025-07-28 23:00:53,603 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 23:00:54,671 - app.services.data_preloader - INFO - 预加载完成: 成功 42, 失败 0, 耗时 1.07秒
