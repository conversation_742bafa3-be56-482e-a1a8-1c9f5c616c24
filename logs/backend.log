INFO:     Will watch for changes in these directories: ['/root/vip/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [36110] using StatReload
/root/vip/backend/venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
2025-07-28 15:34:54,350 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:34:55,125 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:34:55,126 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:34:56,738 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:34:56,740 - app.middleware.compression_middleware - INFO - 智能压缩中间件已设置
2025-07-28 15:34:56,740 - app.middleware.compression_middleware - INFO - 压缩配置: 最小大小=300字节, 压缩级别=6
2025-07-28 15:34:56,741 - app.middleware.access_pattern_middleware - INFO - 访问模式跟踪中间件已设置
2025-07-28 15:34:56,742 - app.utils.redis_cache - INFO - Redis缓存连接成功
2025-07-28 15:34:56,742 - app.middleware.rate_limit_middleware - INFO - 自适应限流中间件已设置
2025-07-28 15:34:56,742 - app.middleware.rate_limit_middleware - INFO - 限流配置: 默认限制=100/分钟, 窗口=60秒
INFO:     Started server process [36123]
INFO:     Waiting for application startup.
2025-07-28 15:34:57,461 - app.services.data_preloader - INFO - 预加载调度器已启动
2025-07-28 15:34:57,462 - app.services.data_preloader - INFO - 数据预加载线程已启动
2025-07-28 15:34:57,462 - app.services.data_preloader - INFO - 开始预加载BI数据...
2025-07-28 15:34:57,462 - app.services.monitoring_service - INFO - 系统监控已启动
2025-07-28 15:34:57,473 - app.services.monitoring_service - INFO - 监控系统已初始化
INFO:     Application startup complete.
2025-07-28 15:34:58,509 - app.services.data_preloader - INFO - 预加载完成: 成功 39, 失败 0, 耗时 1.05秒
INFO:     10.7.7.10:54527 - "GET /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     10.7.7.10:54526 - "GET /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     10.7.7.10:54521 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     10.7.7.10:54525 - "GET /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     10.7.7.10:54524 - "GET /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
INFO:     10.7.7.10:54956 - "GET /api/v1/dashboard/services/distribution HTTP/1.1" 200 OK
INFO:     10.7.7.10:54955 - "GET /api/v1/dashboard/tasks/pending HTTP/1.1" 200 OK
INFO:     10.7.7.10:54954 - "GET /api/v1/dashboard/appointments/today HTTP/1.1" 200 OK
INFO:     10.7.7.10:54949 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
INFO:     10.7.7.10:54957 - "GET /api/v1/dashboard/revenue/trend?period=week HTTP/1.1" 200 OK
