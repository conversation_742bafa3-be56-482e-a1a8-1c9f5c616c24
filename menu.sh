#!/bin/bash

# 按摩推拿连锁门店管理系统 - 多级管理菜单
# 版本: v2.0 - 简化多级选择版

# 配置变量
SCRIPT_DIR="/root/vip"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 打印标题
print_title() {
    clear
    echo -e "${CYAN}"
    echo "=================================================="
    echo "        按摩推拿连锁门店管理系统 - 管理菜单"
    echo "=================================================="
    echo -e "${NC}"
}

# 显示主菜单
show_main_menu() {
    print_title
    echo -e "${WHITE}请选择功能模块：${NC}"
    echo ""
    echo -e "${GREEN}1.${NC} 服务管理"
    echo -e "${GREEN}2.${NC} 系统监控"
    echo -e "${GREEN}3.${NC} 系统维护"
    echo -e "${GREEN}4.${NC} 快速访问"
    echo -e "${GREEN}5.${NC} 项目状态"
    echo ""
    echo -e "${RED}0.${NC} 退出程序"
    echo ""
    echo -e "${CYAN}=================================================="
    echo -e "${NC}"
}

# 显示服务管理子菜单
show_service_menu() {
    print_title
    echo -e "${WHITE}服务管理：${NC}"
    echo ""
    echo -e "${GREEN}1.${NC} 启动所有服务"
    echo -e "${GREEN}2.${NC} 启动前端服务"
    echo -e "${GREEN}3.${NC} 启动后端服务"
    echo -e "${GREEN}4.${NC} 停止所有服务"
    echo -e "${GREEN}5.${NC} 停止前端服务"
    echo -e "${GREEN}6.${NC} 停止后端服务"
    echo -e "${GREEN}7.${NC} 重启所有服务"
    echo ""
    echo -e "${YELLOW}8.${NC} 返回主菜单"
    echo -e "${RED}0.${NC} 退出程序"
    echo ""
}

# 显示系统监控子菜单
show_monitor_menu() {
    print_title
    echo -e "${WHITE}系统监控：${NC}"
    echo ""
    echo -e "${GREEN}1.${NC} 查看服务状态"
    echo -e "${GREEN}2.${NC} 查看前端日志"
    echo -e "${GREEN}3.${NC} 查看后端日志"
    echo -e "${GREEN}4.${NC} 实时监控前端日志"
    echo -e "${GREEN}5.${NC} 实时监控后端日志"
    echo ""
    echo -e "${YELLOW}6.${NC} 返回主菜单"
    echo -e "${RED}0.${NC} 退出程序"
    echo ""
}

# 显示系统维护子菜单
show_maintenance_menu() {
    print_title
    echo -e "${WHITE}系统维护：${NC}"
    echo ""
    echo -e "${GREEN}1.${NC} 检查系统依赖"
    echo -e "${GREEN}2.${NC} 清理僵尸进程"
    echo -e "${GREEN}3.${NC} 备份项目"
    echo -e "${GREEN}4.${NC} 查看帮助"
    echo ""
    echo -e "${YELLOW}5.${NC} 返回主菜单"
    echo -e "${RED}0.${NC} 退出程序"
    echo ""
}

# 显示快速访问子菜单
show_access_menu() {
    print_title
    echo -e "${WHITE}快速访问：${NC}"
    echo ""
    echo -e "${GREEN}1.${NC} 打开前端界面"
    echo -e "${GREEN}2.${NC} 打开API文档"
    echo -e "${GREEN}3.${NC} 打开数据库管理"
    echo ""
    echo -e "${YELLOW}4.${NC} 返回主菜单"
    echo -e "${RED}0.${NC} 退出程序"
    echo ""
}

# 执行命令
execute_command() {
    local command=$1
    local description=$2
    
    print_message $BLUE "正在执行: $description"
    echo ""
    
    cd "$SCRIPT_DIR"
    eval "$command"
    
    echo ""
    print_message $YELLOW "按任意键继续..."
    read -n 1 -s
}

# 实时日志监控
monitor_logs() {
    local service=$1
    local service_name=$2
    
    print_message $BLUE "正在启动 $service_name 实时日志监控..."
    print_message $YELLOW "按 Ctrl+C 返回菜单"
    echo ""
    
    cd "$SCRIPT_DIR"
    ./manage.sh tail "$service"
}

# 打开浏览器
open_browser() {
    local url=$1
    local description=$2
    
    print_message $BLUE "正在打开 $description..."
    print_message $GREEN "URL: $url"
    
    if command -v xdg-open >/dev/null 2>&1; then
        xdg-open "$url" >/dev/null 2>&1 &
    elif command -v firefox >/dev/null 2>&1; then
        firefox "$url" >/dev/null 2>&1 &
    elif command -v google-chrome >/dev/null 2>&1; then
        google-chrome "$url" >/dev/null 2>&1 &
    else
        print_message $YELLOW "未找到浏览器，请手动访问: $url"
    fi
    
    echo ""
    print_message $YELLOW "按任意键继续..."
    read -n 1 -s
}

# 显示项目状态
show_project_status() {
    print_title
    echo -e "${WHITE}项目开发现状：${NC}"
    echo ""
    
    cd "$SCRIPT_DIR"
    
    # 检查后端状态
    if ./manage.sh status | grep -q "后端服务.*✅"; then
        echo -e "${GREEN}✅ 后端服务: 运行中${NC}"
    else
        echo -e "${RED}❌ 后端服务: 未运行${NC}"
    fi
    
    # 检查前端状态
    if ./manage.sh status | grep -q "前端服务.*✅"; then
        echo -e "${GREEN}✅ 前端服务: 运行中${NC}"
    else
        echo -e "${RED}❌ 前端服务: 未运行${NC}"
    fi
    
    echo ""
    echo -e "${CYAN}功能实现状态：${NC}"
    echo -e "${GREEN}✅ 数据库设计: 已完成${NC}"
    echo -e "${GREEN}✅ 后端API: 已完成${NC}"
    echo -e "${YELLOW}⚠️  前端界面: 部分完成${NC}"
    echo -e "${YELLOW}⚠️  系统集成: 进行中${NC}"
    
    echo ""
    print_message $YELLOW "按任意键返回主菜单..."
    read -n 1 -s
}

# 处理服务管理选择
handle_service_choice() {
    local choice=$1
    
    case $choice in
        1) execute_command "./manage.sh start all" "启动所有服务" ;;
        2) execute_command "./manage.sh start frontend" "启动前端服务" ;;
        3) execute_command "./manage.sh start backend" "启动后端服务" ;;
        4) execute_command "./manage.sh stop all" "停止所有服务" ;;
        5) execute_command "./manage.sh stop frontend" "停止前端服务" ;;
        6) execute_command "./manage.sh stop backend" "停止后端服务" ;;
        7) execute_command "./manage.sh restart all" "重启所有服务" ;;
        8) return 0 ;;
        0) exit 0 ;;
        *) print_message $RED "无效选择，请输入 0-8 之间的数字" ;;
    esac
    return 1
}

# 处理系统监控选择
handle_monitor_choice() {
    local choice=$1
    
    case $choice in
        1) execute_command "./manage.sh status" "查看服务状态" ;;
        2) execute_command "./manage.sh logs frontend 50" "查看前端日志" ;;
        3) execute_command "./manage.sh logs backend 50" "查看后端日志" ;;
        4) monitor_logs "frontend" "前端" ;;
        5) monitor_logs "backend" "后端" ;;
        6) return 0 ;;
        0) exit 0 ;;
        *) print_message $RED "无效选择，请输入 0-6 之间的数字" ;;
    esac
    return 1
}

# 处理系统维护选择
handle_maintenance_choice() {
    local choice=$1
    
    case $choice in
        1) execute_command "./manage.sh check" "检查系统依赖" ;;
        2) execute_command "./manage.sh cleanup" "清理僵尸进程" ;;
        3) execute_command "./manage.sh backup" "备份项目" ;;
        4) execute_command "./manage.sh help" "查看帮助" ;;
        5) return 0 ;;
        0) exit 0 ;;
        *) print_message $RED "无效选择，请输入 0-5 之间的数字" ;;
    esac
    return 1
}

# 处理快速访问选择
handle_access_choice() {
    local choice=$1
    
    case $choice in
        1) open_browser "http://10.10.10.207:8080" "前端界面" ;;
        2) open_browser "http://10.10.10.207:8000/docs" "API文档" ;;
        3) open_browser "http://10.10.10.207:8081" "数据库管理" ;;
        4) return 0 ;;
        0) exit 0 ;;
        *) print_message $RED "无效选择，请输入 0-4 之间的数字" ;;
    esac
    return 1
}

# 处理主菜单选择
handle_main_choice() {
    local choice=$1
    
    case $choice in
        1)
            while true; do
                show_service_menu
                echo -n -e "${WHITE}请选择 (0-8): ${NC}"
                read sub_choice
                if handle_service_choice "$sub_choice"; then
                    break
                fi
            done
            ;;
        2)
            while true; do
                show_monitor_menu
                echo -n -e "${WHITE}请选择 (0-6): ${NC}"
                read sub_choice
                if handle_monitor_choice "$sub_choice"; then
                    break
                fi
            done
            ;;
        3)
            while true; do
                show_maintenance_menu
                echo -n -e "${WHITE}请选择 (0-5): ${NC}"
                read sub_choice
                if handle_maintenance_choice "$sub_choice"; then
                    break
                fi
            done
            ;;
        4)
            while true; do
                show_access_menu
                echo -n -e "${WHITE}请选择 (0-4): ${NC}"
                read sub_choice
                if handle_access_choice "$sub_choice"; then
                    break
                fi
            done
            ;;
        5) show_project_status ;;
        0) 
            print_message $GREEN "感谢使用！再见！"
            exit 0
            ;;
        *)
            print_message $RED "无效选择，请输入 0-5 之间的数字"
            echo ""
            print_message $YELLOW "按任意键继续..."
            read -n 1 -s
            ;;
    esac
}

# 主循环
main() {
    # 检查脚本目录
    if [ ! -f "$SCRIPT_DIR/manage.sh" ]; then
        print_message $RED "错误: 找不到 manage.sh 脚本"
        print_message $YELLOW "请确保在正确的目录下运行此脚本"
        exit 1
    fi
    
    # 主循环
    while true; do
        show_main_menu
        echo -n -e "${WHITE}请选择 (0-5): ${NC}"
        read choice
        
        # 验证输入是否为数字
        if [[ "$choice" =~ ^[0-9]+$ ]]; then
            handle_main_choice "$choice"
        else
            print_message $RED "请输入有效的数字 (0-5)"
            echo ""
            print_message $YELLOW "按任意键继续..."
            read -n 1 -s
        fi
    done
}

# 信号处理
trap 'print_message $YELLOW "\n程序被中断，正在退出..."; exit 0' INT TERM

# 脚本入口
main "$@"