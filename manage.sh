#!/bin/bash

# 按摩推拿连锁门店管理系统 - 启动管理脚本
# 作者: AI Assistant
# 版本: v1.0
# 日期: 2025-01-19

# 配置变量
PROJECT_ROOT="/root/vip"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
BACKEND_DIR="$PROJECT_ROOT/backend"
LOGS_DIR="$PROJECT_ROOT/logs"
PID_DIR="$PROJECT_ROOT/pids"

# 端口配置
FRONTEND_PORT=8080
BACKEND_PORT=8000

# 进程名称
FRONTEND_NAME="frontend"
BACKEND_NAME="backend"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 创建必要的目录
create_dirs() {
    mkdir -p "$LOGS_DIR"
    mkdir -p "$PID_DIR"
}

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 打印标题
print_title() {
    echo -e "${CYAN}"
    echo "=================================================="
    echo "    按摩推拿连锁门店管理系统 - 管理脚本"
    echo "=================================================="
    echo -e "${NC}"
}

# 检查进程是否运行
is_running() {
    local service=$1
    local pid_file="$PID_DIR/${service}.pid"

    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p "$pid" > /dev/null 2>&1; then
            # 进一步检查进程是否是我们启动的服务
            if [ "$service" = "$FRONTEND_NAME" ]; then
                if ps -p "$pid" -o cmd= | grep -q "npm run dev"; then
                    return 0
                fi
            elif [ "$service" = "$BACKEND_NAME" ]; then
                if ps -p "$pid" -o cmd= | grep -q "uvicorn app.main:app"; then
                    return 0
                fi
            fi
        fi
        # PID文件存在但进程不匹配，清理PID文件
        rm -f "$pid_file"
        return 1
    fi
    return 1
}

# 获取进程PID
get_pid() {
    local service=$1
    local pid_file="$PID_DIR/${service}.pid"

    if [ -f "$pid_file" ]; then
        cat "$pid_file"
    else
        echo ""
    fi
}

# 检查端口占用并清理
check_and_kill_port() {
    local port=$1
    local service_name=$2

    print_message $YELLOW "检查端口 $port 是否被占用..."

    # 查找占用端口的进程
    local pids=$(lsof -ti:$port 2>/dev/null)

    if [ -n "$pids" ]; then
        print_message $YELLOW "发现端口 $port 被以下进程占用:"
        local killable_pids=""
        local protected_pids=""

        for pid in $pids; do
            local cmd=$(ps -p $pid -o cmd= 2>/dev/null || echo "未知进程")
            print_message $YELLOW "  PID: $pid, 命令: $cmd"

            # 检查是否是受保护的进程（SSH相关）
            if echo "$cmd" | grep -qE "(ssh|sshd|tunnel|port.*forward)" || \
               echo "$cmd" | grep -qE "(-L|LocalForward|RemoteForward)" || \
               ps -p $pid -o comm= 2>/dev/null | grep -qE "^(ssh|sshd)$"; then
                print_message $RED "⚠️  检测到SSH相关进程，跳过: PID $pid"
                protected_pids="$protected_pids $pid"
            else
                killable_pids="$killable_pids $pid"
            fi
        done

        if [ -n "$killable_pids" ]; then
            print_message $BLUE "正在结束可清理的进程..."
            for pid in $killable_pids; do
                local cmd=$(ps -p $pid -o cmd= 2>/dev/null || echo "未知进程")
                print_message $BLUE "结束进程: PID $pid ($cmd)"
                kill -TERM $pid 2>/dev/null
                sleep 1
                # 如果进程还在运行，强制杀死
                if kill -0 $pid 2>/dev/null; then
                    print_message $YELLOW "强制结束进程 $pid"
                    kill -KILL $pid 2>/dev/null
                fi
            done

            # 等待端口释放
            local count=0
            while [ $count -lt 5 ]; do
                local remaining_pids=$(lsof -ti:$port 2>/dev/null)
                if [ -z "$remaining_pids" ]; then
                    print_message $GREEN "✅ 端口 $port 已释放"
                    return 0
                fi
                sleep 1
                count=$((count + 1))
            done
        fi

        # 检查是否还有进程占用端口
        local remaining_pids=$(lsof -ti:$port 2>/dev/null)
        if [ -n "$remaining_pids" ]; then
            if [ -n "$protected_pids" ]; then
                print_message $RED "❌ 端口 $port 被SSH相关进程占用，无法清理"
                print_message $YELLOW "请使用不同的端口或手动处理SSH端口转发"
                return 1
            else
                print_message $RED "❌ 端口 $port 仍被占用，可能需要手动处理"
                return 1
            fi
        else
            print_message $GREEN "✅ 端口 $port 已释放"
            return 0
        fi
    else
        print_message $GREEN "✅ 端口 $port 可用"
        return 0
    fi
}

# 启动前端
start_frontend() {
    print_message $BLUE "正在启动前端服务..."

    if is_running $FRONTEND_NAME; then
        print_message $YELLOW "前端服务已经在运行中"
        return 0
    fi

    # 检查并清理端口占用
    if ! check_and_kill_port $FRONTEND_PORT "前端"; then
        print_message $RED "❌ 无法清理端口 $FRONTEND_PORT，前端启动失败"
        return 1
    fi

    cd "$FRONTEND_DIR"

    # 检查依赖
    if [ ! -d "node_modules" ]; then
        print_message $YELLOW "正在安装前端依赖..."
        npm install
    fi

    # 启动前端服务
    nohup npm run dev > "$LOGS_DIR/frontend.log" 2>&1 &
    local pid=$!
    echo $pid > "$PID_DIR/${FRONTEND_NAME}.pid"

    # 记录启动信息
    print_message $BLUE "前端服务启动中... (PID: $pid)"
    
    # 等待服务启动
    print_message $YELLOW "等待前端服务启动..."
    local count=0
    while [ $count -lt 15 ]; do
        if is_running $FRONTEND_NAME; then
            print_message $GREEN "✅ 前端服务启动成功 (PID: $pid, 端口: $FRONTEND_PORT)"
            return 0
        fi
        sleep 2
        count=$((count + 1))
        echo -n "."
    done

    echo ""
    print_message $RED "❌ 前端服务启动失败或超时"
    print_message $YELLOW "请查看日志: ./manage.sh logs frontend"
    return 1
}

# 启动后端
start_backend() {
    print_message $BLUE "正在启动后端服务..."

    if is_running $BACKEND_NAME; then
        print_message $YELLOW "后端服务已经在运行中"
        return 0
    fi

    # 检查并清理端口占用
    if ! check_and_kill_port $BACKEND_PORT "后端"; then
        print_message $RED "❌ 无法清理端口 $BACKEND_PORT，后端启动失败"
        return 1
    fi

    cd "$BACKEND_DIR"
    
    # 检查虚拟环境
    if [ ! -d "venv" ]; then
        print_message $YELLOW "正在创建Python虚拟环境..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 检查依赖
    if [ ! -f "venv/installed" ]; then
        print_message $YELLOW "正在安装后端依赖..."
        pip install -r requirements.txt
        touch venv/installed
    fi
    
    # 启动后端服务 (在虚拟环境中)
    nohup bash -c "source venv/bin/activate && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload" > "$LOGS_DIR/backend.log" 2>&1 &
    local pid=$!
    echo $pid > "$PID_DIR/${BACKEND_NAME}.pid"

    # 记录启动信息
    print_message $BLUE "后端服务启动中... (PID: $pid)"
    
    # 等待服务启动
    print_message $YELLOW "等待后端服务启动..."
    local count=0
    while [ $count -lt 20 ]; do
        if is_running $BACKEND_NAME; then
            print_message $GREEN "✅ 后端服务启动成功 (PID: $pid, 端口: $BACKEND_PORT)"
            return 0
        fi
        sleep 2
        count=$((count + 1))
        echo -n "."
    done

    echo ""
    print_message $RED "❌ 后端服务启动失败或超时"
    print_message $YELLOW "请查看日志: ./manage.sh logs backend"
    return 1
}

# 停止服务
stop_service() {
    local service=$1
    local service_name=$2
    
    print_message $BLUE "正在停止${service_name}服务..."
    
    if ! is_running $service; then
        print_message $YELLOW "${service_name}服务未运行"
        return 0
    fi
    
    local pid=$(get_pid $service)
    local pid_file="$PID_DIR/${service}.pid"
    
    # 尝试优雅停止
    kill $pid 2>/dev/null
    
    # 等待进程结束
    local count=0
    while [ $count -lt 10 ] && ps -p $pid > /dev/null 2>&1; do
        sleep 1
        count=$((count + 1))
    done
    
    # 如果还在运行，强制杀死
    if ps -p $pid > /dev/null 2>&1; then
        kill -9 $pid 2>/dev/null
        sleep 1
    fi
    
    # 清理PID文件
    rm -f "$pid_file"
    
    if ! ps -p $pid > /dev/null 2>&1; then
        print_message $GREEN "✅ ${service_name}服务已停止"
    else
        print_message $RED "❌ ${service_name}服务停止失败"
        return 1
    fi
}

# 重启服务
restart_service() {
    local service=$1
    local service_name=$2
    
    print_message $BLUE "正在重启${service_name}服务..."
    
    stop_service $service "$service_name"
    sleep 2
    
    if [ "$service" = "$FRONTEND_NAME" ]; then
        start_frontend
    elif [ "$service" = "$BACKEND_NAME" ]; then
        start_backend
    fi
}

# 显示服务状态
show_status() {
    print_message $CYAN "\n=== 服务状态 ==="
    
    # 前端状态
    if is_running $FRONTEND_NAME; then
        local frontend_pid=$(get_pid $FRONTEND_NAME)
        print_message $GREEN "前端服务: ✅ 运行中 (PID: $frontend_pid, 端口: $FRONTEND_PORT)"
        print_message $GREEN "前端地址: http://10.10.10.207:$FRONTEND_PORT"
    else
        print_message $RED "前端服务: ❌ 未运行"
    fi
    
    # 后端状态
    if is_running $BACKEND_NAME; then
        local backend_pid=$(get_pid $BACKEND_NAME)
        print_message $GREEN "后端服务: ✅ 运行中 (PID: $backend_pid, 端口: $BACKEND_PORT)"
        print_message $GREEN "后端地址: http://10.10.10.207:$BACKEND_PORT"
        print_message $GREEN "API文档: http://10.10.10.207:$BACKEND_PORT/docs"
    else
        print_message $RED "后端服务: ❌ 未运行"
    fi
    
    echo ""
}

# 查看日志
view_logs() {
    local service=$1
    local lines=${2:-50}
    
    case $service in
        "frontend"|"前端")
            if [ -f "$LOGS_DIR/frontend.log" ]; then
                print_message $CYAN "=== 前端日志 (最近 $lines 行) ==="
                tail -n $lines "$LOGS_DIR/frontend.log"
            else
                print_message $YELLOW "前端日志文件不存在"
            fi
            ;;
        "backend"|"后端")
            if [ -f "$LOGS_DIR/backend.log" ]; then
                print_message $CYAN "=== 后端日志 (最近 $lines 行) ==="
                tail -n $lines "$LOGS_DIR/backend.log"
            else
                print_message $YELLOW "后端日志文件不存在"
            fi
            ;;
        "all"|"全部")
            view_logs "frontend" $lines
            echo ""
            view_logs "backend" $lines
            ;;
        *)
            print_message $RED "无效的服务名称: $service"
            print_message $YELLOW "可用选项: frontend/前端, backend/后端, all/全部"
            ;;
    esac
}

# 实时查看日志
tail_logs() {
    local service=$1
    
    case $service in
        "frontend"|"前端")
            if [ -f "$LOGS_DIR/frontend.log" ]; then
                print_message $CYAN "=== 实时查看前端日志 (按 Ctrl+C 退出) ==="
                tail -f "$LOGS_DIR/frontend.log"
            else
                print_message $YELLOW "前端日志文件不存在"
            fi
            ;;
        "backend"|"后端")
            if [ -f "$LOGS_DIR/backend.log" ]; then
                print_message $CYAN "=== 实时查看后端日志 (按 Ctrl+C 退出) ==="
                tail -f "$LOGS_DIR/backend.log"
            else
                print_message $YELLOW "后端日志文件不存在"
            fi
            ;;
        *)
            print_message $RED "无效的服务名称: $service"
            print_message $YELLOW "可用选项: frontend/前端, backend/后端"
            ;;
    esac
}

# 显示帮助信息
show_help() {
    print_title
    echo -e "${YELLOW}使用方法:${NC}"
    echo "  $0 [命令] [选项]"
    echo ""
    echo -e "${YELLOW}可用命令:${NC}"
    echo "  start [all|frontend|backend]     启动服务"
    echo "  stop [all|frontend|backend]      停止服务"
    echo "  restart [all|frontend|backend]   重启服务"
    echo "  status                           显示服务状态"
    echo "  logs [frontend|backend|all] [行数]  查看日志"
    echo "  tail [frontend|backend]          实时查看日志"
    echo "  cleanup                          清理僵尸进程"
    echo "  check                            检查系统依赖"
    echo "  backup                           备份项目"
    echo "  help                             显示帮助信息"
    echo ""
    echo -e "${YELLOW}示例:${NC}"
    echo "  $0 start all                     启动前后端服务"
    echo "  $0 start frontend                只启动前端服务"
    echo "  $0 restart backend               重启后端服务"
    echo "  $0 logs frontend 100             查看前端最近100行日志"
    echo "  $0 tail backend                  实时查看后端日志"
    echo ""
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        return 0
    else
        return 1
    fi
}

# 清理僵尸进程
cleanup_zombies() {
    print_message $BLUE "正在清理僵尸进程..."

    # 清理可能的僵尸进程
    pkill -f "npm run dev" 2>/dev/null || true
    pkill -f "uvicorn app.main:app" 2>/dev/null || true
    pkill -f "uvicorn" 2>/dev/null || true

    # 清理PID文件
    rm -f "$PID_DIR"/*.pid

    print_message $GREEN "✅ 清理完成"
}

# 检查系统依赖
check_dependencies() {
    print_message $BLUE "正在检查系统依赖..."

    local missing_deps=()

    # 检查Node.js
    if ! command -v node >/dev/null 2>&1; then
        missing_deps+=("Node.js")
    fi

    # 检查npm
    if ! command -v npm >/dev/null 2>&1; then
        missing_deps+=("npm")
    fi

    # 检查Python
    if ! command -v python3 >/dev/null 2>&1; then
        missing_deps+=("Python3")
    fi

    # 检查pip
    if ! command -v pip3 >/dev/null 2>&1; then
        missing_deps+=("pip3")
    fi

    if [ ${#missing_deps[@]} -eq 0 ]; then
        print_message $GREEN "✅ 系统依赖检查通过"
        return 0
    else
        print_message $RED "❌ 缺少以下依赖: ${missing_deps[*]}"
        return 1
    fi
}

# 主函数
main() {
    # 创建必要目录
    create_dirs

    # 检查参数
    if [ $# -eq 0 ]; then
        show_help
        show_status
        return 0
    fi
    
    local command=$1
    local target=${2:-"all"}
    
    case $command in
        "start"|"启动")
            case $target in
                "all"|"全部")
                    start_backend
                    start_frontend
                    ;;
                "frontend"|"前端")
                    start_frontend
                    ;;
                "backend"|"后端")
                    start_backend
                    ;;
                *)
                    print_message $RED "无效的目标: $target"
                    print_message $YELLOW "可用选项: all, frontend, backend"
                    return 1
                    ;;
            esac
            show_status
            ;;
        "stop"|"停止")
            case $target in
                "all"|"全部")
                    stop_service $FRONTEND_NAME "前端"
                    stop_service $BACKEND_NAME "后端"
                    ;;
                "frontend"|"前端")
                    stop_service $FRONTEND_NAME "前端"
                    ;;
                "backend"|"后端")
                    stop_service $BACKEND_NAME "后端"
                    ;;
                *)
                    print_message $RED "无效的目标: $target"
                    print_message $YELLOW "可用选项: all, frontend, backend"
                    return 1
                    ;;
            esac
            show_status
            ;;
        "restart"|"重启")
            case $target in
                "all"|"全部")
                    restart_service $BACKEND_NAME "后端"
                    restart_service $FRONTEND_NAME "前端"
                    ;;
                "frontend"|"前端")
                    restart_service $FRONTEND_NAME "前端"
                    ;;
                "backend"|"后端")
                    restart_service $BACKEND_NAME "后端"
                    ;;
                *)
                    print_message $RED "无效的目标: $target"
                    print_message $YELLOW "可用选项: all, frontend, backend"
                    return 1
                    ;;
            esac
            show_status
            ;;
        "status"|"状态")
            show_status
            ;;
        "logs"|"日志")
            local lines=${3:-50}
            view_logs $target $lines
            ;;
        "tail"|"实时日志")
            tail_logs $target
            ;;
        "cleanup"|"清理")
            cleanup_zombies
            show_status
            ;;
        "check"|"检查")
            check_dependencies
            ;;
        "backup"|"备份")
            backup_project
            ;;
        "help"|"帮助"|"-h"|"--help")
            show_help
            ;;
        *)
            print_message $RED "未知命令: $command"
            show_help
            return 1
            ;;
    esac
}

# 备份项目
backup_project() {
    print_message $BLUE "正在备份项目..."

    local backup_name="vip_backup_$(date +%Y%m%d_%H%M%S).tar.gz"
    local backup_path="/root/$backup_name"

    cd /root
    tar -czf "$backup_path" vip/ \
        --exclude='vip/node_modules' \
        --exclude='vip/frontend/node_modules' \
        --exclude='vip/backend/__pycache__' \
        --exclude='vip/backend/venv' \
        --exclude='vip/logs' \
        --exclude='vip/pids' \
        2>/dev/null

    if [ -f "$backup_path" ]; then
        local size=$(ls -lh "$backup_path" | awk '{print $5}')
        print_message $GREEN "✅ 备份完成: $backup_path (大小: $size)"
    else
        print_message $RED "❌ 备份失败"
        return 1
    fi
}

# 脚本入口
main "$@"
